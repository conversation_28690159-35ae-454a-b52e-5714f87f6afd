#ifndef __IMAGE_BUFFER_MANAGER_HPP__
#define __IMAGE_BUFFER_MANAGER_HPP__

#include "image_buffer.hpp"
#include <memory>
#include <string>
#include <map>
#include <semaphore.h>

// 图像缓冲区管理器类
class ImageBufferManager {
public:
    // 获取单例实例
    static ImageBufferManager& getInstance();
    
    // 创建新的图像缓冲区
    bool createBuffer(const std::string& buffer_name, size_t buffer_size = 20);
    
    // 获取指定名称的图像缓冲区
    std::shared_ptr<model::ImageBuffer> getBuffer(const std::string& buffer_name);
    
    // 初始化共享内存
    bool initSharedMemory(const std::string& buffer_name, size_t buffer_size = 20);
    
    // 连接到已存在的共享内存
    bool connectToSharedMemory(const std::string& buffer_name);
    
    // 清理共享内存资源
    void cleanupSharedMemory(const std::string& buffer_name, bool is_creator = false);
    
    // 锁定共享缓冲区
    void lockBuffer(const std::string& buffer_name);
    
    // 解锁共享缓冲区
    void unlockBuffer(const std::string& buffer_name);
    
    // 将图像缓冲区数据写入共享内存
    bool writeBufferToSharedMemory(const std::string& buffer_name);
    
    // 从共享内存读取图像缓冲区数据
    bool readBufferFromSharedMemory(const std::string& buffer_name);
    
    // 清理所有资源
    void cleanup();
    
private:
    // 私有构造函数和析构函数（单例模式）
    ImageBufferManager() = default;
    ~ImageBufferManager();
    
    // 禁止拷贝和赋值
    ImageBufferManager(const ImageBufferManager&) = delete;
    ImageBufferManager& operator=(const ImageBufferManager&) = delete;
    
    // 共享内存资源结构
    struct SharedMemoryResource {
        std::shared_ptr<model::ImageBuffer> buffer;
        int fd;
        void* ptr;
        sem_t* mutex;
        size_t size;
        
        SharedMemoryResource() : buffer(nullptr), fd(-1), ptr(nullptr), mutex(nullptr), size(0) {}
    };
    
    // 存储所有缓冲区的映射
    std::map<std::string, SharedMemoryResource> m_resources;
};

#endif // __IMAGE_BUFFER_MANAGER_HPP__ 