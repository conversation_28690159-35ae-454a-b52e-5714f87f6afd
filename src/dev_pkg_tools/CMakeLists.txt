cmake_minimum_required(VERSION 3.8)
project(dev_pkg_tools)

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# find dependencies
find_package(ament_cmake REQUIRED)
# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(OpenCV REQUIRED)
find_package(ament_index_cpp REQUIRED)
find_package(std_msgs REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(Protobuf REQUIRED)

include_directories(
  ${Protobuf_INCLUDE_DIRS}
  ${CMAKE_CURRENT_SOURCE_DIR}/proto
)

# cmake sub directories
add_subdirectory(${CMAKE_CURRENT_LIST_DIR}/proto)

add_executable(video_publisher src/video_publisher.cpp)
ament_target_dependencies(video_publisher rclcpp sensor_msgs cv_bridge OpenCV)
install(TARGETS
  video_publisher
  DESTINATION lib/${PROJECT_NAME})

add_executable(video_publisher_2 src/video_publisher_2.cpp)
ament_target_dependencies(video_publisher_2 rclcpp sensor_msgs cv_bridge OpenCV)
install(TARGETS
  video_publisher_2
  DESTINATION lib/${PROJECT_NAME})

add_executable(img_publisher src/img_publisher.cpp)
ament_target_dependencies(img_publisher rclcpp sensor_msgs cv_bridge OpenCV)
install(TARGETS
  img_publisher
  DESTINATION lib/${PROJECT_NAME})

add_executable(obj_publisher src/obj_publisher.cpp)
target_link_libraries(obj_publisher
  yaml-cpp
  proto_interfaces
  ${Protobuf_LIBRARIES}
)
ament_target_dependencies(obj_publisher
  rclcpp
  std_msgs
)
install(TARGETS
  obj_publisher
  DESTINATION lib/${PROJECT_NAME})

add_executable(obj_subscriber src/obj_subscriber.cpp)
target_link_libraries(obj_subscriber
  proto_interfaces
  ${Protobuf_LIBRARIES}
)
ament_target_dependencies(obj_subscriber
  rclcpp
  std_msgs
)
install(TARGETS
  obj_subscriber
  DESTINATION lib/${PROJECT_NAME})


add_executable(img_obj_publisher src/img_obj_publisher.cpp)
target_link_libraries(img_obj_publisher
  yaml-cpp          # For YAML parsing
  proto_interfaces  # For Protobuf definitions
  ${Protobuf_LIBRARIES} # For Protobuf runtime
  # OpenCV and cv_bridge are header-only or linked via ament_target_dependencies
)
ament_target_dependencies(img_obj_publisher
  rclcpp
  sensor_msgs       # For Image msg
  std_msgs          # For UInt8MultiArray msg
  cv_bridge         # For image conversion
  OpenCV            # For imread (if not implicitly linked by cv_bridge)
  # ament_index_cpp # Uncomment if using find_package_share
)
install(TARGETS
  img_obj_publisher
  DESTINATION lib/${PROJECT_NAME})


add_library(img_obj_publisher_component SHARED src/img_obj_publisher_component.cpp)
target_link_libraries(img_obj_publisher_component
  yaml-cpp          # For YAML parsing
  proto_interfaces  # For Protobuf definitions
  ${Protobuf_LIBRARIES} # For Protobuf runtime
  # OpenCV and cv_bridge are header-only or linked via ament_target_dependencies
)
ament_target_dependencies(img_obj_publisher_component
  rclcpp
  rclcpp_components
  sensor_msgs       # For Image msg
  std_msgs          # For UInt8MultiArray msg
  cv_bridge         # For image conversion
  OpenCV            # For imread (if not implicitly linked by cv_bridge)
  # ament_index_cpp # Uncomment if using find_package_share
)
rclcpp_components_register_nodes(img_obj_publisher_component "Det2dWorker::ImageObjectPublisher")
install(TARGETS img_obj_publisher_component
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)


# Add the video publisher component
add_library(video_publisher_component SHARED
  src/video_publisher_component.cpp
)
# Specify dependencies
ament_target_dependencies(video_publisher_component
  rclcpp
  rclcpp_components
  sensor_msgs
  cv_bridge
  OpenCV
)
# Register the component
rclcpp_components_register_nodes(video_publisher_component
  "Dev2dTools::VideoPublisherComponent"
)
# Install the component library
install(TARGETS
  video_publisher_component
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# add_executable(find_shared_dir_test src/find_shared_dir_test.cpp)
# ament_target_dependencies(find_shared_dir_test 
#   rclcpp 
#   ament_index_cpp
# )
# install(TARGETS
#   find_shared_dir_test
#   DESTINATION lib/${PROJECT_NAME})

# Install launch directory for dev_pkg_tools
install(DIRECTORY launch
    DESTINATION share/${PROJECT_NAME}
)

install(DIRECTORY config
  DESTINATION share/${PROJECT_NAME}
)

if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
