#!/bin/bash
source /root/.bashrc
source /app/dev_ws_det2d/install/setup.bash
source /app/dev_ws_det2d/install/local_setup.bash

python /app/dev_ws_det2d/get_config_by_nacos.py

CONFIG_FILES=("/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_drv.yaml" 
              "/app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/config/config_drv.yaml"
              "/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_camera.yaml"
              "/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_lidar.yaml"
              "/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_group.cfg"
              "/app/dev_ws_det2d/install/dev_pkg_kafka/share/dev_pkg_kafka/config/config_group.cfg")      
for FILE in "${CONFIG_FILES[@]}"; do
  if [ ! -e "$FILE" ]; then
    echo "The file '$FILE' does not exist, please check and restart!"
    exit 1
  fi
  if [ ! -s "$FILE" ]; then
    echo "The file '$FILE' is empty, please check and restart!"
    exit 1
  fi
  echo "The file '$FILE' exists and is not empty."
done

cd /app/dev_ws_det2d/install/dev_pkg_kafka/share/dev_pkg_kafka/config
python ./to_json.py
echo "convert config_group.cfg to config_group.json successfully."
cd ~

python /app/dev_ws_det2d/set_params_to_pkg.py

ros2 launch dev_pkg_tools launch_all.py
