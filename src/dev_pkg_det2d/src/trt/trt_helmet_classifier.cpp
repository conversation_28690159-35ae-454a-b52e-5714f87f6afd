#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp"
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <string>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/opencv.hpp"
#include "trt_helmet_classifier.hpp"
#include "trt_preprocess.hpp"
#include "utils.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace classifier {

/*
    classification model的初始化相关内容。
    包括设置input/output bindings, 分配host/device的memory等
*/
void HelmetClassifier::setup(void const* data, size_t size) {
    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    m_helmetLabelSize = m_context->getBindingDimensions(1).d[1];
    // 头盔分类模型只有一个输出

    CUDA_CHECK(cudaStreamCreate(&m_stream));

    m_inputSize     = m_params->batch_size * m_params->img.c * m_params->img.h * m_params->img.w * sizeof(float);
    m_helmetOutputSize = m_context->getBindingDimensions(1).d[1] * m_params->batch_size * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_helmetOutputMemory[0], m_helmetOutputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_helmetOutputMemory[1], m_helmetOutputSize));

    // //创建m_bindings，之后再寻址就直接从这里找
    m_bindingsOverride[0] = m_inputMemory[1];
    m_bindingsOverride[1] = m_helmetOutputMemory[1];

    // // check class_domains
    // if (m_params->class_domains.empty()) {
    //     LOGE("ERROR: class_domains is empty! Program terminated");
    // }
}

void HelmetClassifier::reset_task(){}

bool HelmetClassifier::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    /*Preprocess -- 获取ImageNet mean, std (根据Python代码)*/
    float mean[]       = {0.485, 0.456, 0.406};
    float std[]        = {0.229, 0.224, 0.225};

    if (m_activedBatchSize > m_params->batch_size) {
        m_activedBatchSize = m_params->batch_size;
    }

    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i);
            return false;
        }
    }

    // 初始化letterbox相关变量
    preprocess::params_init(m_activedBatchSize);

    // 对每个批次进行处理
    for (int b = 0; b < m_params->batch_size; b++) {
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        // 每个通道在当前批次内的起始位置
        const int offset_ch0 = offset_batch;                  // R通道起始位置
        const int offset_ch1 = offset_batch + m_imgArea;      // G通道起始位置
        const int offset_ch2 = offset_batch + 2 * m_imgArea;  // B通道起始位置

        if (b < m_activedBatchSize) {
            /*Preprocess -- 实现letterbox处理，与Python代码一致*/
            int   input_w  = m_inputImages[b].cols;
            int   input_h  = m_inputImages[b].rows;
            int   target_w = m_params->img.w;
            int   target_h = m_params->img.h;
            float scale    = min(float(target_w)/input_w, float(target_h)/input_h);
            int   new_w    = int(input_w * scale);
            int   new_h    = int(input_h * scale);

            preprocess::warpaffine_init(input_h, input_w, target_h, target_w, b);

            // 创建目标大小的空白图像
            cv::Mat tar(target_h, target_w, CV_8UC3, cv::Scalar(0, 0, 0));
            cv::Mat resized_img;

            // 调整图像大小
            cv::resize(m_inputImages[b], resized_img, cv::Size(new_w, new_h), 0, 0, cv::INTER_LINEAR);

            // 计算居中位置
            int x = (target_w - new_w) / 2;
            int y = (target_h - new_h) / 2;

            cv::Rect roi(x, y, new_w, new_h);

            // 将调整大小的图像粘贴到中心
            cv::Mat roiOfTar = tar(roi);
            resized_img.copyTo(roiOfTar);

            // 转换为RGB并进行归一化处理，同时转换为NCHW格式
            for (int i = 0; i < target_h; i++) {
                for (int j = 0; j < target_w; j++) {
                    int index = i * target_w * 3 + j * 3;
                    // BGR转RGB并归一化
                    float r = tar.data[index + 2] / 255.0f;
                    float g = tar.data[index + 1] / 255.0f;
                    float b = tar.data[index + 0] / 255.0f;

                    // 应用ImageNet均值和标准差
                    m_inputMemory[0][offset_ch0 + i * target_w + j] = (r - mean[0]) / std[0];
                    m_inputMemory[0][offset_ch1 + i * target_w + j] = (g - mean[1]) / std[1];
                    m_inputMemory[0][offset_ch2 + i * target_w + j] = (b - mean[2]) / std[2];
                }
            }
        } else {
            // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
            for (int i = 0; i < m_params->img.h; i++) {
                for (int j = 0; j < m_params->img.w; j++) {
                    m_inputMemory[0][offset_ch0 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch1 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch2 + i * m_params->img.w + j] = 0;
                }
            }
        }
    }

    /*Preprocess -- 将host的数据移动到device上*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));

    return true;
}

bool HelmetClassifier::preprocess_gpu() {
    /*Preprocess -- 获取ImageNet mean, std (根据Python代码)*/
    float mean[]       = {0.485, 0.456, 0.406};
    float std[]        = {0.229, 0.224, 0.225};

    /*Preprocess -- 读取数据*/
    cv::Mat input_image;
    input_image = cv::imread(m_imagePath);
    if (input_image.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }

    /*Preprocess -- 使用GPU进行双线性插值, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(input_image, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w,
                                   mean, std, preprocess::tactics::GPU_BILINEAR);

    return true;
}

bool HelmetClassifier::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);

    /*Postprocess -- 将device上的数据移动到host上*/
    CUDA_CHECK(cudaMemcpyAsync(m_helmetOutputMemory[0], m_helmetOutputMemory[1], m_helmetOutputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    // 清空之前的结果
    m_batch_results.clear();
    m_batch_results.resize(m_activedBatchSize, {-1, 0.0f});

    // 数值稳定的softmax实现
    auto softmax = [](float* data, int size) {
        // 1. 找到最大值以避免指数溢出
        float maxVal = (size > 0) ? data[0] : 0.0f;
        for(int i = 1; i < size; ++i) {
            if(data[i] > maxVal) maxVal = data[i];
        }
        // 2. 计算指数并求和
        float sum = 0.0f;
        for(int i = 0; i < size; ++i) {
            data[i] = std::exp(data[i] - maxVal);
            sum += data[i];
        }
        // 3. 归一化以确保和为1
        if(sum > 0.0f) {  // 避免除以零
            for(int i = 0; i < size; ++i) {
                data[i] /= sum;
            }
        } else {
            // 处理边缘情况：如果所有值都是极小的负数
            float val = 1.0f / size;
            for(int i = 0; i < size; ++i) {
                data[i] = val;
            }
        }
    };

    for (int b = 0; b < m_params->batch_size; b++) {
        // 处理头盔分类输出
        float* helmet_output = m_helmetOutputMemory[0] + b * m_helmetLabelSize;
        softmax(helmet_output, m_helmetLabelSize);

        // 找到最大概率的头盔类别
        float max_helmet_prob = -FLT_MAX;
        int helmet_class = -1;
        for (int i = 0; i < m_helmetLabelSize; ++i) {
            LOGV("helmet_output[%d]: %f", i, helmet_output[i]);
            if (helmet_output[i] > max_helmet_prob) {
                max_helmet_prob = helmet_output[i];
                helmet_class = i;
            }
        }

        // 存储结果并输出
        if (b < m_activedBatchSize) {
            // 存储分类结果
            m_batch_results[b] = {helmet_class, max_helmet_prob};

            // 输出日志
            std::string helmet_name = HELMET_MAP.at(helmet_class);
            LOG("\tBatch %d - 头盔状态: %s", b, helmet_name.c_str());
            LOG("\tBatch %d - 头盔状态置信度: %.3f%%", b, max_helmet_prob * 100);
        }
    }

    return true;
}


bool HelmetClassifier::postprocess_gpu() {
    /*
        由于classification task的postprocess比较简单，所以CPU/GPU的处理这里用一样的
        对于像yolo这种detection model, postprocess会包含decode, nms这些处理。可以选择在CPU还是在GPU上跑
    */
    return postprocess_cpu();

}


bool HelmetClassifier::check_model() {
    return true;
}

void HelmetClassifier::inference() {
    if (m_params->dev == CPU) {
        preprocess_cpu();
    } else {
        preprocess_gpu();
    }

    {
        std::lock_guard<std::mutex> lock(m_enqueue_mutex);
        if (!m_context->enqueueV2((void**)m_bindingsOverride, m_stream, nullptr)){
            LOGE("Error happens during DNN inference part, program terminated");
        }
    }

    if (m_params->dev == CPU) {
        postprocess_cpu();
    } else {
        postprocess_gpu();
    }
}


shared_ptr<HelmetClassifier> make_helmet_classifier(
    std::string onnx_path, logger::Level level, model::Params params)
{
    return make_shared<HelmetClassifier>(onnx_path, level, params);
}

}; // namespace classifier

}; // namespace model
