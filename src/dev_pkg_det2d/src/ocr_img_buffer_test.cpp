#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "trt_worker.hpp"
#include "utils.hpp"
#include "image_buffer.hpp"
#include "image_buffer_manager.hpp"

#include <iostream>
#include <chrono>
#include <future>
#include <vector>

using namespace std;

int main(int argc, char const *argv[])
{
    // 获取图像缓冲区管理器实例
    auto& manager = ImageBufferManager::getInstance();
    
    // 默认缓冲区名称
    std::string buffer_name = "default";
    
    // 如果提供了命令行参数，使用指定的缓冲区名称
    if (argc > 1) {
        buffer_name = argv[1];
    }
    
    // 连接到共享内存
    if (!manager.connectToSharedMemory(buffer_name)) {
        std::cerr << "连接到共享内存失败，请确保图像缓冲区节点已启动" << std::endl;
        std::cerr << "请先运行: ros2 run dev_pkg_det2d img_buffer_load_test" << std::endl;
        return 1;
    }
    
    // 获取指定名称的图像缓冲区
    auto image_buffer = manager.getBuffer(buffer_name);
    if (!image_buffer) {
        std::cerr << "获取图像缓冲区失败: " << buffer_name << std::endl;
        return 1;
    }
    
    // 检查缓冲区是否有图像
    manager.lockBuffer(buffer_name);
    size_t buffer_size = image_buffer->size();
    manager.unlockBuffer(buffer_name);
    
    if (buffer_size == 0) {
        std::cerr << "图像缓冲区为空，请确保图像已加载" << std::endl;
        std::cerr << "请先运行: ros2 run dev_pkg_det2d img_buffer_load_test" << std::endl;
        manager.cleanupSharedMemory(buffer_name);
        return 1;
    }
    
    // 模型配置
    string onnxPath = "src/dev_pkg_det2d/models/onnx/ocr_batch1.onnx";
    auto level = logger::Level::VERB;
    auto params = model::Params();

    // 通用参数
    params.batch_size = 1;  // 批处理大小
    params.img = {48, 168, 3};
    params.task = model::task_type::OCR_TASK;
    params.dev = model::device::CPU;
    params.prec = model::precision::FP16;
    params.calib_data_file = "src/dev_pkg_det2d/calibration/calibration_list_custom_plates.txt";
    params.calib_table_file = "src/dev_pkg_det2d/calibration/calibration_ocr_batch1_dla_table.txt";
    
    // 分类参数
    // params.num_cls = 21;
    // std::vector<std::vector<int>>().swap(params.class_domains);
    // params.class_domains.push_back({0,1,2,3,4,5,6,7,8,9});
    // params.class_domains.push_back({10,11,12,13,14,15,16,17,18,19,20});
    
    // DLA参数
    params.use_dla = true;
    params.dla_core = 0;

    // 创建worker实例并设置图像缓冲区
    auto worker1 = thread::create_worker_with_buffer(onnxPath, level, params, image_buffer);
    
    LOG("图像缓冲区 %s 包含 %d 张图像", buffer_name.c_str(), buffer_size);
    
    // 预热模型（第一次运行通常较慢）
    LOG("Warm up model...");
    worker1->inference_all_from_buffer();

    // 测试同步批量推理
    LOG("Test sync batch only work1...");
    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    worker1->inference_all_from_buffer();
    std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();
    LOG("\tSync batch inference time: %d ms", 
        std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
    
    // 测试异步批量推理
    LOG("Test async batch only work1...");
    begin = std::chrono::steady_clock::now();
    auto future = worker1->async_inference_all_from_buffer();
    future.wait();
    end = std::chrono::steady_clock::now();
    LOG("\tAsync batch inference time: %d ms", 
        std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());

    LOG("Test completed");
    
    // 在退出前清理共享内存资源
    manager.cleanupSharedMemory(buffer_name, false);
    return 0;
}