#include <chrono>
#include <functional>
#include <memory>
#include <string>

#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"
#include "std_msgs/msg/header.hpp"

using namespace std::chrono_literals;

class ImagePublisher : public rclcpp::Node
{
public:
    ImagePublisher()
    : Node("image_publisher_node")
    {
        // 声明参数
        this->declare_parameter<std::string>("image_path", "/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_det2d/data/source/plate_det/suzhou_3840_2160CrossImage2.jpg");
        this->declare_parameter<std::vector<std::string>>("topics",
            {"/image_topic_1", "/image_topic_2", "/image_topic_3", "/image_topic_4"});

        // 获取参数
        std::string image_path;
        this->get_parameter("image_path", image_path);
        std::vector<std::string> topics;
        this->get_parameter("topics", topics);

        // 检查 topic 数量
        if (topics.size() != 4) {
            RCLCPP_ERROR(this->get_logger(), "需要指定恰好4个 topics 参数。当前数量: %zu", topics.size());
            rclcpp::shutdown();
            return;
        }

        // 读取图片
        cv::Mat image = cv::imread(image_path, cv::IMREAD_COLOR);
        if (image.empty()) {
            RCLCPP_ERROR(this->get_logger(), "无法加载图片: %s", image_path.c_str());
            rclcpp::shutdown();
            return;
        }

        // 将 OpenCV 图像转换为 sensor_msgs::msg::Image
        // 注意: cv_bridge 会处理内存，无需手动管理 data
        cv_bridge::CvImage cv_image;
        cv_image.header.frame_id = "camera_frame"; // 或其他合适的 frame_id
        cv_image.encoding = sensor_msgs::image_encodings::BGR8; // OpenCV 默认 BGR
        cv_image.image = image;

        image_msg_ = *cv_image.toImageMsg();
        RCLCPP_INFO(this->get_logger(), "成功加载图片: %s", image_path.c_str());

        // 创建四个发布者
        for (size_t i = 0; i < topics.size(); ++i) {
            publishers_[i] = this->create_publisher<sensor_msgs::msg::Image>(topics[i], 10);
            RCLCPP_INFO(this->get_logger(), "创建发布者: %s", topics[i].c_str());
        }

        // 创建定时器，频率 10Hz
        timer_ = this->create_wall_timer(
            100ms, std::bind(&ImagePublisher::timer_callback, this));
    }

private:
    void timer_callback()
    {
        // 更新消息头中的时间戳
        image_msg_.header.stamp = this->get_clock()->now();

        // 发布消息到所有 topic
        for (size_t i = 0; i < 4; ++i) {
             if (publishers_[i]) {
                publishers_[i]->publish(image_msg_);
             }
        }
        // RCLCPP_INFO(this->get_logger(), "发布图像到 4 个 topics"); // 频繁打印会影响性能，可以取消注释用于调试
    }

    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr publishers_[4];
    sensor_msgs::msg::Image image_msg_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ImagePublisher>();
    if (rclcpp::ok()) { // 检查节点是否成功初始化
        rclcpp::spin(node);
    }
    rclcpp::shutdown();
    return 0;
} 