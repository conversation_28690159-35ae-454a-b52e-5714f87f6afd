import os
from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch.actions import IncludeLaunchDescription
from launch.launch_description_sources import PythonLaunchDescriptionSource


def generate_launch_description():
   video_topic_node = IncludeLaunchDescription(
      PythonLaunchDescriptionSource([os.path.join(
         get_package_share_directory('dev_pkg_tools'), 'launch'),
         '/launch_video_topic.py'])
   )
   # drv_node = IncludeLaunchDescription(
   #    PythonLaunchDescriptionSource([os.path.join(
   #       get_package_share_directory('dev_pkg_drv'), 'launch'),
   #       '/launch_drv.py'])
   #    )
   # camera_toipic_node = IncludeLaunchDescription(
   #    PythonLaunchDescriptionSource([os.path.join(
   #       get_package_share_directory('dev_pkg_drv'), 'launch'),
   #       '/launch_camera_topic.py'])
   #    )
   det2d_node = IncludeLaunchDescription(
      PythonLaunchDescriptionSource([os.path.join(
         get_package_share_directory('dev_pkg_det2d'), 'launch'),
         '/launch_det2d.py'])
      )
   # kafka_node = IncludeLaunchDescription(
   #    PythonLaunchDescriptionSource([os.path.join(
   #       get_package_share_directory('dev_pkg_kafka'), 'launch'),
   #       '/launch_kafka.py'])
   #    )

   return LaunchDescription([
      video_topic_node,
      # drv_node,
      # camera_toipic_node,
      det2d_node
      # kafka_node
   ])