#include <iostream>

#include <string>
#include <vector>
#include "assert.h"

using namespace std;

class ImageNetLabels {
public:
  ImageNetLabels()
  {
    for( int i=0 ; i<1000; i++ )
    {
      string x = "NA";
      mLabels.push_back( x );
    }

    {
      mLabels[0]= "tench, Tinca tinca";
      mLabels[1]= "goldfish, Carassius auratus";
      mLabels[2]= "great white shark, white shark, man-eater, man-eating shark, Carcharodon carcharias";
      mLabels[3]= "tiger shark, Galeocerdo cuvieri";
      mLabels[4]= "hammerhead, hammerhead shark";
      mLabels[5]= "electric ray, crampfish, numbfish, torpedo";
      mLabels[6]= "stingray";
      mLabels[7]= "cock";
      mLabels[8]= "hen";
      mLabels[9]= "ostrich, Struthio camelus";
      mLabels[10]= "brambling, Fringilla montifringilla";
      mLabels[11]= "goldfinch, <PERSON>uelis carduelis";
      mLabels[12]= "house finch, linnet, Carpodacus mexicanus";
      mLabels[13]= "junco, snowbird";
      mLabels[14]= "indigo bunting, indigo finch, indigo bird, Passerina cyanea";
      mLabels[15]= "robin, American robin, Turdus migratorius";
      mLabels[16]= "bulbul";
      mLabels[17]= "jay";
      mLabels[18]= "magpie";
      mLabels[19]= "chickadee";
      mLabels[20]= "water ouzel, dipper";
      mLabels[21]= "kite";
      mLabels[22]= "bald eagle, American eagle, Haliaeetus leucocephalus";
      mLabels[23]= "vulture";
      mLabels[24]= "great grey owl, great gray owl, Strix nebulosa";
      mLabels[25]= "European fire salamander, Salamandra salamandra";
      mLabels[26]= "common newt, Triturus vulgaris";
      mLabels[27]= "eft";
      mLabels[28]= "spotted salamander, Ambystoma maculatum";
      mLabels[29]= "axolotl, mud puppy, Ambystoma mexicanum";
      mLabels[30]= "bullfrog, Rana catesbeiana";
      mLabels[31]= "tree frog, tree-frog";
      mLabels[32]= "tailed frog, bell toad, ribbed toad, tailed toad, Ascaphus trui";
      mLabels[33]= "loggerhead, loggerhead turtle, Caretta caretta";
      mLabels[34]= "leatherback turtle, leatherback, leathery turtle, Dermochelys coriacea";
      mLabels[35]= "mud turtle";
      mLabels[36]= "terrapin";
      mLabels[37]= "box turtle, box tortoise";
      mLabels[38]= "banded gecko";
      mLabels[39]= "common iguana, iguana, Iguana iguana";
      mLabels[40]= "American chameleon, anole, Anolis carolinensis";
      mLabels[41]= "whiptail, whiptail lizard";
      mLabels[42]= "agama";
      mLabels[43]= "frilled lizard, Chlamydosaurus kingi";
      mLabels[44]= "alligator lizard";
      mLabels[45]= "Gila monster, Heloderma suspectum";
      mLabels[46]= "green lizard, Lacerta viridis";
      mLabels[47]= "African chameleon, Chamaeleo chamaeleon";
      mLabels[48]= "Komodo dragon, Komodo lizard, dragon lizard, giant lizard, Varanus komodoensis";
      mLabels[49]= "African crocodile, Nile crocodile, Crocodylus niloticus";
      mLabels[50]= "American alligator, Alligator mississipiensis";
      mLabels[51]= "triceratops";
      mLabels[52]= "thunder snake, worm snake, Carphophis amoenus";
      mLabels[53]= "ringneck snake, ring-necked snake, ring snake";
      mLabels[54]= "hognose snake, puff adder, sand viper";
      mLabels[55]= "green snake, grass snake";
      mLabels[56]= "king snake, kingsnake";
      mLabels[57]= "garter snake, grass snake";
      mLabels[58]= "water snake";
      mLabels[59]= "vine snake";
      mLabels[60]= "night snake, Hypsiglena torquata";
      mLabels[61]= "boa constrictor, Constrictor constrictor";
      mLabels[62]= "rock python, rock snake, Python sebae";
      mLabels[63]= "Indian cobra, Naja naja";
      mLabels[64]= "green mamba";
      mLabels[65]= "sea snake";
      mLabels[66]= "horned viper, cerastes, sand viper, horned asp, Cerastes cornutus";
      mLabels[67]= "diamondback, diamondback rattlesnake, Crotalus adamanteus";
      mLabels[68]= "sidewinder, horned rattlesnake, Crotalus cerastes";
      mLabels[69]= "trilobite";
      mLabels[70]= "harvestman, daddy longlegs, Phalangium opilio";
      mLabels[71]= "scorpion";
      mLabels[72]= "black and gold garden spider, Argiope aurantia";
      mLabels[73]= "barn spider, Araneus cavaticus";
      mLabels[74]= "garden spider, Aranea diademata";
      mLabels[75]= "black widow, Latrodectus mactans";
      mLabels[76]= "tarantula";
      mLabels[77]= "wolf spider, hunting spider";
      mLabels[78]= "tick";
      mLabels[79]= "centipede";
      mLabels[80]= "black grouse";
      mLabels[81]= "ptarmigan";
      mLabels[82]= "ruffed grouse, partridge, Bonasa umbellus";
      mLabels[83]= "prairie chicken, prairie grouse, prairie fowl";
      mLabels[84]= "peacock";
      mLabels[85]= "quail";
      mLabels[86]= "partridge";
      mLabels[87]= "African grey, African gray, Psittacus erithacus";
      mLabels[88]= "macaw";
      mLabels[89]= "sulphur-crested cockatoo, Kakatoe galerita, Cacatua galerita";
      mLabels[90]= "lorikeet";
      mLabels[91]= "coucal";
      mLabels[92]= "bee eater";
      mLabels[93]= "hornbill";
      mLabels[94]= "hummingbird";
      mLabels[95]= "jacamar";
      mLabels[96]= "toucan";
      mLabels[97]= "drake";
      mLabels[98]= "red-breasted merganser, Mergus serrator";
      mLabels[99]= "goose";
      mLabels[100]= "black swan, Cygnus atratus";
      mLabels[101]= "tusker";
      mLabels[102]= "echidna, spiny anteater, anteater";
      mLabels[103]= "platypus, duckbill, duckbilled platypus, duck-billed platypus, Ornithorhynchus anatinus";
      mLabels[104]= "wallaby, brush kangaroo";
      mLabels[105]= "koala, koala bear, kangaroo bear, native bear, Phascolarctos cinereus";
      mLabels[106]= "wombat";
      mLabels[107]= "jellyfish";
      mLabels[108]= "sea anemone, anemone";
      mLabels[109]= "brain coral";
      mLabels[110]= "flatworm, platyhelminth";
      mLabels[111]= "nematode, nematode worm, roundworm";
      mLabels[112]= "conch";
      mLabels[113]= "snail";
      mLabels[114]= "slug";
      mLabels[115]= "sea slug, nudibranch";
      mLabels[116]= "chiton, coat-of-mail shell, sea cradle, polyplacophore";
      mLabels[117]= "chambered nautilus, pearly nautilus, nautilus";
      mLabels[118]= "Dungeness crab, Cancer magister";
      mLabels[119]= "rock crab, Cancer irroratus";
      mLabels[120]= "fiddler crab";
      mLabels[121]= "king crab, Alaska crab, Alaskan king crab, Alaska king crab, Paralithodes camtschatica";
      mLabels[122]= "American lobster, Northern lobster, Maine lobster, Homarus americanus";
      mLabels[123]= "spiny lobster, langouste, rock lobster, crawfish, crayfish, sea crawfish";
      mLabels[124]= "crayfish, crawfish, crawdad, crawdaddy";
      mLabels[125]= "hermit crab";
      mLabels[126]= "isopod";
      mLabels[127]= "white stork, Ciconia ciconia";
      mLabels[128]= "black stork, Ciconia nigra";
      mLabels[129]= "spoonbill";
      mLabels[130]= "flamingo";
      mLabels[131]= "little blue heron, Egretta caerulea";
      mLabels[132]= "American egret, great white heron, Egretta albus";
      mLabels[133]= "bittern";
      mLabels[134]= "crane";
      mLabels[135]= "limpkin, Aramus pictus";
      mLabels[136]= "European gallinule, Porphyrio porphyrio";
      mLabels[137]= "American coot, marsh hen, mud hen, water hen, Fulica americana";
      mLabels[138]= "bustard";
      mLabels[139]= "ruddy turnstone, Arenaria interpres";
      mLabels[140]= "red-backed sandpiper, dunlin, Erolia alpina";
      mLabels[141]= "redshank, Tringa totanus";
      mLabels[142]= "dowitcher";
      mLabels[143]= "oystercatcher, oyster catcher";
      mLabels[144]= "pelican";
      mLabels[145]= "king penguin, Aptenodytes patagonica";
      mLabels[146]= "albatross, mollymawk";
      mLabels[147]= "grey whale, gray whale, devilfish, Eschrichtius gibbosus, Eschrichtius robustus";
      mLabels[148]= "killer whale, killer, orca, grampus, sea wolf, Orcinus orca";
      mLabels[149]= "dugong, Dugong dugon";
      mLabels[150]= "sea lion";
      mLabels[151]= "Chihuahua";
      mLabels[152]= "Japanese spaniel";
      mLabels[153]= "Maltese dog, Maltese terrier, Maltese";
      mLabels[154]= "Pekinese, Pekingese, Peke";
      mLabels[155]= "Shih-Tzu";
      mLabels[156]= "Blenheim spaniel";
      mLabels[157]= "papillon";
      mLabels[158]= "toy terrier";
      mLabels[159]= "Rhodesian ridgeback";
      mLabels[160]= "Afghan hound, Afghan";
      mLabels[161]= "basset, basset hound";
      mLabels[162]= "beagle";
      mLabels[163]= "bloodhound, sleuthhound";
      mLabels[164]= "bluetick";
      mLabels[165]= "black-and-tan coonhound";
      mLabels[166]= "Walker hound, Walker foxhound";
      mLabels[167]= "English foxhound";
      mLabels[168]= "redbone";
      mLabels[169]= "borzoi, Russian wolfhound";
      mLabels[170]= "Irish wolfhound";
      mLabels[171]= "Italian greyhound";
      mLabels[172]= "whippet";
      mLabels[173]= "Ibizan hound, Ibizan Podenco";
      mLabels[174]= "Norwegian elkhound, elkhound";
      mLabels[175]= "otterhound, otter hound";
      mLabels[176]= "Saluki, gazelle hound";
      mLabels[177]= "Scottish deerhound, deerhound";
      mLabels[178]= "Weimaraner";
      mLabels[179]= "Staffordshire bullterrier, Staffordshire bull terrier";
      mLabels[180]= "American Staffordshire terrier, Staffordshire terrier, American pit bull terrier, pit bull terrier";
      mLabels[181]= "Bedlington terrier";
      mLabels[182]= "Border terrier";
      mLabels[183]= "Kerry blue terrier";
      mLabels[184]= "Irish terrier";
      mLabels[185]= "Norfolk terrier";
      mLabels[186]= "Norwich terrier";
      mLabels[187]= "Yorkshire terrier";
      mLabels[188]= "wire-haired fox terrier";
      mLabels[189]= "Lakeland terrier";
      mLabels[190]= "Sealyham terrier, Sealyham";
      mLabels[191]= "Airedale, Airedale terrier";
      mLabels[192]= "cairn, cairn terrier";
      mLabels[193]= "Australian terrier";
      mLabels[194]= "Dandie Dinmont, Dandie Dinmont terrier";
      mLabels[195]= "Boston bull, Boston terrier";
      mLabels[196]= "miniature schnauzer";
      mLabels[197]= "giant schnauzer";
      mLabels[198]= "standard schnauzer";
      mLabels[199]= "Scotch terrier, Scottish terrier, Scottie";
      mLabels[200]= "Tibetan terrier, chrysanthemum dog";
      mLabels[201]= "silky terrier, Sydney silky";
      mLabels[202]= "soft-coated wheaten terrier";
      mLabels[203]= "West Highland white terrier";
      mLabels[204]= "Lhasa, Lhasa apso";
      mLabels[205]= "flat-coated retriever";
      mLabels[206]= "curly-coated retriever";
      mLabels[207]= "golden retriever";
      mLabels[208]= "Labrador retriever";
      mLabels[209]= "Chesapeake Bay retriever";
      mLabels[210]= "German short-haired pointer";
      mLabels[211]= "vizsla, Hungarian pointer";
      mLabels[212]= "English setter";
      mLabels[213]= "Irish setter, red setter";
      mLabels[214]= "Gordon setter";
      mLabels[215]= "Brittany spaniel";
      mLabels[216]= "clumber, clumber spaniel";
      mLabels[217]= "English springer, English springer spaniel";
      mLabels[218]= "Welsh springer spaniel";
      mLabels[219]= "cocker spaniel, English cocker spaniel, cocker";
      mLabels[220]= "Sussex spaniel";
      mLabels[221]= "Irish water spaniel";
      mLabels[222]= "kuvasz";
      mLabels[223]= "schipperke";
      mLabels[224]= "groenendael";
      mLabels[225]= "malinois";
      mLabels[226]= "briard";
      mLabels[227]= "kelpie";
      mLabels[228]= "komondor";
      mLabels[229]= "Old English sheepdog, bobtail";
      mLabels[230]= "Shetland sheepdog, Shetland sheep dog, Shetland";
      mLabels[231]= "collie";
      mLabels[232]= "Border collie";
      mLabels[233]= "Bouvier des Flandres, Bouviers des Flandres";
      mLabels[234]= "Rottweiler";
      mLabels[235]= "German shepherd, German shepherd dog, German police dog, alsatian";
      mLabels[236]= "Doberman, Doberman pinscher";
      mLabels[237]= "miniature pinscher";
      mLabels[238]= "Greater Swiss Mountain dog";
      mLabels[239]= "Bernese mountain dog";
      mLabels[240]= "Appenzeller";
      mLabels[241]= "EntleBucher";
      mLabels[242]= "boxer";
      mLabels[243]= "bull mastiff";
      mLabels[244]= "Tibetan mastiff";
      mLabels[245]= "French bulldog";
      mLabels[246]= "Great Dane";
      mLabels[247]= "Saint Bernard, St Bernard";
      mLabels[248]= "Eskimo dog, husky";
      mLabels[249]= "malamute, malemute, Alaskan malamute";
      mLabels[250]= "Siberian husky";
      mLabels[251]= "dalmatian, coach dog, carriage dog";
      mLabels[252]= "affenpinscher, monkey pinscher, monkey dog";
      mLabels[253]= "basenji";
      mLabels[254]= "pug, pug-dog";
      mLabels[255]= "Leonberg";
      mLabels[256]= "Newfoundland, Newfoundland dog";
      mLabels[257]= "Great Pyrenees";
      mLabels[258]= "Samoyed, Samoyede";
      mLabels[259]= "Pomeranian";
      mLabels[260]= "chow, chow chow";
      mLabels[261]= "keeshond";
      mLabels[262]= "Brabancon griffon";
      mLabels[263]= "Pembroke, Pembroke Welsh corgi";
      mLabels[264]= "Cardigan, Cardigan Welsh corgi";
      mLabels[265]= "toy poodle";
      mLabels[266]= "miniature poodle";
      mLabels[267]= "standard poodle";
      mLabels[268]= "Mexican hairless";
      mLabels[269]= "timber wolf, grey wolf, gray wolf, Canis lupus";
      mLabels[270]= "white wolf, Arctic wolf, Canis lupus tundrarum";
      mLabels[271]= "red wolf, maned wolf, Canis rufus, Canis niger";
      mLabels[272]= "coyote, prairie wolf, brush wolf, Canis latrans";
      mLabels[273]= "dingo, warrigal, warragal, Canis dingo";
      mLabels[274]= "dhole, Cuon alpinus";
      mLabels[275]= "African hunting dog, hyena dog, Cape hunting dog, Lycaon pictus";
      mLabels[276]= "hyena, hyaena";
      mLabels[277]= "red fox, Vulpes vulpes";
      mLabels[278]= "kit fox, Vulpes macrotis";
      mLabels[279]= "Arctic fox, white fox, Alopex lagopus";
      mLabels[280]= "grey fox, gray fox, Urocyon cinereoargenteus";
      mLabels[281]= "tabby, tabby cat";
      mLabels[282]= "tiger cat";
      mLabels[283]= "Persian cat";
      mLabels[284]= "Siamese cat, Siamese";
      mLabels[285]= "Egyptian cat";
      mLabels[286]= "cougar, puma, catamount, mountain lion, painter, panther, Felis concolor";
      mLabels[287]= "lynx, catamount";
      mLabels[288]= "leopard, Panthera pardus";
      mLabels[289]= "snow leopard, ounce, Panthera uncia";
      mLabels[290]= "jaguar, panther, Panthera onca, Felis onca";
      mLabels[291]= "lion, king of beasts, Panthera leo";
      mLabels[292]= "tiger, Panthera tigris";
      mLabels[293]= "cheetah, chetah, Acinonyx jubatus";
      mLabels[294]= "brown bear, bruin, Ursus arctos";
      mLabels[295]= "American black bear, black bear, Ursus americanus, Euarctos americanus";
      mLabels[296]= "ice bear, polar bear, Ursus Maritimus, Thalarctos maritimus";
      mLabels[297]= "sloth bear, Melursus ursinus, Ursus ursinus";
      mLabels[298]= "mongoose";
      mLabels[299]= "meerkat, mierkat";
      mLabels[300]= "tiger beetle";
      mLabels[301]= "ladybug, ladybeetle, lady beetle, ladybird, ladybird beetle";
      mLabels[302]= "ground beetle, carabid beetle";
      mLabels[303]= "long-horned beetle, longicorn, longicorn beetle";
      mLabels[304]= "leaf beetle, chrysomelid";
      mLabels[305]= "dung beetle";
      mLabels[306]= "rhinoceros beetle";
      mLabels[307]= "weevil";
      mLabels[308]= "fly";
      mLabels[309]= "bee";
      mLabels[310]= "ant, emmet, pismire";
      mLabels[311]= "grasshopper, hopper";
      mLabels[312]= "cricket";
      mLabels[313]= "walking stick, walkingstick, stick insect";
      mLabels[314]= "cockroach, roach";
      mLabels[315]= "mantis, mantid";
      mLabels[316]= "cicada, cicala";
      mLabels[317]= "leafhopper";
      mLabels[318]= "lacewing, lacewing fly";
      mLabels[319]= "dragonfly, darning needle, devils darning needle, sewing needle, snake feeder, snake doctor, mosquito hawk, skeeter hawk";
      mLabels[320]= "damselfly";
      mLabels[321]= "admiral";
      mLabels[322]= "ringlet, ringlet butterfly";
      mLabels[323]= "monarch, monarch butterfly, milkweed butterfly, Danaus plexippus";
      mLabels[324]= "cabbage butterfly";
      mLabels[325]= "sulphur butterfly, sulfur butterfly";
      mLabels[326]= "lycaenid, lycaenid butterfly";
      mLabels[327]= "starfish, sea star";
      mLabels[328]= "sea urchin";
      mLabels[329]= "sea cucumber, holothurian";
      mLabels[330]= "wood rabbit, cottontail, cottontail rabbit";
      mLabels[331]= "hare";
      mLabels[332]= "Angora, Angora rabbit";
      mLabels[333]= "hamster";
      mLabels[334]= "porcupine, hedgehog";
      mLabels[335]= "fox squirrel, eastern fox squirrel, Sciurus niger";
      mLabels[336]= "marmot";
      mLabels[337]= "beaver";
      mLabels[338]= "guinea pig, Cavia cobaya";
      mLabels[339]= "sorrel";
      mLabels[340]= "zebra";
      mLabels[341]= "hog, pig, grunter, squealer, Sus scrofa";
      mLabels[342]= "wild boar, boar, Sus scrofa";
      mLabels[343]= "warthog";
      mLabels[344]= "hippopotamus, hippo, river horse, Hippopotamus amphibius";
      mLabels[345]= "ox";
      mLabels[346]= "water buffalo, water ox, Asiatic buffalo, Bubalus bubalis";
      mLabels[347]= "bison";
      mLabels[348]= "ram, tup";
      mLabels[349]= "bighorn, bighorn sheep, cimarron, Rocky Mountain bighorn, Rocky Mountain sheep, Ovis canadensis";
      mLabels[350]= "ibex, Capra ibex";
      mLabels[351]= "hartebeest";
      mLabels[352]= "impala, Aepyceros melampus";
      mLabels[353]= "gazelle";
      mLabels[354]= "Arabian camel, dromedary, Camelus dromedarius";
      mLabels[355]= "llama";
      mLabels[356]= "weasel";
      mLabels[357]= "mink";
      mLabels[358]= "polecat, fitch, foulmart, foumart, Mustela putorius";
      mLabels[359]= "black-footed ferret, ferret, Mustela nigripes";
      mLabels[360]= "otter";
      mLabels[361]= "skunk, polecat, wood pussy";
      mLabels[362]= "badger";
      mLabels[363]= "armadillo";
      mLabels[364]= "three-toed sloth, ai, Bradypus tridactylus";
      mLabels[365]= "orangutan, orang, orangutang, Pongo pygmaeus";
      mLabels[366]= "gorilla, Gorilla gorilla";
      mLabels[367]= "chimpanzee, chimp, Pan troglodytes";
      mLabels[368]= "gibbon, Hylobates lar";
      mLabels[369]= "siamang, Hylobates syndactylus, Symphalangus syndactylus";
      mLabels[370]= "guenon, guenon monkey";
      mLabels[371]= "patas, hussar monkey, Erythrocebus patas";
      mLabels[372]= "baboon";
      mLabels[373]= "macaque";
      mLabels[374]= "langur";
      mLabels[375]= "colobus, colobus monkey";
      mLabels[376]= "proboscis monkey, Nasalis larvatus";
      mLabels[377]= "marmoset";
      mLabels[378]= "capuchin, ringtail, Cebus capucinus";
      mLabels[379]= "howler monkey, howler";
      mLabels[380]= "titi, titi monkey";
      mLabels[381]= "spider monkey, Ateles geoffroyi";
      mLabels[382]= "squirrel monkey, Saimiri sciureus";
      mLabels[383]= "Madagascar cat, ring-tailed lemur, Lemur catta";
      mLabels[384]= "indri, indris, Indri indri, Indri brevicaudatus";
      mLabels[385]= "Indian elephant, Elephas maximus";
      mLabels[386]= "African elephant, Loxodonta africana";
      mLabels[387]= "lesser panda, red panda, panda, bear cat, cat bear, Ailurus fulgens";
      mLabels[388]= "giant panda, panda, panda bear, coon bear, Ailuropoda melanoleuca";
      mLabels[389]= "barracouta, snoek";
      mLabels[390]= "eel";
      mLabels[391]= "coho, cohoe, coho salmon, blue jack, silver salmon, Oncorhynchus kisutch";
      mLabels[392]= "rock beauty, Holocanthus tricolor";
      mLabels[393]= "anemone fish";
      mLabels[394]= "sturgeon";
      mLabels[395]= "gar, garfish, garpike, billfish, Lepisosteus osseus";
      mLabels[396]= "lionfish";
      mLabels[397]= "puffer, pufferfish, blowfish, globefish";
      mLabels[398]= "abacus";
      mLabels[399]= "abaya";
      mLabels[400]= "academic gown, academic robe, judges robe";
      mLabels[401]= "accordion, piano accordion, squeeze box";
      mLabels[402]= "acoustic guitar";
      mLabels[403]= "aircraft carrier, carrier, flattop, attack aircraft carrier";
      mLabels[404]= "airliner";
      mLabels[405]= "airship, dirigible";
      mLabels[406]= "altar";
      mLabels[407]= "ambulance";
      mLabels[408]= "amphibian, amphibious vehicle";
      mLabels[409]= "analog clock";
      mLabels[410]= "apiary, bee house";
      mLabels[411]= "apron";
      mLabels[412]= "ashcan, trash can, garbage can, wastebin, ash bin, ash-bin, ashbin, dustbin, trash barrel, trash bin";
      mLabels[413]= "assault rifle, assault gun";
      mLabels[414]= "backpack, back pack, knapsack, packsack, rucksack, haversack";
      mLabels[415]= "bakery, bakeshop, bakehouse";
      mLabels[416]= "balance beam, beam";
      mLabels[417]= "balloon";
      mLabels[418]= "ballpoint, ballpoint pen, ballpen, Biro";
      mLabels[419]= "Band Aid";
      mLabels[420]= "banjo";
      mLabels[421]= "bannister, banister, balustrade, balusters, handrail";
      mLabels[422]= "barbell";
      mLabels[423]= "barber chair";
      mLabels[424]= "barbershop";
      mLabels[425]= "barn";
      mLabels[426]= "barometer";
      mLabels[427]= "barrel, cask";
      mLabels[428]= "barrow, garden cart, lawn cart, wheelbarrow";
      mLabels[429]= "baseball";
      mLabels[430]= "basketball";
      mLabels[431]= "bassinet";
      mLabels[432]= "bassoon";
      mLabels[433]= "bathing cap, swimming cap";
      mLabels[434]= "bath towel";
      mLabels[435]= "bathtub, bathing tub, bath, tub";
      mLabels[436]= "beach wagon, station wagon, wagon, estate car, beach waggon, station waggon, waggon";
      mLabels[437]= "beacon, lighthouse, beacon light, pharos";
      mLabels[438]= "beaker";
      mLabels[439]= "bearskin, busby, shako";
      mLabels[440]= "beer bottle";
      mLabels[441]= "beer glass";
      mLabels[442]= "bell cote, bell cot";
      mLabels[443]= "bib";
      mLabels[444]= "bicycle-built-for-two, tandem bicycle, tandem";
      mLabels[445]= "bikini, two-piece";
      mLabels[446]= "binder, ring-binder";
      mLabels[447]= "binoculars, field glasses, opera glasses";
      mLabels[448]= "birdhouse";
      mLabels[449]= "boathouse";
      mLabels[450]= "bobsled, bobsleigh, bob";
      mLabels[451]= "bolo tie, bolo, bola tie, bola";
      mLabels[452]= "bonnet, poke bonnet";
      mLabels[453]= "bookcase";
      mLabels[454]= "bookshop, bookstore, bookstall";
      mLabels[455]= "bottlecap";
      mLabels[456]= "bow";
      mLabels[457]= "bow tie, bow-tie, bowtie";
      mLabels[458]= "brass, memorial tablet, plaque";
      mLabels[459]= "brassiere, bra, bandeau";
      mLabels[460]= "breakwater, groin, groyne, mole, bulwark, seawall, jetty";
      mLabels[461]= "breastplate, aegis, egis";
      mLabels[462]= "broom";
      mLabels[463]= "bucket, pail";
      mLabels[464]= "buckle";
      mLabels[465]= "bulletproof vest";
      mLabels[466]= "bullet train, bullet";
      mLabels[467]= "butcher shop, meat market";
      mLabels[468]= "cab, hack, taxi, taxicab";
      mLabels[469]= "caldron, cauldron";
      mLabels[470]= "candle, taper, wax light";
      mLabels[471]= "cannon";
      mLabels[472]= "canoe";
      mLabels[473]= "can opener, tin opener";
      mLabels[474]= "cardigan";
      mLabels[475]= "car mirror";
      mLabels[476]= "carousel, carrousel, merry-go-round, roundabout, whirligig";
      mLabels[477]= "carpenters kit, tool kit";
      mLabels[478]= "carton";
      mLabels[479]= "car wheel";
      mLabels[480]= "cash machine, cash dispenser, automated teller machine, automatic teller machine, automated teller, automatic teller, ATM";
      mLabels[481]= "cassette";
      mLabels[482]= "cassette player";
      mLabels[483]= "castle";
      mLabels[484]= "catamaran";
      mLabels[485]= "CD player";
      mLabels[486]= "cello, violoncello";
      mLabels[487]= "cellular telephone, cellular phone, cellphone, cell, mobile phone";
      mLabels[488]= "chain";
      mLabels[489]= "chainlink fence";
      mLabels[490]= "chain mail, ring mail, mail, chain armor, chain armour, ring armor, ring armour";
      mLabels[491]= "chain saw, chainsaw";
      mLabels[492]= "chest";
      mLabels[493]= "chiffonier, commode";
      mLabels[494]= "chime, bell, gong";
      mLabels[495]= "china cabinet, china closet";
      mLabels[496]= "Christmas stocking";
      mLabels[497]= "church, church building";
      mLabels[498]= "cinema, movie theater, movie theatre, movie house, picture palace";
      mLabels[499]= "cleaver, meat cleaver, chopper";
      mLabels[500]= "cliff dwelling";
      mLabels[501]= "cloak";
      mLabels[502]= "clog, geta, patten, sabot";
      mLabels[503]= "cocktail shaker";
      mLabels[504]= "coffee mug";
      mLabels[505]= "coffeepot";
      mLabels[506]= "coil, spiral, volute, whorl, helix";
      mLabels[507]= "combination lock";
      mLabels[508]= "computer keyboard, keypad";
      mLabels[509]= "confectionery, confectionary, candy store";
      mLabels[510]= "container ship, containership, container vessel";
      mLabels[511]= "convertible";
      mLabels[512]= "corkscrew, bottle screw";
      mLabels[513]= "cornet, horn, trumpet, trump";
      mLabels[514]= "cowboy boot";
      mLabels[515]= "cowboy hat, ten-gallon hat";
      mLabels[516]= "cradle";
      mLabels[517]= "crane";
      mLabels[518]= "crash helmet";
      mLabels[519]= "crate";
      mLabels[520]= "crib, cot";
      mLabels[521]= "Crock Pot";
      mLabels[522]= "croquet ball";
      mLabels[523]= "crutch";
      mLabels[524]= "cuirass";
      mLabels[525]= "dam, dike, dyke";
      mLabels[526]= "desk";
      mLabels[527]= "desktop computer";
      mLabels[528]= "dial telephone, dial phone";
      mLabels[529]= "diaper, nappy, napkin";
      mLabels[530]= "digital clock";
      mLabels[531]= "digital watch";
      mLabels[532]= "dining table, board";
      mLabels[533]= "dishrag, dishcloth";
      mLabels[534]= "dishwasher, dish washer, dishwashing machine";
      mLabels[535]= "disk brake, disc brake";
      mLabels[536]= "dock, dockage, docking facility";
      mLabels[537]= "dogsled, dog sled, dog sleigh";
      mLabels[538]= "dome";
      mLabels[539]= "doormat, welcome mat";
      mLabels[540]= "drilling platform, offshore rig";
      mLabels[541]= "drum, membranophone, tympan";
      mLabels[542]= "drumstick";
      mLabels[543]= "dumbbell";
      mLabels[544]= "Dutch oven";
      mLabels[545]= "electric fan, blower";
      mLabels[546]= "electric guitar";
      mLabels[547]= "electric locomotive";
      mLabels[548]= "entertainment center";
      mLabels[549]= "envelope";
      mLabels[550]= "espresso maker";
      mLabels[551]= "face powder";
      mLabels[552]= "feather boa, boa";
      mLabels[553]= "file, file cabinet, filing cabinet";
      mLabels[554]= "fireboat";
      mLabels[555]= "fire engine, fire truck";
      mLabels[556]= "fire screen, fireguard";
      mLabels[557]= "flagpole, flagstaff";
      mLabels[558]= "flute, transverse flute";
      mLabels[559]= "folding chair";
      mLabels[560]= "football helmet";
      mLabels[561]= "forklift";
      mLabels[562]= "fountain";
      mLabels[563]= "fountain pen";
      mLabels[564]= "four-poster";
      mLabels[565]= "freight car";
      mLabels[566]= "French horn, horn";
      mLabels[567]= "frying pan, frypan, skillet";
      mLabels[568]= "fur coat";
      mLabels[569]= "garbage truck, dustcart";
      mLabels[570]= "gasmask, respirator, gas helmet";
      mLabels[571]= "gas pump, gasoline pump, petrol pump, island dispenser";
      mLabels[572]= "goblet";
      mLabels[573]= "go-kart";
      mLabels[574]= "golf ball";
      mLabels[575]= "golfcart, golf cart";
      mLabels[576]= "gondola";
      mLabels[577]= "gong, tam-tam";
      mLabels[578]= "gown";
      mLabels[579]= "grand piano, grand";
      mLabels[580]= "greenhouse, nursery, glasshouse";
      mLabels[581]= "grille, radiator grille";
      mLabels[582]= "grocery store, grocery, food market, market";
      mLabels[583]= "guillotine";
      mLabels[584]= "hair slide";
      mLabels[585]= "hair spray";
      mLabels[586]= "half track";
      mLabels[587]= "hammer";
      mLabels[588]= "hamper";
      mLabels[589]= "hand blower, blow dryer, blow drier, hair dryer, hair drier";
      mLabels[590]= "hand-held computer, hand-held microcomputer";
      mLabels[591]= "handkerchief, hankie, hanky, hankey";
      mLabels[592]= "hard disc, hard disk, fixed disk";
      mLabels[593]= "harmonica, mouth organ, harp, mouth harp";
      mLabels[594]= "harp";
      mLabels[595]= "harvester, reaper";
      mLabels[596]= "hatchet";
      mLabels[597]= "holster";
      mLabels[598]= "home theater, home theatre";
      mLabels[599]= "honeycomb";
      mLabels[600]= "hook, claw";
      mLabels[601]= "hoopskirt, crinoline";
      mLabels[602]= "horizontal bar, high bar";
      mLabels[603]= "horse cart, horse-cart";
      mLabels[604]= "hourglass";
      mLabels[605]= "iPod";
      mLabels[606]= "iron, smoothing iron";
      mLabels[607]= "jack-o-lantern";
      mLabels[608]= "jean, blue jean, denim";
      mLabels[609]= "jeep, landrover";
      mLabels[610]= "jersey, T-shirt, tee shirt";
      mLabels[611]= "jigsaw puzzle";
      mLabels[612]= "jinrikisha, ricksha, rickshaw";
      mLabels[613]= "joystick";
      mLabels[614]= "kimono";
      mLabels[615]= "knee pad";
      mLabels[616]= "knot";
      mLabels[617]= "lab coat, laboratory coat";
      mLabels[618]= "ladle";
      mLabels[619]= "lampshade, lamp shade";
      mLabels[620]= "laptop, laptop computer";
      mLabels[621]= "lawn mower, mower";
      mLabels[622]= "lens cap, lens cover";
      mLabels[623]= "letter opener, paper knife, paperknife";
      mLabels[624]= "library";
      mLabels[625]= "lifeboat";
      mLabels[626]= "lighter, light, igniter, ignitor";
      mLabels[627]= "limousine, limo";
      mLabels[628]= "liner, ocean liner";
      mLabels[629]= "lipstick, lip rouge";
      mLabels[630]= "Loafer";
      mLabels[631]= "lotion";
      mLabels[632]= "loudspeaker, speaker, speaker unit, loudspeaker system, speaker system";
      mLabels[633]= "loupe, jewelers loupe";
      mLabels[634]= "lumbermill, sawmill";
      mLabels[635]= "magnetic compass";
      mLabels[636]= "mailbag, postbag";
      mLabels[637]= "mailbox, letter box";
      mLabels[638]= "maillot";
      mLabels[639]= "maillot, tank suit";
      mLabels[640]= "manhole cover";
      mLabels[641]= "maraca";
      mLabels[642]= "marimba, xylophone";
      mLabels[643]= "mask";
      mLabels[644]= "matchstick";
      mLabels[645]= "maypole";
      mLabels[646]= "maze, labyrinth";
      mLabels[647]= "measuring cup";
      mLabels[648]= "medicine chest, medicine cabinet";
      mLabels[649]= "megalith, megalithic structure";
      mLabels[650]= "microphone, mike";
      mLabels[651]= "microwave, microwave oven";
      mLabels[652]= "military uniform";
      mLabels[653]= "milk can";
      mLabels[654]= "minibus";
      mLabels[655]= "miniskirt, mini";
      mLabels[656]= "minivan";
      mLabels[657]= "missile";
      mLabels[658]= "mitten";
      mLabels[659]= "mixing bowl";
      mLabels[660]= "mobile home, manufactured home";
      mLabels[661]= "Model T";
      mLabels[662]= "modem";
      mLabels[663]= "monastery";
      mLabels[664]= "monitor";
      mLabels[665]= "moped";
      mLabels[666]= "mortar";
      mLabels[667]= "mortarboard";
      mLabels[668]= "mosque";
      mLabels[669]= "mosquito net";
      mLabels[670]= "motor scooter, scooter";
      mLabels[671]= "mountain bike, all-terrain bike, off-roader";
      mLabels[672]= "mountain tent";
      mLabels[673]= "mouse, computer mouse";
      mLabels[674]= "mousetrap";
      mLabels[675]= "moving van";
      mLabels[676]= "muzzle";
      mLabels[677]= "nail";
      mLabels[678]= "neck brace";
      mLabels[679]= "necklace";
      mLabels[680]= "nipple";
      mLabels[681]= "notebook, notebook computer";
      mLabels[682]= "obelisk";
      mLabels[683]= "oboe, hautboy, hautbois";
      mLabels[684]= "ocarina, sweet potato";
      mLabels[685]= "odometer, hodometer, mileometer, milometer";
      mLabels[686]= "oil filter";
      mLabels[687]= "organ, pipe organ";
      mLabels[688]= "oscilloscope, scope, cathode-ray oscilloscope, CRO";
      mLabels[689]= "overskirt";
      mLabels[690]= "oxcart";
      mLabels[691]= "oxygen mask";
      mLabels[692]= "packet";
      mLabels[693]= "paddle, boat paddle";
      mLabels[694]= "paddlewheel, paddle wheel";
      mLabels[695]= "padlock";
      mLabels[696]= "paintbrush";
      mLabels[697]= "pajama, pyjama, pjs, jammies";
      mLabels[698]= "palace";
      mLabels[699]= "panpipe, pandean pipe, syrinx";
      mLabels[700]= "paper towel";
      mLabels[701]= "parachute, chute";
      mLabels[702]= "parallel bars, bars";
      mLabels[703]= "park bench";
      mLabels[704]= "parking meter";
      mLabels[705]= "passenger car, coach, carriage";
      mLabels[706]= "patio, terrace";
      mLabels[707]= "pay-phone, pay-station";
      mLabels[708]= "pedestal, plinth, footstall";
      mLabels[709]= "pencil box, pencil case";
      mLabels[710]= "pencil sharpener";
      mLabels[711]= "perfume, essence";
      mLabels[712]= "Petri dish";
      mLabels[713]= "photocopier";
      mLabels[714]= "pick, plectrum, plectron";
      mLabels[715]= "pickelhaube";
      mLabels[716]= "picket fence, paling";
      mLabels[717]= "pickup, pickup truck";
      mLabels[718]= "pier";
      mLabels[719]= "piggy bank, penny bank";
      mLabels[720]= "pill bottle";
      mLabels[721]= "pillow";
      mLabels[722]= "ping-pong ball";
      mLabels[723]= "pinwheel";
      mLabels[724]= "pirate, pirate ship";
      mLabels[725]= "pitcher, ewer";
      mLabels[726]= "plane, carpenters plane, woodworking plane";
      mLabels[727]= "planetarium";
      mLabels[728]= "plastic bag";
      mLabels[729]= "plate rack";
      mLabels[730]= "plow, plough";
      mLabels[731]= "plunger, plumbers helper";
      mLabels[732]= "Polaroid camera, Polaroid Land camera";
      mLabels[733]= "pole";
      mLabels[734]= "police van, police wagon, paddy wagon, patrol wagon, wagon, black Maria";
      mLabels[735]= "poncho";
      mLabels[736]= "pool table, billiard table, snooker table";
      mLabels[737]= "pop bottle, soda bottle";
      mLabels[738]= "pot, flowerpot";
      mLabels[739]= "potters wheel";
      mLabels[740]= "power drill";
      mLabels[741]= "prayer rug, prayer mat";
      mLabels[742]= "printer";
      mLabels[743]= "prison, prison house";
      mLabels[744]= "projectile, missile";
      mLabels[745]= "projector";
      mLabels[746]= "puck, hockey puck";
      mLabels[747]= "punching bag, punch bag, punching ball, punchball";
      mLabels[748]= "purse";
      mLabels[749]= "quill, quill pen";
      mLabels[750]= "quilt, comforter, comfort, puff";
      mLabels[751]= "racer, race car, racing car";
      mLabels[752]= "racket, racquet";
      mLabels[753]= "radiator";
      mLabels[754]= "radio, wireless";
      mLabels[755]= "radio telescope, radio reflector";
      mLabels[756]= "rain barrel";
      mLabels[757]= "recreational vehicle, RV, R.V.";
      mLabels[758]= "reel";
      mLabels[759]= "reflex camera";
      mLabels[760]= "refrigerator, icebox";
      mLabels[761]= "remote control, remote";
      mLabels[762]= "restaurant, eating house, eating place, eatery";
      mLabels[763]= "revolver, six-gun, six-shooter";
      mLabels[764]= "rifle";
      mLabels[765]= "rocking chair, rocker";
      mLabels[766]= "rotisserie";
      mLabels[767]= "rubber eraser, rubber, pencil eraser";
      mLabels[768]= "rugby ball";
      mLabels[769]= "rule, ruler";
      mLabels[770]= "running shoe";
      mLabels[771]= "safe";
      mLabels[772]= "safety pin";
      mLabels[773]= "saltshaker, salt shaker";
      mLabels[774]= "sandal";
      mLabels[775]= "sarong";
      mLabels[776]= "sax, saxophone";
      mLabels[777]= "scabbard";
      mLabels[778]= "scale, weighing machine";
      mLabels[779]= "school bus";
      mLabels[780]= "schooner";
      mLabels[781]= "scoreboard";
      mLabels[782]= "screen, CRT screen";
      mLabels[783]= "screw";
      mLabels[784]= "screwdriver";
      mLabels[785]= "seat belt, seatbelt";
      mLabels[786]= "sewing machine";
      mLabels[787]= "shield, buckler";
      mLabels[788]= "shoe shop, shoe-shop, shoe store";
      mLabels[789]= "shoji";
      mLabels[790]= "shopping basket";
      mLabels[791]= "shopping cart";
      mLabels[792]= "shovel";
      mLabels[793]= "shower cap";
      mLabels[794]= "shower curtain";
      mLabels[795]= "ski";
      mLabels[796]= "ski mask";
      mLabels[797]= "sleeping bag";
      mLabels[798]= "slide rule, slipstick";
      mLabels[799]= "sliding door";
      mLabels[800]= "slot, one-armed bandit";
      mLabels[801]= "snorkel";
      mLabels[802]= "snowmobile";
      mLabels[803]= "snowplow, snowplough";
      mLabels[804]= "soap dispenser";
      mLabels[805]= "soccer ball";
      mLabels[806]= "sock";
      mLabels[807]= "solar dish, solar collector, solar furnace";
      mLabels[808]= "sombrero";
      mLabels[809]= "soup bowl";
      mLabels[810]= "space bar";
      mLabels[811]= "space heater";
      mLabels[812]= "space shuttle";
      mLabels[813]= "spatula";
      mLabels[814]= "speedboat";
      mLabels[815]= "spider web, spiders web";
      mLabels[816]= "spindle";
      mLabels[817]= "sports car, sport car";
      mLabels[818]= "spotlight, spot";
      mLabels[819]= "stage";
      mLabels[820]= "steam locomotive";
      mLabels[821]= "steel arch bridge";
      mLabels[822]= "steel drum";
      mLabels[823]= "stethoscope";
      mLabels[824]= "stole";
      mLabels[825]= "stone wall";
      mLabels[826]= "stopwatch, stop watch";
      mLabels[827]= "stove";
      mLabels[828]= "strainer";
      mLabels[829]= "streetcar, tram, tramcar, trolley, trolley car";
      mLabels[830]= "stretcher";
      mLabels[831]= "studio couch, day bed";
      mLabels[832]= "stupa, tope";
      mLabels[833]= "submarine, pigboat, sub, U-boat";
      mLabels[834]= "suit, suit of clothes";
      mLabels[835]= "sundial";
      mLabels[836]= "sunglass";
      mLabels[837]= "sunglasses, dark glasses, shades";
      mLabels[838]= "sunscreen, sunblock, sun blocker";
      mLabels[839]= "suspension bridge";
      mLabels[840]= "swab, swob, mop";
      mLabels[841]= "sweatshirt";
      mLabels[842]= "swimming trunks, bathing trunks";
      mLabels[843]= "swing";
      mLabels[844]= "switch, electric switch, electrical switch";
      mLabels[845]= "syringe";
      mLabels[846]= "table lamp";
      mLabels[847]= "tank, army tank, armored combat vehicle, armoured combat vehicle";
      mLabels[848]= "tape player";
      mLabels[849]= "teapot";
      mLabels[850]= "teddy, teddy bear";
      mLabels[851]= "television, television system";
      mLabels[852]= "tennis ball";
      mLabels[853]= "thatch, thatched roof";
      mLabels[854]= "theater curtain, theatre curtain";
      mLabels[855]= "thimble";
      mLabels[856]= "thresher, thrasher, threshing machine";
      mLabels[857]= "throne";
      mLabels[858]= "tile roof";
      mLabels[859]= "toaster";
      mLabels[860]= "tobacco shop, tobacconist shop, tobacconist";
      mLabels[861]= "toilet seat";
      mLabels[862]= "torch";
      mLabels[863]= "totem pole";
      mLabels[864]= "tow truck, tow car, wrecker";
      mLabels[865]= "toyshop";
      mLabels[866]= "tractor";
      mLabels[867]= "trailer truck, tractor trailer, trucking rig, rig, articulated lorry, semi";
      mLabels[868]= "tray";
      mLabels[869]= "trench coat";
      mLabels[870]= "tricycle, trike, velocipede";
      mLabels[871]= "trimaran";
      mLabels[872]= "tripod";
      mLabels[873]= "triumphal arch";
      mLabels[874]= "trolleybus, trolley coach, trackless trolley";
      mLabels[875]= "trombone";
      mLabels[876]= "tub, vat";
      mLabels[877]= "turnstile";
      mLabels[878]= "typewriter keyboard";
      mLabels[879]= "umbrella";
      mLabels[880]= "unicycle, monocycle";
      mLabels[881]= "upright, upright piano";
      mLabels[882]= "vacuum, vacuum cleaner";
      mLabels[883]= "vase";
      mLabels[884]= "vault";
      mLabels[885]= "velvet";
      mLabels[886]= "vending machine";
      mLabels[887]= "vestment";
      mLabels[888]= "viaduct";
      mLabels[889]= "violin, fiddle";
      mLabels[890]= "volleyball";
      mLabels[891]= "waffle iron";
      mLabels[892]= "wall clock";
      mLabels[893]= "wallet, billfold, notecase, pocketbook";
      mLabels[894]= "wardrobe, closet, press";
      mLabels[895]= "warplane, military plane";
      mLabels[896]= "washbasin, handbasin, washbowl, lavabo, wash-hand basin";
      mLabels[897]= "washer, automatic washer, washing machine";
      mLabels[898]= "water bottle";
      mLabels[899]= "water jug";
      mLabels[900]= "water tower";
      mLabels[901]= "whiskey jug";
      mLabels[902]= "whistle";
      mLabels[903]= "wig";
      mLabels[904]= "window screen";
      mLabels[905]= "window shade";
      mLabels[906]= "Windsor tie";
      mLabels[907]= "wine bottle";
      mLabels[908]= "wing";
      mLabels[909]= "wok";
      mLabels[910]= "wooden spoon";
      mLabels[911]= "wool, woolen, woollen";
      mLabels[912]= "worm fence, snake fence, snake-rail fence, Virginia fence";
      mLabels[913]= "wreck";
      mLabels[914]= "yawl";
      mLabels[915]= "yurt";
      mLabels[916]= "web site, website, internet site, site";
      mLabels[917]= "comic book";
      mLabels[918]= "crossword puzzle, crossword";
      mLabels[919]= "street sign";
      mLabels[920]= "traffic light, traffic signal, stoplight";
      mLabels[921]= "book jacket, dust cover, dust jacket, dust wrapper";
      mLabels[922]= "menu";
      mLabels[923]= "plate";
      mLabels[924]= "guacamole";
      mLabels[925]= "consomme";
      mLabels[926]= "hot pot, hotpot";
      mLabels[927]= "trifle";
      mLabels[928]= "ice cream, icecream";
      mLabels[929]= "ice lolly, lolly, lollipop, popsicle";
      mLabels[930]= "French loaf";
      mLabels[931]= "bagel, beigel";
      mLabels[932]= "pretzel";
      mLabels[933]= "cheeseburger";
      mLabels[934]= "hotdog, hot dog, red hot";
      mLabels[935]= "mashed potato";
      mLabels[936]= "head cabbage";
      mLabels[937]= "broccoli";
      mLabels[938]= "cauliflower";
      mLabels[939]= "zucchini, courgette";
      mLabels[940]= "spaghetti squash";
      mLabels[941]= "acorn squash";
      mLabels[942]= "butternut squash";
      mLabels[943]= "cucumber, cuke";
      mLabels[944]= "artichoke, globe artichoke";
      mLabels[945]= "bell pepper";
      mLabels[946]= "cardoon";
      mLabels[947]= "mushroom";
      mLabels[948]= "Granny Smith";
      mLabels[949]= "strawberry";
      mLabels[950]= "orange";
      mLabels[951]= "lemon";
      mLabels[952]= "fig";
      mLabels[953]= "pineapple, ananas";
      mLabels[954]= "banana";
      mLabels[955]= "jackfruit, jak, jack";
      mLabels[956]= "custard apple";
      mLabels[957]= "pomegranate";
      mLabels[958]= "hay";
      mLabels[959]= "carbonara";
      mLabels[960]= "chocolate sauce, chocolate syrup";
      mLabels[961]= "dough";
      mLabels[962]= "meat loaf, meatloaf";
      mLabels[963]= "pizza, pizza pie";
      mLabels[964]= "potpie";
      mLabels[965]= "burrito";
      mLabels[966]= "red wine";
      mLabels[967]= "espresso";
      mLabels[968]= "cup";
      mLabels[969]= "eggnog";
      mLabels[970]= "alp";
      mLabels[971]= "bubble";
      mLabels[972]= "cliff, drop, drop-off";
      mLabels[973]= "coral reef";
      mLabels[974]= "geyser";
      mLabels[975]= "lakeside, lakeshore";
      mLabels[976]= "promontory, headland, head, foreland";
      mLabels[977]= "sandbar, sand bar";
      mLabels[978]= "seashore, coast, seacoast, sea-coast";
      mLabels[979]= "valley, vale";
      mLabels[980]= "volcano";
      mLabels[981]= "ballplayer, baseball player";
      mLabels[982]= "groom, bridegroom";
      mLabels[983]= "scuba diver";
      mLabels[984]= "rapeseed";
      mLabels[985]= "daisy";
      mLabels[986]= "yellow ladys slipper, yellow lady-slipper, Cypripedium calceolus, Cypripedium parviflorum";
      mLabels[987]= "corn";
      mLabels[988]= "acorn";
      mLabels[989]= "hip, rose hip, rosehip";
      mLabels[990]= "buckeye, horse chestnut, conker";
      mLabels[991]= "coral fungus";
      mLabels[992]= "agaric";
      mLabels[993]= "gyromitra";
      mLabels[994]= "stinkhorn, carrion fungus";
      mLabels[995]= "earthstar";
      mLabels[996]= "hen-of-the-woods, hen of the woods, Polyporus frondosus, Grifola frondosa";
      mLabels[997]= "bolete";
      mLabels[998]= "ear, spike, capitulum";
      mLabels[999]= "toilet tissue, toilet paper, bathroom tissue";
    }
  }

  string imagenet_labelstring( int i ) {
    assert( i>=0 && i<1000 );
    return mLabels[i];
  }

private:
  vector<string> mLabels;

};