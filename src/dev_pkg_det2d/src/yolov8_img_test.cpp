#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "trt_worker.hpp"
#include "utils.hpp"

#include <iostream>
#include <dirent.h> // For directory traversal
#include <sys/stat.h> // For file status
#include <cstring> // For std::strerror
#include <vector>
#include <future>

using namespace std;

int main(int argc, char const *argv[])
{
    /*这么实现目的在于让调用的整个过程精简化*/
    string onnxPath    = "src/dev_pkg_det2d/models/onnx/yolov8n.onnx";

    auto level         = logger::Level::VERB;
    auto params        = model::Params();

    // common params
    params.batch_size       = 1;
    params.img              = {640, 640, 3};
    params.task             = model::task_type::DETECTION;
    params.dev              = model::device::CPU;
    params.prec             = model::precision::INT8;  // INT8
    // detection params
    params.det_model   = model::detection_model::YOLOV8;
    params.calib_data_file    = "src/dev_pkg_det2d/calibration/calibration_list_coco.txt";
    params.calib_table_file   = "src/dev_pkg_det2d/calibration/calibration_yolov8n_dla_table.txt";
    params.conf_thresh        = 0.3;
    params.nms_thresh         = 0.45;
    // dla params
    params.use_dla          = true;
    params.dla_core         = 0;

    // 创建一个worker的实例, 在创建的时候就完成初始化
    auto worker   = thread::create_worker(onnxPath, level, params);

    // 根据worker中的task类型进行推理
    // worker->inference("src/dev_pkg_det2d/data/source/car.jpg");
    // worker->inference("src/dev_pkg_det2d/data/source/bedroom.jpg");
    // worker->inference("src/dev_pkg_det2d/data/source/crossroad.jpg");
    // worker->inference("src/dev_pkg_det2d/data/source/airport.jpg");
    // worker->inference("src/dev_pkg_det2d/data/media/parking.jpg");
    // worker->inference("src/dev_pkg_det2d/data/media/street.jpg");
    // worker->inference("src/dev_pkg_det2d/data/media/bj_short.jpg");

    // worker->inference("src/dev_pkg_det2d/data/val/images/China_Drone_000003.jpg");
    // worker->inference("src/dev_pkg_det2d/data/val/images/China_Drone_000026.jpg");
    // worker->inference("src/dev_pkg_det2d/data/val/images/China_Drone_000035.jpg");
    // worker->inference("src/dev_pkg_det2d/data/val/images/China_Drone_000050.jpg");

    // worker->inference("src/dev_pkg_det2d/data/val/images/Japan_009358_p.jpg");
    // worker->inference("src/dev_pkg_det2d/data/val/images/Japan_012734_p.jpg");
    // worker->inference("src/dev_pkg_det2d/data/val/images/Japan_012899_p.jpg");

    // jump first launch
    worker->inference("src/dev_pkg_det2d/data/source/classification");
    std::chrono::steady_clock::time_point begin;
    std::chrono::steady_clock::time_point end;

    // 获取ms级时间戳
    begin = std::chrono::steady_clock::now();
    for (int i = 0; i < 50; i++) {
        worker->inference("src/dev_pkg_det2d/data/source/classification");
    }
    end = std::chrono::steady_clock::now();
    LOG("\ttime difference of 50 sync inference uses %d ms", 
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());

    // 获取ms级时间戳
    begin = std::chrono::steady_clock::now();
    std::vector<std::future<void>> futures;
    for (int i = 0; i < 50; i++) {
        futures.push_back(worker->async_inference("src/dev_pkg_det2d/data/source/classification"));
    }
    for (auto& future : futures) {
        future.wait();
    }
    end = std::chrono::steady_clock::now();
    LOG("\ttime difference of 50 async inference uses %d ms", 
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
    

    // // 读取coco验证集所有图片进行推理
    // std::string directory = "data/coco/val2017";
    // std::vector<std::string> image_paths = get_image_paths(directory);
    // for (const auto& path : image_paths) {
    //     worker->inference(path);
    // }
    // LOG("\tFile Count: %d", image_paths.size());
    // // 保存所有的评估结果
    // worker->save_eval_results();

    return 0;

    /*
     * 这里记录一下测试结果
     * TensorRT 8.6
     *  - IInt8EntropyCalibrator2: int8精度下降严重，classness掉点严重
     *  - IInt8MinMaxCalibrator:   int8推理精度可以恢复, classness的max会凸显出来
     * 
     * 为什么呢？
     * 其实只要我们回顾一下因为MinMax会把FP32中的最大最小值也会留下来
     * 但为什么yolov8的fp32的最大最小值会如此重要呢？因为C2F架构中会DWConv
     * depthwise的存在会潜在性的让output tensor的FP32在每一个layer都会有很大不同的分布
     * 如果用entropy的话，很有可能会让某些关键信息流失掉
     *
     * 注意，当我们换了模型(e.g. yolov8n -> yolov8x)，
     * 或者换了calibrator(e.g. IInt8EntropyCalibrator2 -> IInt8MinMaxCalibrator)以后
     * 我们必须要把calibration_table给删除掉重新制作。
     * 因为之前的calibration_table记载的层的dynamic range的统计信息无法被复用，会报错
     *
    */
}

