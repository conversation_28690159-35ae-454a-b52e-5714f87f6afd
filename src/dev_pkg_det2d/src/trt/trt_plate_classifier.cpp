#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp" 
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <string>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/opencv.hpp"
#include "imagenet_labels.hpp"
#include "vehicle_color_labels.hpp"
#include "trt_plate_classifier.hpp"
#include "trt_preprocess.hpp"
#include "utils.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace classifier {

/*
    classification model的初始化相关内容。
    包括设置input/output bindings, 分配host/device的memory等
*/
void PlateClassifier::setup(void const* data, size_t size) {
    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    m_colorLabelSize = m_context->getBindingDimensions(1).d[1];
    m_layerLabelSize = m_context->getBindingDimensions(2).d[1];
    // 考虑到大多数classification model都是1 input, 1 output, 这边这么写。如果像BEVFusion这种有多输出的需要修改

    CUDA_CHECK(cudaStreamCreate(&m_stream));
    
    m_inputSize     = m_params->batch_size * m_params->img.c * m_params->img.h * m_params->img.w * sizeof(float);
    m_colorOutputSize = m_context->getBindingDimensions(1).d[1] * m_params->batch_size * sizeof(float);
    m_layerOutputSize = m_context->getBindingDimensions(2).d[1] * m_params->batch_size * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_colorOutputMemory[0], m_colorOutputSize));
    CUDA_CHECK(cudaMallocHost(&m_layerOutputMemory[0], m_layerOutputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_colorOutputMemory[1], m_colorOutputSize));
    CUDA_CHECK(cudaMalloc(&m_layerOutputMemory[1], m_layerOutputSize));

    // //创建m_bindings，之后再寻址就直接从这里找
    m_bindingsOverride[0] = m_inputMemory[1];
    m_bindingsOverride[1] = m_colorOutputMemory[1];
    m_bindingsOverride[2] = m_layerOutputMemory[1];

    // // check class_domains
    // if (m_params->class_domains.empty()) {
    //     LOGE("ERROR: class_domains is empty! Program terminated");
    // }
}

void PlateClassifier::reset_task(){}

bool PlateClassifier::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    /*Preprocess -- 获取mean, std*/
    float mean[]       = {0.5, 0.5, 0.5};
    float std[]        = {0.5, 0.5, 0.5};

    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i); 
            return false;
        }
    }

    /*Preprocess -- 测速*/
    // m_timer->start_cpu();

    /*Preprocess -- resize(默认是bilinear interpolation)*/
    for (int i = 0; i < m_activedBatchSize; i++) {
        cv::resize(m_inputImages[i], m_inputImages[i], 
                   cv::Size(m_params->img.w, m_params->img.h), 0, 0, cv::INTER_LINEAR);
    }

    /*Preprocess -- host端进行normalization和BGR2RGB, NHWC->NCHW*/
    for (int b = 0; b < m_params->batch_size; b++) {
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        // 每个通道在当前批次内的起始位置
        const int offset_ch0 = offset_batch;          // R通道起始位置
        const int offset_ch1 = offset_batch + m_imgArea;   // G通道起始位置
        const int offset_ch2 = offset_batch + 2 * m_imgArea; // B通道起始位置
        for (int i = 0; i < m_params->img.h; i++) {
            for (int j = 0; j < m_params->img.w; j++) {
                // 原图NHWC布局中的像素索引（BGR顺序）
                const int src_index = (i * m_params->img.w + j) * m_params->img.c;
                // 目标NCHW布局中的像素索引（RGB顺序）
                const int dst_pos = i * m_params->img.w + j;
                if (b < m_activedBatchSize) {
                    // BGR转RGB：input[BGR] -> output[RGB]
                    m_inputMemory[0][offset_ch0 + dst_pos] = // R通道
                        (m_inputImages[b].data[src_index + 2] / 255.0f - mean[2]) / std[2];
                    m_inputMemory[0][offset_ch1 + dst_pos] = // G通道
                        (m_inputImages[b].data[src_index + 1] / 255.0f - mean[1]) / std[1];
                    m_inputMemory[0][offset_ch2 + dst_pos] = // B通道
                        (m_inputImages[b].data[src_index + 0] / 255.0f - mean[0]) / std[0];
                } else {
                    // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
                    m_inputMemory[0][offset_ch0 + dst_pos] = 0;
                    m_inputMemory[0][offset_ch1 + dst_pos] = 0;
                    m_inputMemory[0][offset_ch2 + dst_pos] = 0;
                }
            }
        }
    }

    /*Preprocess -- 将host的数据移动到device上*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));

    // m_timer->stop_cpu<timer::Timer::ms>("preprocess(CPU)");

    return true;
}

bool PlateClassifier::preprocess_gpu() {
    /*Preprocess -- 获取mean, std*/
    float mean[]       = {0.5, 0.5, 0.5};
    float std[]        = {0.5, 0.5, 0.5};

    /*Preprocess -- 读取数据*/
    cv::Mat input_image;
    input_image = cv::imread(m_imagePath);
    if (input_image.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }

    /*Preprocess -- 测速*/
    // m_timer->start_gpu();
    
    /*Preprocess -- 使用GPU进行双线性插值, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(input_image, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w, 
                                   mean, std, preprocess::tactics::GPU_BILINEAR);

    // m_timer->stop_gpu("preprocess(GPU)");
    return true;
}

bool PlateClassifier::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);
    /*Postprocess -- 测速*/
    // m_timer->start_cpu();

    /*Postprocess -- 将device上的数据移动到host上*/
    CUDA_CHECK(cudaMemcpyAsync(m_colorOutputMemory[0], m_colorOutputMemory[1], m_colorOutputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaMemcpyAsync(m_layerOutputMemory[0], m_layerOutputMemory[1], m_layerOutputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    // 数值稳定的softmax实现
    auto softmax = [](float* data, int size) {
        // 1. 找到最大值以避免指数溢出
        float maxVal = (size > 0) ? data[0] : 0.0f;
        for(int i = 1; i < size; ++i) {
            if(data[i] > maxVal) maxVal = data[i];
        }
        // 2. 计算指数并求和
        float sum = 0.0f;
        for(int i = 0; i < size; ++i) {
            data[i] = std::exp(data[i] - maxVal);
            sum += data[i];
        }
        // 3. 归一化以确保和为1
        if(sum > 0.0f) {  // 避免除以零
            for(int i = 0; i < size; ++i) {
                data[i] /= sum;
            }
        } else {
            // 处理边缘情况：如果所有值都是极小的负数
            float val = 1.0f / size;
            for(int i = 0; i < size; ++i) {
                data[i] = val;
            }
        }
    };

    for (int b = 0; b < m_params->batch_size; b++) {
        // 处理颜色输出
        float* color_output = m_colorOutputMemory[0] + b * m_colorLabelSize;
        softmax(color_output, m_colorLabelSize);
        // 找到最大概率的颜色类别
        float max_color_prob = -FLT_MAX;
        int color_class = -1;
        for (int i = 0; i < m_colorLabelSize; ++i) {
            LOGV("color_output[%d]: %f", i, color_output[i]);
            if (color_output[i] > max_color_prob) {
                max_color_prob = color_output[i];
                color_class = i;
            }
        }
        
        // 处理层输出
        float* layer_output = m_layerOutputMemory[0] + b * m_layerLabelSize;
        softmax(layer_output, m_layerLabelSize);
        // 找到最大概率的层类别
        float max_layer_prob = -FLT_MAX;
        int layer_class = -1;
        for (int i = 0; i < m_layerLabelSize; ++i) {
            LOGV("layer_output[%d]: %f", i, layer_output[i]);
            if (layer_output[i] > max_layer_prob) {
                max_layer_prob = layer_output[i];
                layer_class = i;
            }
        }
        
        // 输出结果
        if (b < m_activedBatchSize) {
            std::string color_name = COLOR_MAP.at(color_class);
            std::string layer_name = LAYER_MAP.at(layer_class);
            
            LOG("\tBatch %d - 车牌颜色: %s", b, color_name.c_str());
            LOG("\tBatch %d - 车牌颜色置信度: %.3f%%", b, max_color_prob * 100);
            LOG("\tBatch %d - 车牌层数: %s", b, layer_name.c_str());
            LOG("\tBatch %d - 车牌层数置信度: %.3f%%", b, max_layer_prob * 100);
        }

    }

    // m_timer->stop_cpu<timer::Timer::ms>("postprocess(CPU)");
    // m_timer->show();

    return true;
}


bool PlateClassifier::postprocess_gpu() {
    /*
        由于classification task的postprocess比较简单，所以CPU/GPU的处理这里用一样的
        对于像yolo这种detection model, postprocess会包含decode, nms这些处理。可以选择在CPU还是在GPU上跑
    */
    return postprocess_cpu();

}


bool PlateClassifier::check_model() {
    return true;
}

void PlateClassifier::inference() {
    if (m_params->dev == CPU) {
        preprocess_cpu();
    } else {
        preprocess_gpu();
    }

    {
        std::lock_guard<std::mutex> lock(m_enqueue_mutex);
        if (!m_context->enqueueV2((void**)m_bindingsOverride, m_stream, nullptr)){
            LOGE("Error happens during DNN inference part, program terminated");
        }
    }

    if (m_params->dev == CPU) {
        postprocess_cpu();
    } else {
        postprocess_gpu();
    }
}


shared_ptr<PlateClassifier> make_plate_classifier(
    std::string onnx_path, logger::Level level, model::Params params)
{
    return make_shared<PlateClassifier>(onnx_path, level, params);
}

}; // namespace classifier

}; // namespace model
