#include "image_buffer_manager.hpp"
#include <sys/mman.h>
#include <sys/stat.h>
#include <fcntl.h>
#include <unistd.h>
#include <semaphore.h>
#include <string.h>
#include <opencv2/opencv.hpp>
#include <errno.h>
#include <iostream>

// 共享内存默认大小
const size_t DEFAULT_SHARED_MEMORY_SIZE = 1024 * 1024 * 100; // 100MB

// 共享内存头部结构，用于存储元数据
struct SharedMemoryHeader {
    int image_count;
    size_t data_offset;  // 图像数据开始的偏移量
};

// 图像元数据结构，用于在共享内存中存储
struct SharedImageMetadata {
    int64_t unique_id;
    int64_t timestamp;
    char frame_id[256];
    int width;
    int height;
    int channels;
    size_t data_size;
    size_t data_offset;  // 相对于数据区域开始的偏移量
};

// 获取单例实例
ImageBufferManager& ImageBufferManager::getInstance() {
    static ImageBufferManager instance;
    return instance;
}

// 析构函数，清理所有资源
ImageBufferManager::~ImageBufferManager() {
    cleanup();
}

// 创建新的图像缓冲区
bool ImageBufferManager::createBuffer(const std::string& buffer_name, size_t buffer_size) {
    // 如果已存在，直接返回
    if (m_resources.find(buffer_name) != m_resources.end()) {
        return true;
    }
    
    // 创建新的资源结构
    SharedMemoryResource resource;
    resource.buffer = model::create_image_buffer(buffer_size);
    resource.size = DEFAULT_SHARED_MEMORY_SIZE;
    
    // 添加到映射
    m_resources[buffer_name] = resource;
    return true;
}

// 获取指定名称的图像缓冲区
std::shared_ptr<model::ImageBuffer> ImageBufferManager::getBuffer(const std::string& buffer_name) {
    auto it = m_resources.find(buffer_name);
    if (it != m_resources.end()) {
        return it->second.buffer;
    }
    return nullptr;
}

// 初始化共享内存
bool ImageBufferManager::initSharedMemory(const std::string& buffer_name, size_t buffer_size) {
    try {
        // 首先尝试连接到已存在的共享内存
        if (connectToSharedMemory(buffer_name)) {
            std::cout << "已连接到现有共享内存: " << buffer_name << std::endl;
            return true;
        }
        
        // 如果连接失败，则创建新的共享内存
        std::cout << "创建新的共享内存: " << buffer_name << "..." << std::endl;
        
        // 创建共享内存名称和信号量名称
        std::string shm_name = "/image_buffer_" + buffer_name;
        std::string sem_name = "/image_buffer_mutex_" + buffer_name;
        
        // 尝试移除已存在但可能损坏的共享内存
        shm_unlink(shm_name.c_str());
        sem_unlink(sem_name.c_str());
        
        // 创建新的资源结构
        SharedMemoryResource resource;
        resource.size = DEFAULT_SHARED_MEMORY_SIZE;
        
        // 创建共享内存区域
        resource.fd = shm_open(shm_name.c_str(), O_CREAT | O_RDWR, 0666);
        if (resource.fd == -1) {
            std::cerr << "无法创建共享内存: " << strerror(errno) << std::endl;
            return false;
        }
        
        // 设置共享内存大小
        if (ftruncate(resource.fd, resource.size) == -1) {
            std::cerr << "无法设置共享内存大小: " << strerror(errno) << std::endl;
            close(resource.fd);
            return false;
        }
        
        // 映射共享内存
        resource.ptr = mmap(NULL, resource.size, PROT_READ | PROT_WRITE, 
                           MAP_SHARED, resource.fd, 0);
        if (resource.ptr == MAP_FAILED) {
            std::cerr << "无法映射共享内存: " << strerror(errno) << std::endl;
            close(resource.fd);
            return false;
        }
        
        // 创建命名信号量用于同步
        resource.mutex = sem_open(sem_name.c_str(), O_CREAT, 0666, 1);
        if (resource.mutex == SEM_FAILED) {
            std::cerr << "无法创建信号量: " << strerror(errno) << std::endl;
            munmap(resource.ptr, resource.size);
            close(resource.fd);
            return false;
        }
        
        // 创建图像缓冲区
        resource.buffer = model::create_image_buffer(buffer_size);
        
        // 初始化共享内存头部
        SharedMemoryHeader* header = static_cast<SharedMemoryHeader*>(resource.ptr);
        header->image_count = 0;
        header->data_offset = sizeof(SharedMemoryHeader);
        
        // 添加到映射
        m_resources[buffer_name] = resource;
        
        return true;
    } catch (const std::exception& e) {
        std::cerr << "初始化共享内存失败: " << e.what() << std::endl;
        return false;
    }
}

// 连接到已存在的共享内存
bool ImageBufferManager::connectToSharedMemory(const std::string& buffer_name) {
    try {
        // 创建共享内存名称和信号量名称
        std::string shm_name = "/image_buffer_" + buffer_name;
        std::string sem_name = "/image_buffer_mutex_" + buffer_name;
        
        // 创建新的资源结构
        SharedMemoryResource resource;
        
        // 连接到已存在的共享内存区域
        resource.fd = shm_open(shm_name.c_str(), O_RDWR, 0666);
        if (resource.fd == -1) {
            // 对于首次运行时的"不存在"错误不需要打印警告
            if (errno != ENOENT) { // ENOENT 表示文件不存在
                std::cerr << "无法打开共享内存: " << strerror(errno) 
                          << " (名称: " << shm_name << ")" << std::endl;
            }
            return false;
        }
        
        // 获取共享内存大小
        struct stat sb;
        if (fstat(resource.fd, &sb) == -1) {
            std::cerr << "无法获取共享内存大小: " << strerror(errno) << std::endl;
            close(resource.fd);
            return false;
        }
        
        resource.size = sb.st_size;
        std::cout << "共享内存大小: " << resource.size << " 字节" << std::endl;
        
        // 映射共享内存
        resource.ptr = mmap(NULL, resource.size, PROT_READ | PROT_WRITE, 
                           MAP_SHARED, resource.fd, 0);
        if (resource.ptr == MAP_FAILED) {
            std::cerr << "无法映射共享内存: " << strerror(errno) << std::endl;
            close(resource.fd);
            return false;
        }
        
        // 连接到已存在的信号量
        resource.mutex = sem_open(sem_name.c_str(), 0);
        if (resource.mutex == SEM_FAILED) {
            std::cerr << "无法打开信号量: " << strerror(errno) 
                      << " (名称: " << sem_name << ")" << std::endl;
            munmap(resource.ptr, resource.size);
            close(resource.fd);
            return false;
        }
        
        // 创建图像缓冲区
        resource.buffer = model::create_image_buffer(20); // 默认大小
        
        // 添加到映射
        m_resources[buffer_name] = resource;
        
        // 从共享内存读取数据到本地缓冲区
        lockBuffer(buffer_name);
        bool read_success = readBufferFromSharedMemory(buffer_name);
        unlockBuffer(buffer_name);
        
        if (!read_success) {
            std::cerr << "从共享内存读取数据失败: " << buffer_name << std::endl;
        }
        
        std::cout << "成功连接到共享内存: " << buffer_name << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "连接到共享内存失败: " << e.what() << std::endl;
        return false;
    }
}

// 清理共享内存资源
void ImageBufferManager::cleanupSharedMemory(const std::string& buffer_name, bool is_creator) {
    auto it = m_resources.find(buffer_name);
    if (it == m_resources.end()) {
        return;
    }
    
    SharedMemoryResource& resource = it->second;
    
    if (resource.ptr != nullptr && resource.ptr != MAP_FAILED) {
        munmap(resource.ptr, resource.size);
        resource.ptr = nullptr;
    }
    
    if (resource.fd != -1) {
        close(resource.fd);
        resource.fd = -1;
    }
    
    if (resource.mutex != nullptr && resource.mutex != SEM_FAILED) {
        sem_close(resource.mutex);
        resource.mutex = nullptr;
    }
    
    // 只有创建者才应该删除共享资源
    if (is_creator) {
        std::string shm_name = "/image_buffer_" + buffer_name;
        std::string sem_name = "/image_buffer_mutex_" + buffer_name;
        
        std::cout << "清理共享内存资源: " << buffer_name << "..." << std::endl;
        shm_unlink(shm_name.c_str());
        sem_unlink(sem_name.c_str());
    }
    
    // 从映射中移除
    m_resources.erase(it);
}

// 锁定共享缓冲区
void ImageBufferManager::lockBuffer(const std::string& buffer_name) {
    auto it = m_resources.find(buffer_name);
    if (it != m_resources.end() && it->second.mutex != nullptr && it->second.mutex != SEM_FAILED) {
        sem_wait(it->second.mutex);
    }
}

// 解锁共享缓冲区
void ImageBufferManager::unlockBuffer(const std::string& buffer_name) {
    auto it = m_resources.find(buffer_name);
    if (it != m_resources.end() && it->second.mutex != nullptr && it->second.mutex != SEM_FAILED) {
        sem_post(it->second.mutex);
    }
}

// 将图像缓冲区数据写入共享内存
bool ImageBufferManager::writeBufferToSharedMemory(const std::string& buffer_name) {
    auto it = m_resources.find(buffer_name);
    if (it == m_resources.end() || !it->second.buffer || !it->second.ptr) {
        return false;
    }
    
    SharedMemoryResource& resource = it->second;
    
    try {
        // 获取所有图像和元数据
        std::vector<cv::Mat> images;
        std::vector<model::ImageMetadata> metadata_list;
        if (!resource.buffer->get_all_images(images, metadata_list)) {
            std::cerr << "获取图像失败: " << buffer_name << std::endl;
            return false;
        }
        
        // 计算所需的内存大小
        size_t header_size = sizeof(SharedMemoryHeader);
        size_t metadata_array_size = sizeof(SharedImageMetadata) * images.size();
        
        // 设置头部
        SharedMemoryHeader* header = static_cast<SharedMemoryHeader*>(resource.ptr);
        header->image_count = images.size();
        header->data_offset = header_size + metadata_array_size;
        
        // 写入元数据数组
        SharedImageMetadata* shared_metadata = reinterpret_cast<SharedImageMetadata*>(
            static_cast<char*>(resource.ptr) + header_size);
        
        // 计算数据区域的起始位置
        char* data_area = static_cast<char*>(resource.ptr) + header->data_offset;
        size_t current_data_offset = 0;
        
        // 写入每张图像及其元数据
        for (size_t i = 0; i < images.size(); ++i) {
            const cv::Mat& img = images[i];
            const model::ImageMetadata& meta = metadata_list[i];
            
            // 填充元数据
            shared_metadata[i].unique_id = meta.unique_id;
            shared_metadata[i].timestamp = meta.timestamp;
            strncpy(shared_metadata[i].frame_id, meta.frame_id.c_str(), 255);
            shared_metadata[i].frame_id[255] = '\0';
            shared_metadata[i].width = img.cols;
            shared_metadata[i].height = img.rows;
            shared_metadata[i].channels = img.channels();
            shared_metadata[i].data_size = img.total() * img.elemSize();
            shared_metadata[i].data_offset = current_data_offset;
            
            // 复制图像数据
            if (current_data_offset + shared_metadata[i].data_size > resource.size - header->data_offset) {
                std::cerr << "共享内存不足以存储所有图像: " << buffer_name << std::endl;
                return false;
            }
            
            memcpy(data_area + current_data_offset, img.data, shared_metadata[i].data_size);
            current_data_offset += shared_metadata[i].data_size;
        }
        
        std::cout << "已将 " << images.size() << " 张图像写入共享内存: " << buffer_name << std::endl;
        return true;
    } catch (const std::exception& e) {
        std::cerr << "写入共享内存失败: " << e.what() << std::endl;
        return false;
    }
}

// 从共享内存读取图像缓冲区数据
bool ImageBufferManager::readBufferFromSharedMemory(const std::string& buffer_name) {
    auto it = m_resources.find(buffer_name);
    if (it == m_resources.end() || !it->second.buffer || !it->second.ptr) {
        return false;
    }
    
    SharedMemoryResource& resource = it->second;
    
    try {
        // 读取头部
        SharedMemoryHeader* header = static_cast<SharedMemoryHeader*>(resource.ptr);
        if (header->image_count <= 0) {
            std::cerr << "共享内存中没有图像数据: " << buffer_name << std::endl;
            return false;
        }
        
        // 读取元数据数组
        SharedImageMetadata* shared_metadata = reinterpret_cast<SharedImageMetadata*>(
            static_cast<char*>(resource.ptr) + sizeof(SharedMemoryHeader));
        
        // 获取数据区域的起始位置
        char* data_area = static_cast<char*>(resource.ptr) + header->data_offset;
        
        // 读取每张图像及其元数据
        std::vector<cv::Mat> images;
        std::vector<model::ImageMetadata> metadata_list;
        
        for (int i = 0; i < header->image_count; ++i) {
            // 创建图像
            cv::Mat img(shared_metadata[i].height, shared_metadata[i].width, 
                       CV_8UC(shared_metadata[i].channels));
            
            // 复制图像数据
            memcpy(img.data, data_area + shared_metadata[i].data_offset, 
                  shared_metadata[i].data_size);
            
            // 创建元数据
            model::ImageMetadata meta;
            meta.unique_id = shared_metadata[i].unique_id;
            meta.timestamp = shared_metadata[i].timestamp;
            meta.frame_id = shared_metadata[i].frame_id;
            
            // 添加到向量
            images.push_back(img);
            metadata_list.push_back(meta);
        }
        
        // 更新本地缓冲区
        if (!images.empty()) {
            resource.buffer->add_images(images, metadata_list);
            std::cout << "已从共享内存读取 " << images.size() << " 张图像: " << buffer_name << std::endl;
            return true;
        }
        
        return false;
    } catch (const std::exception& e) {
        std::cerr << "从共享内存读取失败: " << e.what() << std::endl;
        return false;
    }
}

// 清理所有资源
void ImageBufferManager::cleanup() {
    // 复制键列表，因为在循环中会修改映射
    std::vector<std::string> keys;
    for (const auto& pair : m_resources) {
        keys.push_back(pair.first);
    }
    
    // 清理每个缓冲区
    for (const auto& key : keys) {
        cleanupSharedMemory(key, true);
    }
}