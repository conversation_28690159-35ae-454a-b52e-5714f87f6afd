#ifndef __TRT_FISHEYE_DETECTOR_DETR_HPP__
#define __TRT_FISHEYE_DETECTOR_DETR_HPP__

#include <thread>
#include <mutex>
#include <memory>
#include <vector>
#include <string>
#include "NvInfer.h"
#include "trt_logger.hpp"
#include "trt_model.hpp"
#include "trt_evaluation.hpp"
#include <cmath> // for exp function
#include <algorithm> // for std::min, std::max

namespace model{

namespace detector {

class FisheyeDetectorDetr : public Model{

public:
    // 这个构造函数实际上调用的是父类的Model的构造函数
    FisheyeDetectorDetr(std::string onnx_path, logger::Level level, Params params)
        : Model(onnx_path, level, params),
          m_cocoResults("eval/yolov8n/eval_results.json"),
          m_bboxCount(0){
            switch(params.det_model){
                case model::detection_model::YOLOV5:
                    m_padding_value = 114;
                    m_xywhs = 5;
                    break;
                case model::detection_model::YOLOV7:
                    m_padding_value = 114;
                    m_xywhs = 5;
                    break;
                case model::detection_model::YOLOV8:
                    m_padding_value = 0;
                    m_xywhs = 4;
                    break;
                case model::detection_model::DETR:
                    m_padding_value = 0;
                    m_xywhs = 4;
                    break;
                default:
                    m_padding_value = 0;
                    m_xywhs = 0;
                    break;
            }
          };

public:
    // 这里detection自己实现了一套前处理/后处理，以及内存分配的初始化
    virtual void setup(void const* data, std::size_t size) override;
    virtual void reset_task() override;
    virtual bool preprocess_cpu() override;
    virtual bool preprocess_gpu() override;
    virtual bool postprocess_cpu() override;
    virtual bool postprocess_gpu() override;
    // check model name in params
    virtual bool check_model() override;
    virtual void inference() override;

public:
    // 以coco可处理的格式，保存所有检测的结果
    void save_coco_results() {
        m_cocoResults.save_json_results();
    };
    bool get_obj_result(std::vector<bbox> &objects, std::string id);
    bool get_img_result(cv::Mat &img, std::string id);
    std::vector<std::vector<bbox>> get_batch_obj_bboxes();
    std::vector<std::vector<cv::Mat>> get_batch_cropped_images();
    // 获取带有检测框的输入图像
    cv::Mat get_image_detected(int batch_idx) {
        if (batch_idx >= 0 && batch_idx < static_cast<int>(m_inputImages.size())) {
            return m_inputImages[batch_idx];
        }
        return cv::Mat(); // 返回空图像
    }

private:
    float iou_calc(bbox bbox1, bbox bbox2);

private:
    // std::vector<bbox> m_bboxes;
    // std::vector<std::vector<bbox>> m_batch_bboxes; // first dim: batch, second dim: bboxes
    int m_inputSize; 
    int m_imgArea;
    // int m_outputSize;
    int m_logitsOutputSize;
    int m_boxesOutputSize;
    float* m_logitsOutputMemory[2];
    float* m_boxesOutputMemory[2];
    float* m_bindingsOverride[3];
    // result for evaluation
    evaluation::CocoEvaluator m_cocoResults;
    // all bbox for debug
    int m_bboxCount;
    // result cache (old interface)
    std::map<std::string, std::vector<bbox>> obj_results;
    std::map<std::string, cv::Mat> img_results;
    std::mutex mtx_result;
    // common params for different models
    uint8_t m_padding_value;
    int m_xywhs;
    // result cache
    std::vector<std::vector<bbox>> m_batch_obj_bboxes;
    // cropped images
    std::vector<std::vector<cv::Mat>> m_batch_cropped_images;

private:
    // 其他成员变量...
    struct ImageSize {
        int width;
        int height;
    };
    std::vector<ImageSize> m_originalSizes;
};

// 外部调用的接口
std::shared_ptr<FisheyeDetectorDetr> make_fisheye_detector_detr(
    std::string onnx_path, logger::Level level, Params params);

}; // namespace detector
}; // namespace model

#endif //__TRT_FISHEYE_DETECTOR_DETR_HPP__
