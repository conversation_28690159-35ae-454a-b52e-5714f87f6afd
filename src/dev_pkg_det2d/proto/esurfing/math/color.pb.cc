// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/math/color.proto

#include "esurfing/math/color.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace perception {
namespace detect {
PROTOBUF_CONSTEXPR Color::Color(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.r_)*/0
  , /*decltype(_impl_.g_)*/0
  , /*decltype(_impl_.b_)*/0
  , /*decltype(_impl_.a_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ColorDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ColorDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ColorDefaultTypeInternal() {}
  union {
    Color _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ColorDefaultTypeInternal _Color_default_instance_;
}  // namespace detect
}  // namespace perception
static ::_pb::Metadata file_level_metadata_esurfing_2fmath_2fcolor_2eproto[1];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_esurfing_2fmath_2fcolor_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_esurfing_2fmath_2fcolor_2eproto = nullptr;

const uint32_t TableStruct_esurfing_2fmath_2fcolor_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::Color, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::Color, _impl_.r_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::Color, _impl_.g_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::Color, _impl_.b_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::Color, _impl_.a_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::perception::detect::Color)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::perception::detect::_Color_default_instance_._instance,
};

const char descriptor_table_protodef_esurfing_2fmath_2fcolor_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031esurfing/math/color.proto\022\021perception."
  "detect\"3\n\005Color\022\t\n\001r\030\001 \001(\005\022\t\n\001g\030\002 \001(\005\022\t\n"
  "\001b\030\003 \001(\005\022\t\n\001a\030\004 \001(\002B\rB\tPercColorP\000b\006prot"
  "o3"
  ;
static ::_pbi::once_flag descriptor_table_esurfing_2fmath_2fcolor_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_esurfing_2fmath_2fcolor_2eproto = {
    false, false, 122, descriptor_table_protodef_esurfing_2fmath_2fcolor_2eproto,
    "esurfing/math/color.proto",
    &descriptor_table_esurfing_2fmath_2fcolor_2eproto_once, nullptr, 0, 1,
    schemas, file_default_instances, TableStruct_esurfing_2fmath_2fcolor_2eproto::offsets,
    file_level_metadata_esurfing_2fmath_2fcolor_2eproto, file_level_enum_descriptors_esurfing_2fmath_2fcolor_2eproto,
    file_level_service_descriptors_esurfing_2fmath_2fcolor_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_esurfing_2fmath_2fcolor_2eproto_getter() {
  return &descriptor_table_esurfing_2fmath_2fcolor_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_esurfing_2fmath_2fcolor_2eproto(&descriptor_table_esurfing_2fmath_2fcolor_2eproto);
namespace perception {
namespace detect {

// ===================================================================

class Color::_Internal {
 public:
};

Color::Color(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.Color)
}
Color::Color(const Color& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Color* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.r_){}
    , decltype(_impl_.g_){}
    , decltype(_impl_.b_){}
    , decltype(_impl_.a_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.r_, &from._impl_.r_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.a_) -
    reinterpret_cast<char*>(&_impl_.r_)) + sizeof(_impl_.a_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.Color)
}

inline void Color::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.r_){0}
    , decltype(_impl_.g_){0}
    , decltype(_impl_.b_){0}
    , decltype(_impl_.a_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Color::~Color() {
  // @@protoc_insertion_point(destructor:perception.detect.Color)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Color::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Color::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Color::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.Color)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.r_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.a_) -
      reinterpret_cast<char*>(&_impl_.r_)) + sizeof(_impl_.a_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Color::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 r = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.r_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 g = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.g_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 b = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.b_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float a = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.a_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Color::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.Color)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 r = 1;
  if (this->_internal_r() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_r(), target);
  }

  // int32 g = 2;
  if (this->_internal_g() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_g(), target);
  }

  // int32 b = 3;
  if (this->_internal_b() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_b(), target);
  }

  // float a = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = this->_internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_a(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.Color)
  return target;
}

size_t Color::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.Color)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 r = 1;
  if (this->_internal_r() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_r());
  }

  // int32 g = 2;
  if (this->_internal_g() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_g());
  }

  // int32 b = 3;
  if (this->_internal_b() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_b());
  }

  // float a = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = this->_internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Color::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Color::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Color::GetClassData() const { return &_class_data_; }


void Color::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Color*>(&to_msg);
  auto& from = static_cast<const Color&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.Color)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_r() != 0) {
    _this->_internal_set_r(from._internal_r());
  }
  if (from._internal_g() != 0) {
    _this->_internal_set_g(from._internal_g());
  }
  if (from._internal_b() != 0) {
    _this->_internal_set_b(from._internal_b());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_a = from._internal_a();
  uint32_t raw_a;
  memcpy(&raw_a, &tmp_a, sizeof(tmp_a));
  if (raw_a != 0) {
    _this->_internal_set_a(from._internal_a());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Color::CopyFrom(const Color& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.Color)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Color::IsInitialized() const {
  return true;
}

void Color::InternalSwap(Color* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Color, _impl_.a_)
      + sizeof(Color::_impl_.a_)
      - PROTOBUF_FIELD_OFFSET(Color, _impl_.r_)>(
          reinterpret_cast<char*>(&_impl_.r_),
          reinterpret_cast<char*>(&other->_impl_.r_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Color::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fmath_2fcolor_2eproto_getter, &descriptor_table_esurfing_2fmath_2fcolor_2eproto_once,
      file_level_metadata_esurfing_2fmath_2fcolor_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace detect
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::perception::detect::Color*
Arena::CreateMaybeMessage< ::perception::detect::Color >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::Color >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
