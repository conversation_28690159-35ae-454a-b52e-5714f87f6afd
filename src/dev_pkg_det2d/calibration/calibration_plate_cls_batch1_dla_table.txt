TRT-8602-EntropyCalibration2
input: 3c305a1f
/conv1/Conv_output_0: 3d2c146f
/relu/Relu_output_0: 3d049de0
/maxpool/MaxPool_output_0: 3d049de0
/layer1/layer1.0/conv1/Conv_output_0: 3d22831a
/layer1/layer1.0/relu/Relu_output_0: 3d227443
/layer1/layer1.0/conv2/Conv_output_0: 3d1b6eb8
/layer1/layer1.0/Add_output_0: 3d8521e9
/layer1/layer1.0/relu_1/Relu_output_0: 3d8521e9
/layer1/layer1.1/conv1/Conv_output_0: 3d35814f
/layer1/layer1.1/relu/Relu_output_0: 3d0d450d
/layer1/layer1.1/conv2/Conv_output_0: 3d1ccc1d
/layer1/layer1.1/Add_output_0: 3d8ca6c7
/layer1/layer1.1/relu_1/Relu_output_0: 3d8ca6c7
/layer1/layer1.2/conv1/Conv_output_0: 3d11ec86
/layer1/layer1.2/relu/Relu_output_0: 3d17bc9b
/layer1/layer1.2/conv2/Conv_output_0: 3d0f1e07
/layer1/layer1.2/Add_output_0: 3da2293b
/layer1/layer1.2/relu_1/Relu_output_0: 3da2293b
/layer2/layer2.0/downsample/downsample.0/Conv_output_0: 3d15f562
/layer2/layer2.0/conv1/Conv_output_0: 3d10968e
/layer2/layer2.0/relu/Relu_output_0: 3d1bc8b3
/layer2/layer2.0/conv2/Conv_output_0: 3d16bd66
/layer2/layer2.0/Add_output_0: 3d745b2f
/layer2/layer2.0/relu_1/Relu_output_0: 3d59d377
/layer2/layer2.1/conv1/Conv_output_0: 3d09a637
/layer2/layer2.1/relu/Relu_output_0: 3d0e1b2b
/layer2/layer2.1/conv2/Conv_output_0: 3d20b6db
/layer2/layer2.1/Add_output_0: 3d980b03
/layer2/layer2.1/relu_1/Relu_output_0: 3d980b03
/layer2/layer2.2/conv1/Conv_output_0: 3d093fa6
/layer2/layer2.2/relu/Relu_output_0: 3d093fa6
/layer2/layer2.2/conv2/Conv_output_0: 3d032618
/layer2/layer2.2/Add_output_0: 3d996adb
/layer2/layer2.2/relu_1/Relu_output_0: 3d996adb
/layer2/layer2.3/conv1/Conv_output_0: 3d281cb9
/layer2/layer2.3/relu/Relu_output_0: 3d09e4f7
/layer2/layer2.3/conv2/Conv_output_0: 3d10dc5b
/layer2/layer2.3/Add_output_0: 3dd7f713
/layer2/layer2.3/relu_1/Relu_output_0: 3dd7f713
/layer3/layer3.0/downsample/downsample.0/Conv_output_0: 3d0ffe0c
/layer3/layer3.0/conv1/Conv_output_0: 3d13126e
/layer3/layer3.0/relu/Relu_output_0: 3d1f1665
/layer3/layer3.0/conv2/Conv_output_0: 3d1a7636
/layer3/layer3.0/Add_output_0: 3d8668d5
/layer3/layer3.0/relu_1/Relu_output_0: 3d8668d5
/layer3/layer3.1/conv1/Conv_output_0: 3d3259db
/layer3/layer3.1/relu/Relu_output_0: 3d325127
/layer3/layer3.1/conv2/Conv_output_0: 3d143fc8
/layer3/layer3.1/Add_output_0: 3da2bf08
/layer3/layer3.1/relu_1/Relu_output_0: 3db3aa10
/layer3/layer3.2/conv1/Conv_output_0: 3d1aa29e
/layer3/layer3.2/relu/Relu_output_0: 3d28b5a3
/layer3/layer3.2/conv2/Conv_output_0: 3d04c6ab
/layer3/layer3.2/Add_output_0: 3dde66fb
/layer3/layer3.2/relu_1/Relu_output_0: 3dde66fb
/layer3/layer3.3/conv1/Conv_output_0: 3d25a6dc
/layer3/layer3.3/relu/Relu_output_0: 3d25a6dc
/layer3/layer3.3/conv2/Conv_output_0: 3d02d23c
/layer3/layer3.3/Add_output_0: 3e0704d2
/layer3/layer3.3/relu_1/Relu_output_0: 3e0704d2
/layer3/layer3.4/conv1/Conv_output_0: 3cf4a1cd
/layer3/layer3.4/relu/Relu_output_0: 3d091b26
/layer3/layer3.4/conv2/Conv_output_0: 3cd67853
/layer3/layer3.4/Add_output_0: 3e00f81f
/layer3/layer3.4/relu_1/Relu_output_0: 3e020a8a
/layer3/layer3.5/conv1/Conv_output_0: 3cf23d7a
/layer3/layer3.5/relu/Relu_output_0: 3d025576
/layer3/layer3.5/conv2/Conv_output_0: 3ce6f550
/layer3/layer3.5/Add_output_0: 3df86393
/layer3/layer3.5/relu_1/Relu_output_0: 3e03191c
/layer4/layer4.0/downsample/downsample.0/Conv_output_0: 3d208263
/layer4/layer4.0/conv1/Conv_output_0: 3d0d086a
/layer4/layer4.0/relu/Relu_output_0: 3d17fdc7
/layer4/layer4.0/conv2/Conv_output_0: 3cff720f
/layer4/layer4.0/Add_output_0: 3d80e47f
/layer4/layer4.0/relu_1/Relu_output_0: 3d8dedb0
/layer4/layer4.1/conv1/Conv_output_0: 3d0950ac
/layer4/layer4.1/relu/Relu_output_0: 3d17baa7
/layer4/layer4.1/conv2/Conv_output_0: 3d0613d8
/layer4/layer4.1/Add_output_0: 3d9f26b7
/layer4/layer4.1/relu_1/Relu_output_0: 3d9f26b7
/layer4/layer4.2/conv1/Conv_output_0: 3d1ae587
/layer4/layer4.2/relu/Relu_output_0: 3d1bf86c
/layer4/layer4.2/conv2/Conv_output_0: 3d21dea1
/layer4/layer4.2/Add_output_0: 3dd1e683
/layer4/layer4.2/relu_1/Relu_output_0: 3dcfdef3
/avgpool/GlobalAveragePool_output_0: 3d887074
/Flatten_output_0: 3d887074
(Unnamed Layer* 88) [Constant]_output: 3a02ef1c
(Unnamed Layer* 89) [Matrix Multiply]_output: 3d9876bb
(Unnamed Layer* 90) [Constant]_output: 39e9a698
(Unnamed Layer* 91) [Shuffle]_output: 39e9a698
/classifier/classifier.1/Gemm_output_0: 3d82da6f
/classifier/classifier.2/Relu_output_0: 3dbd325d
(Unnamed Layer* 94) [Constant]_output: 39a79c8b
(Unnamed Layer* 95) [Matrix Multiply]_output: 3d84eb37
(Unnamed Layer* 96) [Constant]_output: 3985d55b
(Unnamed Layer* 97) [Shuffle]_output: 3985d55b
/classifier/classifier.4/Gemm_output_0: 3d50301b
/classifier/classifier.5/Relu_output_0: 3d499409
(Unnamed Layer* 100) [Constant]_output: 3985e485
(Unnamed Layer* 101) [Matrix Multiply]_output: 3e03c5d8
(Unnamed Layer* 102) [Constant]_output: 38fcaa0b
(Unnamed Layer* 103) [Shuffle]_output: 38fcaa0b
output_1: 3e023d4f
(Unnamed Layer* 105) [Constant]_output: 3a02555e
(Unnamed Layer* 106) [Matrix Multiply]_output: 3da04b85
(Unnamed Layer* 107) [Constant]_output: 39dc0c53
(Unnamed Layer* 108) [Shuffle]_output: 39dc0c53
/classifier2/classifier2.1/Gemm_output_0: 3d9da4bd
/classifier2/classifier2.2/Relu_output_0: 3dc90b36
(Unnamed Layer* 111) [Constant]_output: 3994fbb9
(Unnamed Layer* 112) [Matrix Multiply]_output: 3da1b75d
(Unnamed Layer* 113) [Constant]_output: 394d5355
(Unnamed Layer* 114) [Shuffle]_output: 394d5355
/classifier2/classifier2.4/Gemm_output_0: 3d979bdb
/classifier2/classifier2.5/Relu_output_0: 3da21f21
(Unnamed Layer* 117) [Constant]_output: 393d9eaa
(Unnamed Layer* 118) [Matrix Multiply]_output: 3de10504
(Unnamed Layer* 119) [Constant]_output: 38cdb1e0
(Unnamed Layer* 120) [Shuffle]_output: 38cdb1e0
output_2: 3de50ab7
