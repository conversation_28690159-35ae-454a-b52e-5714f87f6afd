cmake_minimum_required(VERSION 3.8)
project(dev_pkg_det2d LANGUAGES CXX CUDA)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++17 -O3")
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_BUILD_TYPE Release)
option(CUDA_USE_STATIC_CUDA_RUNTIME OFF)

# Default to C99
if(NOT CMAKE_C_STANDARD)
  set(CMAKE_C_STANDARD 99)
endif()

# Default to C++14
if(NOT CMAKE_CXX_STANDARD)
  set(CMAKE_CXX_STANDARD 14)
endif()

if(CMAKE_COMPILER_IS_GNUCXX OR CMAKE_CXX_COMPILER_ID MATCHES "Clang")
  add_compile_options(-Wall -Wextra -Wpedantic)
endif()

# =========================== 常规依赖设置==================================================
option(CMAKE_CUDASM "CUDA SM Version" OFF)
set(CMAKE_CUDASM 87)
set(ENV{ConfigurationStatus} "Failed")
# Set TensorRT-related variables
set(ENV{TensorRT_Lib} "/usr/lib/aarch64-linux-gnu")
set(ENV{TensorRT_Inc} "/usr/include/aarch64-linux-gnu/")
set(ENV{TensorRT_Bin} "/usr/src/tensorrt/bin/")
# Set CUDA-related variables
set(ENV{CUDA_Lib} "/usr/local/cuda12.2/lib64")
set(ENV{CUDA_Inc} "/usr/local/cuda12.2/include")
set(ENV{CUDA_Bin} "/usr/local/cuda12.2/bin")
set(ENV{CUDNN_Lib} "/usr/local/cuda12.2/lib64")
# Set protobuf and spconv variables
# set(ENV{Protobuf_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# set(ENV{Spdlog_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# set(ENV{Spconv_Lib} ${CMAKE_CURRENT_SOURCE_DIR}/devel/lib)
# # set lib
# set(OpenCV_DIR "/usr/local/opencv_481/lib/cmake/opencv4")
# set(CUDA_TOOLKIT_ROOT_DIR "/usr/local/cuda-11.4")
set(ENV{PATH} "$ENV{TensorRT_Bin}:$ENV{CUDA_Bin}:$ENV{PATH}")
set(ENV{LD_LIBRARY_PATH} "$ENV{TensorRT_Lib}:$ENV{CUDA_Lib}:$ENV{CUDNN_Lib}:$ENV{Spconv_Lib}:$ENV{LD_LIBRARY_PATH}")
set(ENV{ConfigurationStatus} "Success")

set(CUDA_NVCC_FLAGS_RELEASE "-Xcompiler -std=c++14,-Wextra,-Wall,-Wno-deprecated-declarations,-O3 -DENABLE_TEXT_BACKEND_STB -use_fast_math")
set(CUDA_NVCC_FLAGS_DEBUG "-std=c++17 -O0 -g -DENABLE_TEXT_BACKEND_STB -use_fast_math")

message(STATUS "Set CUDA optimizaion flag for cuda arc version = `${CMAKE_CUDASM}`")
if(CMAKE_BUILD_TYPE MATCHES "Release")
  set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS_RELEASE}
    -gencode arch=compute_${CMAKE_CUDASM},code=compute_${CMAKE_CUDASM})
elseif(CMAKE_BUILD_TYPE MATCHES "Debug")
  set(CUDA_NVCC_FLAGS ${CUDA_NVCC_FLAGS_DEBUG}
    -gencode arch=compute_${CMAKE_CUDASM},code=compute_${CMAKE_CUDASM})
endif()
# =========================================================================================

# Paths
set(SRC_PATH "src")
set(INC_PATH "include/dev_pkg_det2d")
# Source files
file(GLOB_RECURSE TRT_SRC "${SRC_PATH}/trt/*.cpp")
file(GLOB_RECURSE CU_SRC "${SRC_PATH}/cu/*.cu")

# find dependencies
find_package(ament_cmake REQUIRED)
find_package(ament_cmake_auto REQUIRED)
ament_auto_find_build_dependencies()
# uncomment the following section in order to fill in
# further dependencies manually.
# find_package(<dependency> REQUIRED)
find_package(ament_index_cpp REQUIRED)
find_package(rclcpp REQUIRED)
find_package(rclcpp_components REQUIRED)
find_package(sensor_msgs REQUIRED)
find_package(std_msgs REQUIRED)
find_package(message_filters REQUIRED)
find_package(cv_bridge REQUIRED)
find_package(OpenCV REQUIRED)
find_package(CUDA REQUIRED)
find_package(yaml-cpp REQUIRED)
find_package(Protobuf REQUIRED)
# use custom interfaces
find_package(dev_pkg_interfaces)

# Include directories
include_directories(
    ${SRC_PATH}
    ${INC_PATH}
    ${OpenCV_INCLUDE_DIRS}
    ${CUDA_INCLUDE_DIR}
    # Do not need to set trt inc in trt docker

    ${Protobuf_INCLUDE_DIRS}
    ${CMAKE_CURRENT_SOURCE_DIR}/proto
)

# Libraries
link_directories(
    ${CUDA_LIB_DIR}
    # Do not need to set trt lib in trt docker
)

# cmake sub directories
add_subdirectory(${CMAKE_CURRENT_LIST_DIR}/proto)

# CUDA plugins for pre/post processing
cuda_add_library(cuda_plugins SHARED ${CU_SRC})
target_link_libraries(cuda_plugins cudart)
install(TARGETS
    cuda_plugins
    DESTINATION lib)

# TRT pipeline
add_library(trt_plugins SHARED ${TRT_SRC})
target_link_libraries(trt_plugins
    stdc++fs
    nvinfer
    nvonnxparser
    cuda_plugins
)
install(TARGETS
    trt_plugins
    DESTINATION lib)

# Add executable yolov8_img_test
add_executable(yolov8_img_test ${SRC_PATH}/yolov8_img_test.cpp)
target_link_libraries(yolov8_img_test trt_plugins)
ament_target_dependencies(yolov8_img_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install yolov8_img_test
install(TARGETS yolov8_img_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable classifier_img_test
add_executable(classifier_img_test ${SRC_PATH}/classifier_img_test.cpp)
target_link_libraries(classifier_img_test trt_plugins)
ament_target_dependencies(classifier_img_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install classifier_img_test
install(TARGETS classifier_img_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add image_buffer_manager library
add_library(image_buffer_manager SHARED
    ${SRC_PATH}/oth/image_buffer_manager.cpp
)
target_include_directories(image_buffer_manager PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/${INC_PATH}
)
target_link_libraries(image_buffer_manager
    ${OpenCV_LIBS}
    rt  # POSIX实时库
    pthread  # 线程库
)
install(TARGETS image_buffer_manager
    DESTINATION lib
)

# Add new executable img_buffer_load_test
add_executable(img_buffer_load_test ${SRC_PATH}/img_buffer_load_test.cpp)
target_link_libraries(img_buffer_load_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(img_buffer_load_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install img_buffer_load_test
install(TARGETS img_buffer_load_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable classifier_img_buffer_test
add_executable(classifier_img_buffer_test ${SRC_PATH}/classifier_img_buffer_test.cpp)
target_link_libraries(classifier_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(classifier_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install classifier_img_buffer_test
install(TARGETS classifier_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable plate_det_img_buffer_test
add_executable(plate_det_img_buffer_test ${SRC_PATH}/plate_det_img_buffer_test.cpp)
target_link_libraries(plate_det_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(plate_det_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install plate_det_img_buffer_test
install(TARGETS plate_det_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable vehicle_det_img_buffer_test
add_executable(vehicle_det_img_buffer_test ${SRC_PATH}/vehicle_det_img_buffer_test.cpp)
target_link_libraries(vehicle_det_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(vehicle_det_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install vehicle_det_img_buffer_test
install(TARGETS vehicle_det_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable vehicle_det_detr_img_buffer_test
add_executable(vehicle_det_detr_img_buffer_test ${SRC_PATH}/vehicle_det_detr_img_buffer_test.cpp)
target_link_libraries(vehicle_det_detr_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(vehicle_det_detr_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install vehicle_det_detr_img_buffer_test
install(TARGETS vehicle_det_detr_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable fisheye_det_detr_img_buffer_test
add_executable(fisheye_det_detr_img_buffer_test ${SRC_PATH}/fisheye_det_detr_img_buffer_test.cpp)
target_link_libraries(fisheye_det_detr_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(fisheye_det_detr_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install fisheye_det_detr_img_buffer_test
install(TARGETS fisheye_det_detr_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable helmet_cls_img_buffer_test
add_executable(helmet_cls_img_buffer_test ${SRC_PATH}/helmet_cls_img_buffer_test.cpp)
target_link_libraries(helmet_cls_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(helmet_cls_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install helmet_cls_img_buffer_test
install(TARGETS helmet_cls_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable plate_cls_img_buffer_test
add_executable(plate_cls_img_buffer_test ${SRC_PATH}/plate_cls_img_buffer_test.cpp)
target_link_libraries(plate_cls_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(plate_cls_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install plate_cls_img_buffer_test
install(TARGETS plate_cls_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable ocr_img_buffer_test
add_executable(ocr_img_buffer_test ${SRC_PATH}/ocr_img_buffer_test.cpp)
target_link_libraries(ocr_img_buffer_test
    trt_plugins
    image_buffer_manager
    rt
    pthread
)
ament_target_dependencies(ocr_img_buffer_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Install ocr_img_buffer_test
install(TARGETS ocr_img_buffer_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable yolo_ros2_interface_test
add_executable(yolo_ros2_interface_test ${SRC_PATH}/yolo_ros2_interface_test.cpp)
target_link_libraries(yolo_ros2_interface_test
    trt_plugins
    yaml-cpp
)
ament_target_dependencies(yolo_ros2_interface_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
    dev_pkg_interfaces
    ament_index_cpp
)
# Install yolo_ros2_interface_test
install(TARGETS yolo_ros2_interface_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add the component library
add_library(det2d_component SHARED src/yolo_ros2_component_test.cpp)
target_compile_features(det2d_component PUBLIC cxx_std_17)
target_link_libraries(det2d_component
    trt_plugins
    yaml-cpp
)
ament_target_dependencies(det2d_component
    rclcpp
    rclcpp_components
    sensor_msgs
    cv_bridge
    OpenCV
    CUDA
    dev_pkg_interfaces
)
# Register the component
rclcpp_components_register_nodes(det2d_component "YoloDetector::Det2dNode")
install(TARGETS det2d_component
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# # --- Add executable ros2_image_crop_buffer_test ---
# add_executable(ros2_image_crop_buffer_test src/ros2_image_crop_buffer_test.cpp)
# target_link_libraries(ros2_image_crop_buffer_test
#     image_buffer_manager  # Links against the shared memory buffer library
#     proto_interfaces      # Links against the compiled protobuf messages library
#     ${Protobuf_LIBRARIES} # Links against the protobuf runtime library
#     rt                    # Needed for shared memory/semaphores
#     pthread               # Needed for threading/semaphores
#     # OpenCV is typically linked via ament_target_dependencies/cv_bridge
# )
# ament_target_dependencies(ros2_image_crop_buffer_test
#     rclcpp
#     message_filters       # For time synchronization
#     sensor_msgs
#     std_msgs
#     cv_bridge
#     OpenCV                # Explicitly add OpenCV
#     dev_pkg_interfaces
# )
# # Install ros2_image_crop_buffer_test
# install(TARGETS ros2_image_crop_buffer_test
#     DESTINATION lib/${PROJECT_NAME}
# )
# # ---------------------------------------------------------

# --- Add executable ros2_image_crop_buffer_component_test ---
add_library(ros2_image_crop_buffer_component_test SHARED src/ros2_image_crop_buffer_component_test.cpp)
target_link_libraries(ros2_image_crop_buffer_component_test
    image_buffer_manager  # Links against the shared memory buffer library
    proto_interfaces      # Links against the compiled protobuf messages library
    ${Protobuf_LIBRARIES} # Links against the protobuf runtime library
    rt                    # Needed for shared memory/semaphores
    pthread               # Needed for threading/semaphores
    # OpenCV is typically linked via ament_target_dependencies/cv_bridge
)
ament_target_dependencies(ros2_image_crop_buffer_component_test
    rclcpp
    rclcpp_components
    message_filters       # For time synchronization
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV                # Explicitly add OpenCV
    dev_pkg_interfaces
)
# Register the component
rclcpp_components_register_nodes(ros2_image_crop_buffer_component_test "Det2dWorker::ImageCropperNode")
install(TARGETS ros2_image_crop_buffer_component_test
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)
# ---------------------------------------------------------

# --- Add executable img_buffer_check_test ---
add_executable(img_buffer_check_test src/img_buffer_check_test.cpp)
target_link_libraries(img_buffer_check_test
    image_buffer_manager  # Links against the shared memory buffer library
    rt                    # Needed for shared memory/semaphores
    pthread               # Needed for threading/semaphores
)
ament_target_dependencies(img_buffer_check_test
    rclcpp
    sensor_msgs
    cv_bridge
    OpenCV
)
# Install the executable
install(TARGETS img_buffer_check_test
  DESTINATION lib/${PROJECT_NAME}
)
# ---------------------------------------------------------

# Add executable ros2_vehicle_det_test
add_executable(ros2_vehicle_det_test ${SRC_PATH}/ros2_vehicle_det_test.cpp)
target_link_libraries(ros2_vehicle_det_test
    trt_plugins
    proto_interfaces
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_vehicle_det_test
    rclcpp
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
install(TARGETS ros2_vehicle_det_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add executable ros2_vehicle_det_detr_test
add_executable(ros2_vehicle_det_detr_test ${SRC_PATH}/ros2_vehicle_det_detr_test.cpp)
target_link_libraries(ros2_vehicle_det_detr_test
    trt_plugins
    proto_interfaces
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_vehicle_det_detr_test
    rclcpp
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
install(TARGETS ros2_vehicle_det_detr_test
    DESTINATION lib/${PROJECT_NAME}
)

# Add the vehicle detection component library
add_library(ros2_vehicle_det_component_test SHARED ${SRC_PATH}/ros2_vehicle_det_component_test.cpp)
target_compile_features(ros2_vehicle_det_component_test PUBLIC cxx_std_17)
target_link_libraries(ros2_vehicle_det_component_test
    trt_plugins
    proto_interfaces
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_vehicle_det_component_test
    rclcpp
    rclcpp_components
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Register the component
rclcpp_components_register_nodes(ros2_vehicle_det_component_test "Det2dWorker::VehicleDetNode")
install(TARGETS ros2_vehicle_det_component_test
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# Add the vehicle detection detr component library
add_library(ros2_vehicle_det_detr_component_test SHARED ${SRC_PATH}/ros2_vehicle_det_detr_component_test.cpp)
target_compile_features(ros2_vehicle_det_detr_component_test PUBLIC cxx_std_17)
target_link_libraries(ros2_vehicle_det_detr_component_test
    trt_plugins
    proto_interfaces
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_vehicle_det_detr_component_test
    rclcpp
    rclcpp_components
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Register the component
rclcpp_components_register_nodes(ros2_vehicle_det_detr_component_test "Det2dWorker::VehicleDetDetrNode")
install(TARGETS ros2_vehicle_det_detr_component_test
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)

# Add the helmet cls component library
add_library(ros2_helmet_cls_component_test SHARED ${SRC_PATH}/ros2_helmet_cls_component_test.cpp)
target_compile_features(ros2_helmet_cls_component_test PUBLIC cxx_std_17)
target_link_libraries(ros2_helmet_cls_component_test
    trt_plugins
    proto_interfaces
    ${Protobuf_LIBRARIES}
    rt
    pthread
)
ament_target_dependencies(ros2_helmet_cls_component_test
    rclcpp
    rclcpp_components
    sensor_msgs
    std_msgs
    cv_bridge
    OpenCV
    CUDA
)
# Register the component
rclcpp_components_register_nodes(ros2_helmet_cls_component_test "Det2dWorker::HelmetClsNode")
install(TARGETS ros2_helmet_cls_component_test
  ARCHIVE DESTINATION lib
  LIBRARY DESTINATION lib
  RUNTIME DESTINATION bin
)


# Install config directory for dev_pkg_det2d
install(DIRECTORY config
    DESTINATION share/${PROJECT_NAME}
)

# Install calibration directory for dev_pkg_det2d
install(DIRECTORY calibration
    DESTINATION share/${PROJECT_NAME}
)

# Install models directory for dev_pkg_det2d
install(DIRECTORY models
    DESTINATION share/${PROJECT_NAME}
)

# Install launch directory for dev_pkg_det2d
install(DIRECTORY launch
    DESTINATION share/${PROJECT_NAME}
)


if(BUILD_TESTING)
  find_package(ament_lint_auto REQUIRED)
  # the following line skips the linter which checks for copyrights
  # comment the line when a copyright and license is added to all source files
  set(ament_cmake_copyright_FOUND TRUE)
  # the following line skips cpplint (only works in a git repo)
  # comment the line when this package is in a git repo and when
  # a copyright and license is added to all source files
  set(ament_cmake_cpplint_FOUND TRUE)
  ament_lint_auto_find_test_dependencies()
endif()

ament_package()
