#include <iostream>

#include <string>
#include <vector>
#include "assert.h"

class VehicleColorLabels {
public:
  VehicleColorLabels()
  {
    for( int i=0 ; i<21; i++ )
    {
      std::string x = "NA";
      mLabels.push_back( x );
    }

    {
      mLabels[0]= "bus";
      mLabels[1]= "car";
      mLabels[2]= "engineering truck";
      mLabels[3]= "truck";
      mLabels[4]= "police car";
      mLabels[5]= "ambulance";
      mLabels[6]= "mixer";
      mLabels[7]= "null";
      mLabels[8]= "slagcar";
      mLabels[9]= "fire engine";
      mLabels[10]= "white";
      mLabels[11]= "gray";
      mLabels[12]= "red";
      mLabels[13]= "yellow";
      mLabels[14]= "brown";
      mLabels[15]= "blue";
      mLabels[16]= "black";
      mLabels[17]= "green";
      mLabels[18]= "purple";
      mLabels[19]= "pink";
      mLabels[20]= "other";
    }
  }

  std::string imagenet_labelstring( int i ) {
    assert( i>=0 && i<21 );
    return mLabels[i];
  }

private:
  std::vector<std::string> mLabels;

};