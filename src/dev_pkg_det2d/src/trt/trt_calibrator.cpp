#include "NvInfer.h"
#include "trt_calibrator.hpp"
#include "utils.hpp"
#include "trt_logger.hpp"
#include "trt_preprocess.hpp"

#include <fstream>
#include <vector>
#include <algorithm>
#include <iterator>
#include <opencv2/core/core.hpp>
#include <opencv2/highgui/highgui.hpp>

using namespace std;
using namespace nvinfer1;

namespace model{

void Int8EntropyCalibrator::preprocess_cpu_vehicle_det(cv::Mat &h_src, float* d_tar, const int& tar_h, const int& tar_w) {
    // 检查输入图像
    if (h_src.data == nullptr) {
        LOG("ERROR: Image data is null! Skipping preprocessing");
        return;
    }

    /* ------------------------------------方法1. letterbox------------------------------------- */
    // // 检测模型的预处理 - 类似于Detector::preprocess_cpu中的逻辑
    // // 实现letterbox处理
    // int   input_w  = h_src.cols;
    // int   input_h  = h_src.rows;
    // int   target_w = tar_w;
    // int   target_h = tar_h;
    // float scale    = std::min(float(target_w)/input_w, float(target_h)/input_h);
    // int   new_w    = int(input_w * scale);
    // int   new_h    = int(input_h * scale);

    // // 创建目标图像并填充背景
    // cv::Mat tar(target_h, target_w, CV_8UC3, cv::Scalar(114, 114, 114)); // 使用114作为填充值(常见于YOLO)
    // cv::Mat resized_img;
    // cv::resize(h_src, resized_img, cv::Size(new_w, new_h));

    // // 寻找resize后的图片在背景中的位置
    // int x = (target_w - new_w) / 2;
    // int y = (target_h - new_h) / 2;
    // cv::Rect roi(x, y, new_w, new_h);

    // // 将调整大小的图像放入目标图像的中心
    // cv::Mat roiOfTar = tar(roi);
    // resized_img.copyTo(roiOfTar);

    // // 进行normalization和BGR2RGB, NHWC->NCHW
    // int imgArea = target_h * target_w;
    // float* h_buffer = new float[3 * imgArea];
    
    // for (int i = 0; i < target_h; i++) {
    //     for (int j = 0; j < target_w; j++) {
    //         int index = i * target_w * 3 + j * 3;
    //         // BGR转RGB并归一化
    //         h_buffer[0 * imgArea + i * target_w + j] = tar.data[index + 2] / 255.0f; // R
    //         h_buffer[1 * imgArea + i * target_w + j] = tar.data[index + 1] / 255.0f; // G
    //         h_buffer[2 * imgArea + i * target_w + j] = tar.data[index + 0] / 255.0f; // B
    //     }
    // }
    /* ----------------------------------------------------------------------------------------- */

    /* ------------------------------------方法2. 直接resize------------------------------------- */
     // 直接调整图像大小，不使用letterbox
     cv::Mat resized_img;
     cv::resize(h_src, resized_img, cv::Size(tar_w, tar_h), 0, 0, cv::INTER_LINEAR);
 
     // 进行normalization和BGR2RGB, NHWC->NCHW
     int imgArea = tar_h * tar_w;
     float* h_buffer = new float[3 * imgArea];
     
     for (int i = 0; i < tar_h; i++) {
         for (int j = 0; j < tar_w; j++) {
             int index = i * tar_w * 3 + j * 3;
             // BGR转RGB并归一化
             h_buffer[0 * imgArea + i * tar_w + j] = resized_img.data[index + 2] / 255.0f; // R
             h_buffer[1 * imgArea + i * tar_w + j] = resized_img.data[index + 1] / 255.0f; // G
             h_buffer[2 * imgArea + i * tar_w + j] = resized_img.data[index + 0] / 255.0f; // B
         }
     }
     /* ----------------------------------------------------------------------------------------- */

    // 将处理后的数据复制到目标内存
    CUDA_CHECK(cudaMemcpy(d_tar, h_buffer, 3 * imgArea * sizeof(float), cudaMemcpyHostToDevice));
    
    delete[] h_buffer;
}

void Int8EntropyCalibrator::preprocess_cpu_fisheye_det(cv::Mat &h_src, float* d_tar, const int& tar_h, const int& tar_w) {
    // 检查输入图像
    if (h_src.data == nullptr) {
        LOG("ERROR: Image data is null! Skipping preprocessing");
        return;
    }

    // 分类模型的预处理 - 类似于Classifier::preprocess_cpu中的逻辑
    // 获取mean, std (ImageNet标准值)
    float mean[]       = {0.406, 0.456, 0.485};
    float std[]        = {0.225, 0.224, 0.229};

    // 调整图像大小
    cv::Mat resized_img;
    cv::resize(h_src, resized_img, cv::Size(tar_w, tar_h), 0, 0, cv::INTER_LINEAR);

    // 进行normalization和BGR2RGB, NHWC->NCHW
    int imgArea = tar_h * tar_w;
    float* h_buffer = new float[3 * imgArea];
    
    for (int i = 0; i < tar_h; i++) {
        for (int j = 0; j < tar_w; j++) {
            // 原图NHWC布局中的像素索引（BGR顺序）
            const int src_index = (i * tar_w + j) * 3;
            // 目标NCHW布局中的像素索引（RGB顺序）
            const int dst_pos = i * tar_w + j;
            
            // BGR转RGB并应用均值和标准差归一化
            h_buffer[0 * imgArea + dst_pos] = // R通道
                (resized_img.data[src_index + 2] / 255.0f - mean[2]) / std[2];
            h_buffer[1 * imgArea + dst_pos] = // G通道
                (resized_img.data[src_index + 1] / 255.0f - mean[1]) / std[1];
            h_buffer[2 * imgArea + dst_pos] = // B通道
                (resized_img.data[src_index + 0] / 255.0f - mean[0]) / std[0];
        }
    }

    // 将处理后的数据复制到目标内存
    CUDA_CHECK(cudaMemcpy(d_tar, h_buffer, 3 * imgArea * sizeof(float), cudaMemcpyHostToDevice));
    
    delete[] h_buffer;
}

void Int8EntropyCalibrator::preprocess_cpu_helmet_cls(cv::Mat &h_src, float* d_tar, const int& tar_h, const int& tar_w) {
    // 检查输入图像
    if (h_src.data == nullptr) {
        LOG("ERROR: Image data is null! Skipping preprocessing");
        return;
    }

    // 检测模型的预处理 - 类似于Detector::preprocess_cpu中的逻辑
    // 实现letterbox处理
    int   input_w  = h_src.cols;
    int   input_h  = h_src.rows;
    int   target_w = tar_w;
    int   target_h = tar_h;
    float scale    = std::min(float(target_w)/input_w, float(target_h)/input_h);
    int   new_w    = int(input_w * scale);
    int   new_h    = int(input_h * scale);

    // 创建目标图像并填充背景
    cv::Mat tar(target_h, target_w, CV_8UC3, cv::Scalar(0, 0, 0)); // 使用114作为填充值(常见于YOLO)
    cv::Mat resized_img;
    cv::resize(h_src, resized_img, cv::Size(new_w, new_h));

    // 寻找resize后的图片在背景中的位置
    int x = (target_w - new_w) / 2;
    int y = (target_h - new_h) / 2;
    cv::Rect roi(x, y, new_w, new_h);

    // 将调整大小的图像放入目标图像的中心
    cv::Mat roiOfTar = tar(roi);
    resized_img.copyTo(roiOfTar);

    // 进行normalization和BGR2RGB, NHWC->NCHW
    int imgArea = target_h * target_w;
    float* h_buffer = new float[3 * imgArea];
    
    for (int i = 0; i < target_h; i++) {
        for (int j = 0; j < target_w; j++) {
            int index = i * target_w * 3 + j * 3;
            // BGR转RGB并归一化
            h_buffer[0 * imgArea + i * target_w + j] = tar.data[index + 2] / 255.0f; // R
            h_buffer[1 * imgArea + i * target_w + j] = tar.data[index + 1] / 255.0f; // G
            h_buffer[2 * imgArea + i * target_w + j] = tar.data[index + 0] / 255.0f; // B
        }
    }

    // 将处理后的数据复制到目标内存
    CUDA_CHECK(cudaMemcpy(d_tar, h_buffer, 3 * imgArea * sizeof(float), cudaMemcpyHostToDevice));
    
    delete[] h_buffer;
}

void Int8EntropyCalibrator::preprocess_cpu_plate_det(cv::Mat &h_src, float* d_tar, const int& tar_h, const int& tar_w) {
    // 检查输入图像
    if (h_src.data == nullptr) {
        LOG("ERROR: Image data is null! Skipping preprocessing");
        return;
    }

    // 检测模型的预处理 - 类似于Detector::preprocess_cpu中的逻辑
    // 实现letterbox处理
    int   input_w  = h_src.cols;
    int   input_h  = h_src.rows;
    int   target_w = tar_w;
    int   target_h = tar_h;
    float scale    = std::min(float(target_w)/input_w, float(target_h)/input_h);
    int   new_w    = int(input_w * scale);
    int   new_h    = int(input_h * scale);

    // 创建目标图像并填充背景
    cv::Mat tar(target_h, target_w, CV_8UC3, cv::Scalar(114, 114, 114)); // 使用114作为填充值(常见于YOLO)
    cv::Mat resized_img;
    cv::resize(h_src, resized_img, cv::Size(new_w, new_h));

    // 寻找resize后的图片在背景中的位置
    int x = (target_w - new_w) / 2;
    int y = (target_h - new_h) / 2;
    cv::Rect roi(x, y, new_w, new_h);

    // 将调整大小的图像放入目标图像的中心
    cv::Mat roiOfTar = tar(roi);
    resized_img.copyTo(roiOfTar);

    // 进行normalization和BGR2RGB, NHWC->NCHW
    int imgArea = target_h * target_w;
    float* h_buffer = new float[3 * imgArea];
    
    for (int i = 0; i < target_h; i++) {
        for (int j = 0; j < target_w; j++) {
            int index = i * target_w * 3 + j * 3;
            // BGR转RGB并归一化
            h_buffer[0 * imgArea + i * target_w + j] = tar.data[index + 2] / 255.0f; // R
            h_buffer[1 * imgArea + i * target_w + j] = tar.data[index + 1] / 255.0f; // G
            h_buffer[2 * imgArea + i * target_w + j] = tar.data[index + 0] / 255.0f; // B
        }
    }

    // 将处理后的数据复制到目标内存
    CUDA_CHECK(cudaMemcpy(d_tar, h_buffer, 3 * imgArea * sizeof(float), cudaMemcpyHostToDevice));
    
    delete[] h_buffer;
}

void Int8EntropyCalibrator::preprocess_cpu_vehicle_cls(cv::Mat &h_src, float* d_tar, const int& tar_h, const int& tar_w) {
    // 检查输入图像
    if (h_src.data == nullptr) {
        LOG("ERROR: Image data is null! Skipping preprocessing");
        return;
    }

    // 分类模型的预处理 - 类似于Classifier::preprocess_cpu中的逻辑
    // 获取mean, std (ImageNet标准值)
    float mean[] = {0.406, 0.456, 0.485};
    float std[]  = {0.225, 0.224, 0.229};

    // 调整图像大小
    cv::Mat resized_img;
    cv::resize(h_src, resized_img, cv::Size(tar_w, tar_h), 0, 0, cv::INTER_LINEAR);

    // 进行normalization和BGR2RGB, NHWC->NCHW
    int imgArea = tar_h * tar_w;
    float* h_buffer = new float[3 * imgArea];
    
    for (int i = 0; i < tar_h; i++) {
        for (int j = 0; j < tar_w; j++) {
            // 原图NHWC布局中的像素索引（BGR顺序）
            const int src_index = (i * tar_w + j) * 3;
            // 目标NCHW布局中的像素索引（RGB顺序）
            const int dst_pos = i * tar_w + j;
            
            // BGR转RGB并应用均值和标准差归一化
            h_buffer[0 * imgArea + dst_pos] = // R通道
                (resized_img.data[src_index + 2] / 255.0f - mean[2]) / std[2];
            h_buffer[1 * imgArea + dst_pos] = // G通道
                (resized_img.data[src_index + 1] / 255.0f - mean[1]) / std[1];
            h_buffer[2 * imgArea + dst_pos] = // B通道
                (resized_img.data[src_index + 0] / 255.0f - mean[0]) / std[0];
        }
    }

    // 将处理后的数据复制到目标内存
    CUDA_CHECK(cudaMemcpy(d_tar, h_buffer, 3 * imgArea * sizeof(float), cudaMemcpyHostToDevice));
    
    delete[] h_buffer;
}

/*
 * calibrator的构造函数
 * 我们在这里把calibration所需要的数据集准备好，需要保证数据集的数量可以被batchSize整除
 * 同时由于calibration是在device上进行的，所以需要分配空间
 */
Int8EntropyCalibrator::Int8EntropyCalibrator(
    const int&    batchSize,
    const string& calibrationDataPath,
    const string& calibrationTablePath,
    const int&    inputSize,
    const int&    inputH,
    const int&    inputW,
    const task_type& taskType):

    m_batchSize(batchSize),
    m_inputH(inputH),
    m_inputW(inputW),
    m_inputSize(inputSize),
    m_inputCount(batchSize * inputSize),
    m_calibrationTablePath(calibrationTablePath),
    m_taskType(taskType)
{
    m_imageList = loadDataList(calibrationDataPath);
    m_imageList.resize(static_cast<int>(m_imageList.size() / m_batchSize) * m_batchSize);
    std::random_shuffle(m_imageList.begin(), m_imageList.end(), 
                        [](int i){ return rand() % i; });
    CUDA_CHECK(cudaMalloc(&m_deviceInput, m_inputCount * sizeof(float)));
}

/*
 * 获取做calibration的时候的一个batch的图片，之后上传到device上
 * 需要注意的是，这里面的一个batch中的每一个图片，都需要做与真正推理是一样的前处理
 * 这里面我们选择在GPU上进行前处理，所以处理万
 */
bool Int8EntropyCalibrator::getBatch(
    void* bindings[], const char* names[], int nbBindings) noexcept
{
    // 检查是否已经处理完所有图像
    if (m_imageIndex >= m_imageList.size()) {
        return false;
    }

    // 计算当前批次实际大小（可能小于m_batchSize，如果是最后一个不完整批次）
    int batchSize = std::min(m_batchSize, static_cast<int>(m_imageList.size() - m_imageIndex));
    
    LOG("  Processing calibration batch %d/%d (total %d images)", 
        (m_imageIndex / m_batchSize) + 1, 
        (m_imageList.size() + m_batchSize - 1) / m_batchSize,
        batchSize);

    /*
     * 对一个batch里的所有图像进行预处理
     * 这里可有以及个扩展的点
     *  1. 可以把这个部分做成函数，以函数指针的方式传给calibrator。因为不同的task会有不同的预处理
     *  2. 可以实现一个bacthed preprocess
     * 这里留给当作今后的TODO
     */
    cv::Mat input_image;
    for (int i = 0; i < batchSize; i++) {
        // 获取当前图像路径并读取图像
        std::string imagePath = m_imageList.at(m_imageIndex);
        LOG("  %3d/%3d (%3dx%3d): %s", 
            m_imageIndex + 1, m_imageList.size(), m_inputH, m_inputW, imagePath.c_str());
            
        input_image = cv::imread(imagePath);
        if (input_image.empty()) {
            LOG("错误: 无法读取图像: %s", imagePath.c_str());
            // 跳过这张图片，继续处理下一张
            m_imageIndex++;
            continue;
        }
        
        // 根据任务类型选择不同的预处理方法
        // 这里假设有一个成员变量m_taskType来指示当前的任务类型
        // 如果没有这个变量，您可以添加一个枚举类型和相应的成员变量
        if (m_taskType == task_type::VEHICLE_DETECTION) {
            // 使用检测模型的预处理
            preprocess_cpu_vehicle_det(
                input_image,
                m_deviceInput + i * m_inputSize,
                m_inputH, m_inputW);
        } else if (m_taskType == task_type::FISHEYE_DETECTION_DETR) {
            // 使用检测模型的预处理
            preprocess_cpu_fisheye_det(
                input_image,
                m_deviceInput + i * m_inputSize,
                m_inputH, m_inputW);
        } else if (m_taskType == task_type::PLATE_DETECTION) {
            // 使用检测模型的预处理
            preprocess_cpu_plate_det(
                input_image,
                m_deviceInput + i * m_inputSize,
                m_inputH, m_inputW);
        } else if (m_taskType == task_type::VEHICLE_CLASSIFICATION) {
            // 使用分类模型的预处理
            preprocess_cpu_vehicle_cls(
                input_image,
                m_deviceInput + i * m_inputSize,
                m_inputH, m_inputW);
        } else {
            // 默认使用GPU预处理
            preprocess::preprocess_resize_gpu(
                input_image, 
                m_deviceInput + i * m_inputSize,
                m_inputH, m_inputW, 
                preprocess::tactics::GPU_BILINEAR_CENTER);
        }
        
        // 更新图像索引
        m_imageIndex++;
    }

    bindings[0] = m_deviceInput;

    return true;
}
    
/* 
 * 读取calibration table的信息来创建INT8的推理引擎, 
 * 将calibration table的信息存储到calibration cache，这样可以防止每次创建int推理引擎的时候都需要跑一次calibration
 * 如果没有calibration table的话就会直接跳过这一步，之后调用writeCalibrationCache来创建calibration table
 */
const void* Int8EntropyCalibrator::readCalibrationCache(size_t& length) noexcept
{
    void* output;
    m_calibrationCache.clear();

    ifstream input(m_calibrationTablePath, ios::binary);
    input >> noskipws;
    if (m_readCache && input.good())
        copy(istream_iterator<char>(input), istream_iterator<char>(), back_inserter(m_calibrationCache));

    length = m_calibrationCache.size();
    if (length){
        LOG("Using cached calibration table to build INT8 trt engine...");
        output = &m_calibrationCache[0];
    }else{
        LOG("Creating new calibration table to build INT8 trt engine...");
        output = nullptr;
    }
    return output;
}

/* 
 * 将calibration cache的信息写入到calibration table中
*/
void Int8EntropyCalibrator::writeCalibrationCache(const void* cache, size_t length) noexcept
{
    ofstream output(m_calibrationTablePath, ios::binary);
    output.write(reinterpret_cast<const char*>(cache), length);
    output.close();
}

} // namespace model
