from launch import LaunchDescription
from launch_ros.actions import ComposableNodeContainer
from launch_ros.descriptions import ComposableNode
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

# 视频发布节点配置参数
video_publisher_para = {
    'video_path': "/workspace/test/shizhe.ma/ros2-sensor-drv/src/dev_ws_det2d/src/dev_pkg_det2d/data/case/0514/vru/R39_Bn_CamW__102_rss-ai-prd-suz-wt-gpu30_1920X1080_2025-05-14-07-29-59-898.mp4",
    'topic_name': "/c0",
    'publish_rate': 60.0,
    'loop_video': False
}

# 2D感知节点配置参数 log_level: 0: FATAL, 1: ERROR, 2: WARN
vehicle_det_para = {
    'model_path': "/workspace/test/shizhe.ma/ros2-sensor-drv/src/dev_ws_det2d/src/dev_pkg_det2d/models/onnx/vehicle_det_detr_batch4.onnx",
    'model_path_cls': "/workspace/test/shizhe.ma/ros2-sensor-drv/src/dev_ws_det2d/src/dev_pkg_det2d/models/onnx/vehicle_cls_batch8.onnx",
    'input_topics': ["/c0", "c1", "c2", "c3"],
    'output_topics': ["/o0", "o1", "o2", "o3"],
    'inference_rate': 5.0,
    'confidence_threshold': 0.6,
    'max_queue_size': 1,
    'max_batch_size': 8,
    'group_names': ["g0", "g1", "g2", "g3"],
    'log_level': 2
}

def generate_launch_description():
    # Create the composable node container
    container = ComposableNodeContainer(
        name='vehicle_detection_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dev_pkg_tools',
                plugin='Dev2dTools::VideoPublisherComponent',
                name='video_publisher_node',
                parameters=[
                    video_publisher_para
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),
            ComposableNode(
                package='dev_pkg_det2d',
                plugin='Det2dWorker::VehicleDetDetrNode',
                name='vehicle_det_node',
                parameters=[
                    vehicle_det_para
                ],
                extra_arguments=[{'use_intra_process_comms': True}]
            )
        ],
        output='screen',
    )
    
    return LaunchDescription([
        container
    ])
