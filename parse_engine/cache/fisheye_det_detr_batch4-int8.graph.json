{"Layers": [{"Name": "/backbone/backbone.0/encoder/encoder/embeddings/Cast", "LayerType": "NoOp", "Inputs": [{"Name": "input", "Location": "<PERSON><PERSON>", "Dimensions": [4, 3, 560, 560], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/backbone/backbone.0/encoder/encoder/embeddings/Cast_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 3, 560, 560], "Format/Datatype": "Row major linear FP32"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "Reformatting CopyNode for Input Tensor 0 to /backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv", "LayerType": "Reformat", "Inputs": [{"Name": "/backbone/backbone.0/encoder/encoder/embeddings/Cast_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 3, 560, 560], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 3, 560, 560], "Format/Datatype": "Four wide channel vectorized row major Int8 format"}], "ParameterType": "Reformat", "Origin": "REFORMAT", "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 3, 560, 560], "Format/Datatype": "Four wide channel vectorized row major Int8 format"}], "Outputs": [{"Name": "/backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 384, 40, 40], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "Convolution", "Kernel": [14, 14], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [0, 0], "PostPadding": [0, 0], "Stride": [14, 14], "Dilation": [1, 1], "OutMaps": 384, "Groups": 1, "Weights": {"Type": "Int8", "Count": 225792}, "Bias": {"Type": "Float", "Count": 384}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 1, "HasReLU": 0, "TacticName": "ampere_fp32_icudnn_int8x4_128x64_relu_interior_nn_v1", "TacticValue": "0xff6944b17d5b2e32", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv]"}, {"Name": "{ForeignNode[backbone.0.encoder.encoder.encoder.layer.0.norm1.weight + (Unnamed Layer* 48) [Shuffle].../backbone/backbone.0/projector/Concat]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/encoder/encoder/embeddings/patch_embeddings/projection/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 384, 40, 40], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/Concat_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1536, 40, 40], "Format/Datatype": "Row major linear FP32"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "Reformatting CopyNode for Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv", "LayerType": "Reformat", "Inputs": [{"Name": "/backbone/backbone.0/projector/Concat_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1536, 40, 40], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1536, 40, 40], "Format/Datatype": "Thirty-two wide channel vectorized row major Int8 format"}], "ParameterType": "Reformat", "Origin": "REFORMAT", "TacticValue": "0x00000000000003ea", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1536, 40, 40], "Format/Datatype": "Thirty-two wide channel vectorized row major Int8 format"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Thirty-two wide channel vectorized row major Int8 format"}], "ParameterType": "Convolution", "Kernel": [1, 1], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [0, 0], "PostPadding": [0, 0], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 256, "Groups": 1, "Weights": {"Type": "Int8", "Count": 393216}, "Bias": {"Type": "Float", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_interleaved_i8i8_i8i32_f32_nchw_vect_c_32kcrs_vect_c_32_nchw_vect_c_32_tilesize192x128x64_stage3_warpsize4x2x1_g1_tensor16x8x32_simple_t1r1s1", "TacticValue": "0xb2cc5e08f6b66610", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv]"}, {"Name": "Reformatting CopyNode for Input Tensor 0 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "LayerType": "Reformat", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Thirty-two wide channel vectorized row major Int8 format"}], "Outputs": [{"Name": "Reformatted Input Tensor 0 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Reformat", "Origin": "REFORMAT", "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "Reformatted Input Tensor 0 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}, {"Name": "Reformatted Output Tensor 1 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "Reformatting CopyNode for Output Tensor 1 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "LayerType": "NoOp", "Inputs": [{"Name": "Reformatted Output Tensor 1 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/Split_132]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/Split_output_1", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 2 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "Reformatting CopyNode for Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv", "LayerType": "NoOp", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/Split_output_1", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 2 == 0"}], "Outputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "Reformatted Input Tensor 0 to /backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv]"}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/act/Mul]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/conv/Conv]"}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/act/Mul]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/conv/Conv]"}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/act/Mul]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/conv/Conv]"}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/act/Mul]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/conv/Conv]"}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/bn/ReduceMean.../backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/act/Mul]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv2/conv/Conv", "LayerType": "CaskConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [3, 3], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [1, 1], "PostPadding": [1, 1], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 128, "Groups": 1, "Weights": {"Type": "Half", "Count": 147456}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "HasBias": 0, "HasReLU": 0, "TacticName": "sm80_xmma_fprop_implicit_gemm_f16f16_f16f16_f16_nhwckrsc_nhwc_tilesize128x128x32_stage4_warpsize2x2x1_g1_tensor16x8x16_t1r3s3", "TacticValue": "0x60c3421152ef8e10", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv2/conv/Conv]"}, {"Name": "Reformatting CopyNode for Input Tensor 2 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/Split.../backbone/backbone.0/projector/stages.0/stages.0.0/Concat]}", "LayerType": "NoOp", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/Split_output_1", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 2 == 0"}], "Outputs": [{"Name": "Reformatted Input Tensor 2 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/Split.../backbone/backbone.0/projector/stages.0/stages.0.0/Concat]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "{ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/Split.../backbone/backbone.0/projector/stages.0/stages.0.0/Concat]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv1/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.2/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}, {"Name": "Reformatted Input Tensor 2 to {ForeignNode[/backbone/backbone.0/projector/stages.0/stages.0.0/Split.../backbone/backbone.0/projector/stages.0/stages.0.0/Concat]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.0/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/m.1/cv2/act/Mul_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 128, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/Concat_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 640, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv2/conv/Conv", "LayerType": "CaskGemmConvolution", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/Concat_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 640, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "ParameterType": "Convolution", "Kernel": [1, 1], "PaddingMode": "kEXPLICIT_ROUND_DOWN", "PrePadding": [0, 0], "PostPadding": [0, 0], "Stride": [1, 1], "Dilation": [1, 1], "OutMaps": 256, "Groups": 1, "Weights": {"Type": "Half", "Count": 163840}, "Bias": {"Type": "Half", "Count": 0}, "HasSparseWeights": 0, "HasDynamicFilter": 0, "HasDynamicBias": 0, "HasResidual": 0, "ConvXAsActInputIdx": -1, "BiasAsActInputIdx": -1, "ResAsActInputIdx": -1, "Activation": "NONE", "TacticName": "ampere_h16816gemm_128x128_ldg8_stages_32x5_nn_v1", "TacticValue": "0x000000000002054c", "StreamId": 0, "Metadata": "[ONNX Layer: /backbone/backbone.0/projector/stages.0/stages.0.0/cv2/conv/Conv]"}, {"Name": "Reformatting CopyNode for Input Tensor 0 to {ForeignNode[/transformer/Constant_38_output_0 + (Unnamed Layer* 1389) [Shuffle].../transformer/decoder/Slice_1]}", "LayerType": "Reformat", "Inputs": [{"Name": "/backbone/backbone.0/projector/stages.0/stages.0.0/cv2/conv/Conv_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Channel major FP16 format where channel % 8 == 0"}], "Outputs": [{"Name": "Reformatted Input Tensor 0 to {ForeignNode[/transformer/Constant_38_output_0 + (Unnamed Layer* 1389) [Shuffle].../transformer/decoder/Slice_1]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "Reformat", "Origin": "REFORMAT", "TacticValue": "0x00000000000003ea", "StreamId": 0, "Metadata": ""}, {"Name": "{ForeignNode[/transformer/Constant_38_output_0 + (Unnamed Layer* 1389) [Shuffle].../transformer/decoder/Slice_1]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "Reformatted Input Tensor 0 to {ForeignNode[/transformer/Constant_38_output_0 + (Unnamed Layer* 1389) [Shuffle].../transformer/decoder/Slice_1]}", "Location": "<PERSON><PERSON>", "Dimensions": [4, 256, 40, 40], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/Concat_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1600, 256], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/Concat_11_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 4], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 4], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_11_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_10_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_8_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_7_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_5_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_4_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}, {"Name": "PWN(/transformer/decoder/Sin)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Sin_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iSin(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Sin]"}, {"Name": "PWN(/transformer/decoder/Cos)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Cos_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iCos(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Cos]"}, {"Name": "PWN(/transformer/decoder/Sin_1)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_4_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Sin_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iSin(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Sin_1]"}, {"Name": "PWN(/transformer/decoder/Cos_1)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_5_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Cos_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iCos(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Cos_1]"}, {"Name": "PWN(/transformer/decoder/Sin_2)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_7_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Sin_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iSin(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Sin_2]"}, {"Name": "PWN(/transformer/decoder/Cos_2)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_8_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Cos_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iCos(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Cos_2]"}, {"Name": "PWN(/transformer/decoder/Sin_3)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_10_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Sin_3_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iSin(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Sin_3]"}, {"Name": "PWN(/transformer/decoder/Cos_3)", "LayerType": "PointWiseV2", "Inputs": [{"Name": "/transformer/decoder/Slice_11_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "/transformer/decoder/Cos_3_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "ParameterType": "PointWise", "ParameterSubType": "PointWiseExpression", "NbInputArgs": 1, "InputArgs": ["arg0"], "NbOutputVars": 1, "OutputVars": ["var0"], "NbParams": 0, "Params": [], "NbLiterals": 0, "Literals": [], "NbOperations": 1, "Operations": ["auto const var0 = pwgen::iCos(arg0);"], "TacticValue": "0x0000000000000009", "StreamId": 0, "Metadata": "[ONNX Layer: /transformer/decoder/Cos_3]"}, {"Name": "{ForeignNode[onnx::MatMul_4326 + (Unnamed Layer* 1675) [Shuffle].../Concat]}", "LayerType": "<PERSON><PERSON>", "Inputs": [{"Name": "/transformer/Concat_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 1600, 256], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/Concat_11_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 4], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Slice_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 4], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Sin_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Cos_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Sin_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Cos_1_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Sin_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Cos_2_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Sin_3_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}, {"Name": "/transformer/decoder/Cos_3_output_0", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 64], "Format/Datatype": "Row major linear FP32"}], "Outputs": [{"Name": "pred_boxes", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 91], "Format/Datatype": "Row major linear FP32"}, {"Name": "pred_logits", "Location": "<PERSON><PERSON>", "Dimensions": [4, 300, 4], "Format/Datatype": "Row major linear FP32"}], "TacticValue": "0x0000000000000000", "StreamId": 0, "Metadata": ""}], "Bindings": ["input", "pred_logits", "pred_boxes"]}