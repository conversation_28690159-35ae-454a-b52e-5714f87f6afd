#include <cv_bridge/cv_bridge.h>
#include <dirent.h>    // For directory traversal
#include <sys/stat.h>  // For file status
#include <yaml-cpp/yaml.h>

#include <ament_index_cpp/get_package_share_directory.hpp>
#include <cstring>  // For std::strerror
#include <fstream>
#include <iostream>
#include <opencv2/opencv.hpp>
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_components/register_node_macro.hpp>
#include <sensor_msgs/msg/image.hpp>

#include "dev_pkg_interfaces/msg/bbox.hpp"
#include "dev_pkg_interfaces/msg/bbox_array.hpp"
#include "trt_logger.hpp"
#include "trt_model.hpp"
#include "trt_worker.hpp"
#include "utils.hpp"

namespace YoloDetector {

class Det2dNode : public rclcpp::Node {
 public:
  Det2dNode(const rclcpp::NodeOptions& options)
      : Node("det2d_node", options),
        last_message_time_(std::chrono::steady_clock::now()) {
    // Declare input and ouput parameters
    this->declare_parameter<std::string>("model_file", "");
    this->declare_parameter<bool>("is_use_drv_cfg", true);
    this->declare_parameter<std::string>("drv_cfg_file", "");
    // this->declare_parameter<std::string>("input_topic_name", "");
    this->declare_parameter<std::vector<std::string>>(
        "input_topic_names", std::vector<std::string>{});
    this->declare_parameter<std::string>("output_obj_topic_name", "");
    this->declare_parameter<std::string>("output_img_topic_name", "");
    this->declare_parameter<int>("rate_infer_all_camera", 1);
    // Get input and ouput parameters
    this->get_parameter("model_file", model_file_);
    this->get_parameter("is_use_drv_cfg", is_use_drv_cfg_);
    this->get_parameter("drv_cfg_file", drv_cfg_file_);
    // this->get_parameter("input_topic_name", input_topic_name_);
    this->get_parameter("input_topic_names", input_topic_names_);
    this->get_parameter("output_obj_topic_name", output_obj_topic_name_);
    this->get_parameter("output_img_topic_name", output_img_topic_name_);
    this->get_parameter("rate_infer_all_camera", rate_infer_all_camera_);
    // Log input and ouput parameters
    RCLCPP_INFO(this->get_logger(), "Model file: %s", model_file_.c_str());
    RCLCPP_INFO(this->get_logger(), "Is use drv cfg: %d", is_use_drv_cfg_);
    RCLCPP_INFO(this->get_logger(), "Drv cfg file: %s", drv_cfg_file_.c_str());
    // RCLCPP_INFO(this->get_logger(), "Input topic name: %s",
    //             input_topic_name_.c_str());
    for (auto input_topic_name : input_topic_names_) {
      RCLCPP_INFO(this->get_logger(), "Input topic name: %s",
                  input_topic_name.c_str());
    }
    RCLCPP_INFO(this->get_logger(), "Output object topic name: %s",
                output_obj_topic_name_.c_str());
    RCLCPP_INFO(this->get_logger(), "Output image topic name: %s",
                output_img_topic_name_.c_str());
    RCLCPP_INFO(this->get_logger(), "Rate infer all camera: %d",
                rate_infer_all_camera_);

    // Declare infer params
    logger::Level level;
    model::Params params;
    params.task = model::task_type::DETECTION;  // default task
    this->declare_parameter<int>("log_level", 4);
    this->declare_parameter<int>("img_height", 640);
    this->declare_parameter<int>("img_width", 640);
    this->declare_parameter<int>("img_channel", 3);
    this->declare_parameter<int>("pro_device", 0);
    this->declare_parameter<int>("model_precision", 0);
    this->declare_parameter<int>("model_type", 0);
    this->declare_parameter<std::string>("calib_data_file", "");
    this->declare_parameter<std::string>("calib_table_file", "");
    this->declare_parameter<float>("conf_thresh", 0.25);
    this->declare_parameter<float>("nms_thresh", 0.45);
    // Get infer params
    int temp_level = 4;
    this->get_parameter("log_level", temp_level);
    level = static_cast<logger::Level>(temp_level);
    this->get_parameter("img_height", params.img.h);
    this->get_parameter("img_width", params.img.w);
    this->get_parameter("img_channel", params.img.c);
    int temp_dev = 0;
    this->get_parameter("pro_device", temp_dev);
    params.dev = static_cast<model::device>(temp_dev);
    int temp_prec = 0;
    this->get_parameter("model_precision", temp_prec);
    params.prec = static_cast<model::precision>(temp_prec);
    int temp_model = 0;
    this->get_parameter("model_type", temp_model);
    params.det_model = static_cast<model::detection_model>(temp_model);
    this->get_parameter("calib_data_file", params.calib_data_file);
    this->get_parameter("calib_table_file", params.calib_table_file);
    this->get_parameter("conf_thresh", params.conf_thresh);
    this->get_parameter("nms_thresh", params.nms_thresh);
    // Log infer params
    RCLCPP_INFO(this->get_logger(), "Log level: %d", level);
    RCLCPP_INFO(this->get_logger(), "Image height: %d", params.img.h);
    RCLCPP_INFO(this->get_logger(), "Image width: %d", params.img.w);
    RCLCPP_INFO(this->get_logger(), "Image channels: %d", params.img.c);
    RCLCPP_INFO(this->get_logger(), "Processor device: %d", params.dev);
    RCLCPP_INFO(this->get_logger(), "Model precision: %d", params.prec);
    RCLCPP_INFO(this->get_logger(), "Model type: %d", params.det_model);
    RCLCPP_INFO(this->get_logger(), "Calibration data file: %s",
                params.calib_data_file.c_str());
    RCLCPP_INFO(this->get_logger(), "Calibration table file: %s",
                params.calib_table_file.c_str());
    RCLCPP_INFO(this->get_logger(), "Confidence threshold: %f",
                params.conf_thresh);
    RCLCPP_INFO(this->get_logger(), "NMS threshold: %f", params.nms_thresh);

    // // init infer params
    // auto level         = logger::Level::VERB;
    // auto params        = model::Params();
    // params.img         = {640, 640, 3};
    // params.task        = model::task_type::DETECTION;
    // params.dev         = model::device::CPU;
    // params.prec        = model::precision::INT8;  // INT8
    // params.det_model   = model::detection_model::YOLOV7;

    // create worker
    if (model_file_ != "") {
      m_worker_ = thread::create_worker(model_file_, level, params);
    }

    // init input topic names by drv cfg
    if (is_use_drv_cfg_) {
      input_topic_names_ = read_shm_keys(drv_cfg_file_.c_str());
    }
    RCLCPP_INFO(this->get_logger(),
                "Input topic names are being changed by drv cfg...");
    for (auto input_topic_name : input_topic_names_) {
      RCLCPP_INFO(this->get_logger(), "Input topic name: %s",
                  input_topic_name.c_str());
    }

    // read stream
    // if (input_topic_name_ != "") {
    //     subscriber_ = this->create_subscription<sensor_msgs::msg::Image>(
    //         input_topic_name_, 10,
    //         std::bind(&Det2dNode::read_stream_callback, this,
    //                 std::placeholders::_1));
    // }
    if (!input_topic_names_.empty()) {
      for (const auto& input_topic_name : input_topic_names_) {
        auto subscriber = this->create_subscription<sensor_msgs::msg::Image>(
            input_topic_name, 10,
            [this,
             input_topic_name](const sensor_msgs::msg::Image::SharedPtr msg) {
              this->read_stream_callback(msg, input_topic_name);
            });
        subscribers_.push_back(subscriber);
      }
    }

    // get results
    if (output_obj_topic_name_ != "") {
      obj_publisher_ =
          this->create_publisher<dev_pkg_interfaces::msg::BboxArray>(
              output_obj_topic_name_, 10);
    }
    if (output_img_topic_name_ != "") {
      img_publisher_ = this->create_publisher<sensor_msgs::msg::Image>(
          output_img_topic_name_, 10);
    }

    // start get results thread
    if (m_worker_ != nullptr) {
      get_results_thread_ = std::thread(&Det2dNode::get_results_callback, this);
    }

    // worker and monitor
    timer_ = this->create_wall_timer(
        std::chrono::milliseconds(1000 / rate_infer_all_camera_),
        std::bind(&Det2dNode::work_infer_callback, this));
  }
  ~Det2dNode() { stop(); }

  bool check_input_params() {
    bool ret = true;
    if (model_file_ == "") {
      RCLCPP_ERROR(this->get_logger(), "model_file is empty");
      ret = false;
    }
    // if (input_topic_name_ == "") {
    //     RCLCPP_ERROR(this->get_logger(), "input_topic_name is empty");
    //     ret = false;
    // }
    if (input_topic_names_.empty()) {
      RCLCPP_ERROR(this->get_logger(), "input_topic_names is empty");
      ret = false;
    }

    return ret;
  }

  void start() { rclcpp::spin(this->get_node_base_interface()); }
  void stop() {
    rclcpp::shutdown();
    if (get_results_thread_.joinable()) {
      get_results_thread_.join();
    }
  }

 private:
  std::vector<std::string> read_shm_keys(const std::string& config_file) {
    std::vector<std::string> shm_keys;
    // Check if the file exists and is accessible
    std::ifstream file(config_file);
    if (!file.good()) {
      LOGW((std::string("Unable to open config file: ") + config_file).c_str());
      return shm_keys;
    }
    file.close();
    // Load the YAML configuration
    YAML::Node config;
    try {
      config = YAML::LoadFile(config_file);
    } catch (const YAML::Exception& e) {
      LOGW((std::string("Failed to parse YAML file: ") + std::string(e.what()))
               .c_str());
      return shm_keys;
    }
    // Check if the expected keys are present
    if (!config["camera_used"] || !config["camera_used"].IsSequence()) {
      LOGW("Missing or invalid 'camera_used' key in config file.");
      return shm_keys;
    }
    // Extract shm_keys
    for (const auto& camera : config["camera_used"]) {
      if (!camera["sensor_name"]) {
        LOGW("Missing 'sensor_name' key in one of the camera entries.");
        continue;  // Skip this entry
      }
      shm_keys.push_back(camera["sensor_name"].as<std::string>());
    }
    return shm_keys;
  }

  void read_stream_callback(const sensor_msgs::msg::Image::SharedPtr msg,
                            const std::string& camera_name) {
    std::lock_guard<std::mutex> lock(camera_mutex_);
    last_message_time_ = std::chrono::steady_clock::now();
    cv_bridge::CvImagePtr cv_ptr;
    try {
      cv_ptr = cv_bridge::toCvCopy(msg, sensor_msgs::image_encodings::BGR8);
    } catch (cv_bridge::Exception& e) {
      RCLCPP_ERROR(this->get_logger(), "cv_bridge exception: %s", e.what());
      return;
    }
    // // debug: show input
    // cv::imshow("Video Frame", cv_ptr->image);
    // cv::waitKey(1);
    start_infer_timestamps_map_[camera_name] = msg->header.stamp.nanosec;
    images_map_[camera_name] = cv_ptr->image;
  }

  void get_results_callback() {
    while (rclcpp::ok()) {
      if (output_obj_topic_name_ != "" && output_img_topic_name_ == "") {
        for (auto& [camera_name, timestamp] : end_infer_timestamps_map_) {
          if (timestamp > 0) {
            std::vector<model::detector::bbox> objects;
            bool ret = m_worker_->get_obj_result(
                objects, camera_name + std::to_string(timestamp));
            if (!ret) {
              LOGD("Failed to get results, Status:%d", ret);
              continue;
            }

            // publish bbbox
            if (!objects.empty()) {
              auto message_bboxes = dev_pkg_interfaces::msg::BboxArray();
              message_bboxes.camera_name = camera_name;
              message_bboxes.timestamp_ns = timestamp;
              for (auto& object : objects) {
                auto message_bbox = dev_pkg_interfaces::msg::Bbox();
                message_bbox.x_left = object.x0;
                message_bbox.y_top = object.y0;
                message_bbox.width = object.x1 - object.x0;
                message_bbox.height = object.y1 - object.y0;
                message_bbox.confidence = object.confidence;
                message_bbox.class_id = object.label;
                message_bboxes.boxes.push_back(message_bbox);
              }
              obj_publisher_->publish(message_bboxes);
            }

            {
              std::lock_guard<std::mutex> lock(camera_mutex_);
              end_infer_timestamps_map_[camera_name] = 0;
            }
          }
        }
      }

      else if (output_img_topic_name_ != "" && output_obj_topic_name_ == "") {
        for (auto& [camera_name, timestamp] : end_infer_timestamps_map_) {
          if (timestamp > 0) {
            cv::Mat img;
            bool ret = m_worker_->get_img_result(
                img, camera_name + std::to_string(timestamp));
            if (!ret) {
              LOGD("Failed to get results, Status:%d", ret);
              continue;
            }
            if (!img.empty()) {
              auto msg =
                  cv_bridge::CvImage(std_msgs::msg::Header(), "bgr8", img)
                      .toImageMsg();
              msg->header.stamp.sec = timestamp / 1e9;
              msg->header.stamp.nanosec = timestamp;
              msg->header.frame_id = camera_name;
              img_publisher_->publish(*msg);
            }

            {
              std::lock_guard<std::mutex> lock(camera_mutex_);
              end_infer_timestamps_map_[camera_name] = 0;
            }
          }
        }
      }

      else if (output_obj_topic_name_ == "" && output_img_topic_name_ == "") {
        for (auto& [camera_name, timestamp] : end_infer_timestamps_map_) {
          if (timestamp > 0) {
            cv::Mat img;
            bool ret = m_worker_->get_img_result(
                img, camera_name + std::to_string(timestamp));
            if (!ret) {
              LOGD("Failed to get results, Status:%d", ret);
              continue;
            }
            if (!img.empty()) {
              // resize img to 1024*768
              cv::resize(img, img, cv::Size(1024, 768));
              cv::imshow("Detect-Img", img);
              cv::waitKey(1);
            }

            {
              std::lock_guard<std::mutex> lock(camera_mutex_);
              end_infer_timestamps_map_[camera_name] = 0;
            }
          }
        }
      }

      else {
        for (auto& [camera_name, timestamp] : end_infer_timestamps_map_) {
          if (timestamp > 0) {
            std::vector<model::detector::bbox> objects;
            bool ret = m_worker_->get_obj_result(
                objects, camera_name + std::to_string(timestamp));
            if (!ret) {
              LOGD("Failed to get results, Status:%d", ret);
              continue;
            }

            // publish bbbox
            if (!objects.empty()) {
              auto message_bboxes = dev_pkg_interfaces::msg::BboxArray();
              message_bboxes.camera_name = camera_name;
              message_bboxes.timestamp_ns = timestamp;
              for (auto& object : objects) {
                auto message_bbox = dev_pkg_interfaces::msg::Bbox();
                message_bbox.x_left = object.x0;
                message_bbox.y_top = object.y0;
                message_bbox.width = object.x1 - object.x0;
                message_bbox.height = object.y1 - object.y0;
                message_bbox.confidence = object.confidence;
                message_bbox.class_id = object.label;
                message_bboxes.boxes.push_back(message_bbox);
              }
              obj_publisher_->publish(message_bboxes);
            }

            cv::Mat img;
            ret = m_worker_->get_img_result(
                img, camera_name + std::to_string(timestamp));
            if (!ret) {
              LOGD("Failed to get results, Status:%d", ret);
              continue;
            }
            if (!img.empty()) {
              auto msg =
                  cv_bridge::CvImage(std_msgs::msg::Header(), "bgr8", img)
                      .toImageMsg();
              msg->header.stamp.sec = timestamp / 1e9;
              msg->header.stamp.nanosec = timestamp;
              msg->header.frame_id = camera_name;
              img_publisher_->publish(*msg);
            }

            {
              std::lock_guard<std::mutex> lock(camera_mutex_);
              end_infer_timestamps_map_[camera_name] = 0;
            }
          }
        }
      }
    }
  }

  void work_infer_callback() {
    std::lock_guard<std::mutex> lock(camera_mutex_);
    auto now = std::chrono::steady_clock::now();
    auto duration_since_last_message =
        std::chrono::duration_cast<std::chrono::seconds>(now -
                                                         last_message_time_);
    if (duration_since_last_message.count() > 5) {
      RCLCPP_WARN(this->get_logger(), "No messages received for 5 seconds.");
      // this->stop();
    }
    for (auto& [camera_name, timestamp] : start_infer_timestamps_map_) {
      if (timestamp > 0) {
        m_worker_->inference(camera_name + std::to_string(timestamp),
                             images_map_[camera_name]);
        end_infer_timestamps_map_[camera_name] = timestamp;
        start_infer_timestamps_map_[camera_name] = 0;
      } else {
        RCLCPP_WARN(this->get_logger(),
                    "The timestamp of input msg is 0, camera_name: %s",
                    camera_name.c_str());
      }
    }
  }

 private:
  // key: camera name, value: timestamp(nano)
  std::map<std::string, uint32_t> start_infer_timestamps_map_;
  std::map<std::string, uint32_t> end_infer_timestamps_map_;
  // key: camera name, value: latest image
  std::map<std::string, cv::Mat> images_map_;
  int rate_infer_all_camera_;
  std::mutex camera_mutex_;

  std::shared_ptr<thread::Worker> m_worker_ = nullptr;
  std::string model_file_ = "";
  bool is_use_drv_cfg_ = true;
  std::string drv_cfg_file_ = "";
  // std::string input_topic_name_ = "";
  std::vector<std::string> input_topic_names_;
  std::string output_obj_topic_name_ = "";
  std::string output_img_topic_name_ = "";

  // rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr subscriber_;
  std::vector<rclcpp::Subscription<sensor_msgs::msg::Image>::SharedPtr>
      subscribers_;
  rclcpp::Publisher<dev_pkg_interfaces::msg::BboxArray>::SharedPtr
      obj_publisher_;
  rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr img_publisher_;
  std::thread get_results_thread_;

  rclcpp::TimerBase::SharedPtr timer_;
  std::chrono::steady_clock::time_point last_message_time_;
};

}  // namespace YoloDetector

RCLCPP_COMPONENTS_REGISTER_NODE(YoloDetector::Det2dNode)

// // Signal handler function
// void signal_handler(int signum) { rclcpp::shutdown(); }

// int main(int argc, char** argv) {
//   // Register signal handler
//   signal(SIGINT, signal_handler);

//   rclcpp::init(argc, argv);
//   rclcpp::NodeOptions options;
//   // launch without parameters
//   try {
//     std::string share_directory =
//         ament_index_cpp::get_package_share_directory("dev_pkg_det2d");
//     std::string params_file =
//         share_directory + std::string("/config/params.yaml");
//     LOG(" Params file: %s", params_file.c_str());
//     options.arguments({"--ros-args", "--params-file", params_file.c_str()});
//   } catch (const std::exception& e) {
//     LOGW(" Error: %s", e.what());
//     return -1;
//   }
//   // options.arguments({"--ros-args", "--params-file",
//   //                    "src/dev_pkg_det2d/config/params.yaml"});

//   auto det2d_node = std::make_shared<Det2dNode>(options);
//   if (!det2d_node->check_input_params()) {
//     return -1;
//   }
//   det2d_node->start();
//   det2d_node->stop();

//   return 0;
// }