TRT-8602-EntropyCalibration2
input: 3c305a1f
conv2d_37.tmp_0: 3c33ef87
batch_norm_0.tmp_3: 3d3e9a18
(Unnamed Layer* 3) [Shuffle]_output: 3cc18f1e
p2o.Add.1: 3d95d845
p2o.Clip.1: 3d418f1e
p2o.Mul.1: 3e6aa4f8
(Unnamed Layer* 8) [Shuffle]_output: 3d418f1e
batch_norm_0.tmp_4: 3d1c6dfb
depthwise_conv2d_0.tmp_0: 3c1453ef
batch_norm_1.tmp_3: 3dabe98c
(Unnamed Layer* 13) [Shuffle]_output: 3cc18f1e
p2o.Add.3: 3dd10d92
p2o.Clip.3: 3d418f1e
p2o.Mul.3: 3f16bc74
(Unnamed Layer* 18) [Shuffle]_output: 3d418f1e
batch_norm_1.tmp_4: 3dc8fb46
conv2d_38.tmp_0: 3d3b73a7
batch_norm_2.tmp_3: 3e027c37
(Unnamed Layer* 23) [Shuffle]_output: 3cc18f1e
p2o.Add.5: 3e1f7270
p2o.Clip.5: 3d418f1e
p2o.Mul.5: 3f3d5313
(Unnamed Layer* 28) [Shuffle]_output: 3d418f1e
batch_norm_2.tmp_4: 3dfc6ec5
depthwise_conv2d_1.tmp_0: 3d0dc404
batch_norm_3.tmp_3: 3db64c7e
(Unnamed Layer* 33) [Shuffle]_output: 3cc18f1e
p2o.Add.7: 3de99532
p2o.Clip.7: 3d418f1e
p2o.Mul.7: 3eec049d
(Unnamed Layer* 38) [Shuffle]_output: 3d418f1e
batch_norm_3.tmp_4: 3d9d5869
conv2d_39.tmp_0: 3d167c18
batch_norm_4.tmp_3: 3e228d23
(Unnamed Layer* 43) [Shuffle]_output: 3cc18f1e
p2o.Add.9: 3e75a591
p2o.Clip.9: 3d418f1e
p2o.Mul.9: 3f11fddd
(Unnamed Layer* 48) [Shuffle]_output: 3d418f1e
batch_norm_4.tmp_4: 3dc2a7d1
depthwise_conv2d_2.tmp_0: 3c9c3747
batch_norm_5.tmp_3: 3e49c8ac
(Unnamed Layer* 53) [Shuffle]_output: 3cc18f1e
p2o.Add.11: 3e52eb92
p2o.Clip.11: 3d418f1e
p2o.Mul.11: 3f1580fc
(Unnamed Layer* 58) [Shuffle]_output: 3d418f1e
batch_norm_5.tmp_4: 3dc756a6
conv2d_40.tmp_0: 3db6af38
batch_norm_6.tmp_3: 3e5f758f
(Unnamed Layer* 63) [Shuffle]_output: 3cc18f1e
p2o.Add.13: 3e46df54
p2o.Clip.13: 3d418f1e
p2o.Mul.13: 3f48d6ba
(Unnamed Layer* 68) [Shuffle]_output: 3d418f1e
batch_norm_6.tmp_4: 3e05e47d
depthwise_conv2d_3.tmp_0: 3d037b32
batch_norm_7.tmp_3: 3dd61335
(Unnamed Layer* 73) [Shuffle]_output: 3cc18f1e
p2o.Add.15: 3de7f399
p2o.Clip.15: 3d418f1e
p2o.Mul.15: 3ee7c09c
(Unnamed Layer* 78) [Shuffle]_output: 3d418f1e
batch_norm_7.tmp_4: 3d9a8068
conv2d_41.tmp_0: 3d3ba33f
batch_norm_8.tmp_3: 3dcba8bc
(Unnamed Layer* 83) [Shuffle]_output: 3cc18f1e
p2o.Add.17: 3e17a0fa
p2o.Clip.17: 3d418f1e
p2o.Mul.17: 3ea67172
(Unnamed Layer* 88) [Shuffle]_output: 3d418f1e
batch_norm_8.tmp_4: 3d5dec98
depthwise_conv2d_4.tmp_0: 3c823b29
batch_norm_9.tmp_3: 3dd8ae85
(Unnamed Layer* 93) [Shuffle]_output: 3cc18f1e
p2o.Add.19: 3db96139
p2o.Clip.19: 3d418f1e
p2o.Mul.19: 3e9324c1
(Unnamed Layer* 98) [Shuffle]_output: 3d418f1e
batch_norm_9.tmp_4: 3d443101
conv2d_42.tmp_0: 3d03ea4b
batch_norm_10.tmp_3: 3d6fbd3a
(Unnamed Layer* 103) [Shuffle]_output: 3cc18f1e
p2o.Add.21: 3da2d2f2
p2o.Clip.21: 3d418f1e
p2o.Mul.21: 3e8205c2
(Unnamed Layer* 108) [Shuffle]_output: 3d418f1e
batch_norm_10.tmp_4: 3d2d5d01
depthwise_conv2d_5.tmp_0: 3c7feb19
batch_norm_11.tmp_3: 3d4235f1
(Unnamed Layer* 113) [Shuffle]_output: 3cc18f1e
p2o.Add.23: 3d90b98b
p2o.Clip.23: 3d418f1e
p2o.Mul.23: 3e6a286d
(Unnamed Layer* 118) [Shuffle]_output: 3d418f1e
batch_norm_11.tmp_4: 3d1c1af3
conv2d_43.tmp_0: 3cf5cadb
batch_norm_12.tmp_3: 3d6f878f
(Unnamed Layer* 123) [Shuffle]_output: 3cc18f1e
p2o.Add.25: 3d8aa106
p2o.Clip.25: 3d418f1e
p2o.Mul.25: 3ea65304
(Unnamed Layer* 128) [Shuffle]_output: 3d418f1e
batch_norm_12.tmp_4: 3d5dc405
depthwise_conv2d_6.tmp_0: 3c53dca6
batch_norm_13.tmp_3: 3d9b0c3c
(Unnamed Layer* 133) [Shuffle]_output: 3cc18f1e
p2o.Add.27: 3d925838
p2o.Clip.27: 3d418f1e
p2o.Mul.27: 3e53cfca
(Unnamed Layer* 138) [Shuffle]_output: 3d418f1e
batch_norm_13.tmp_4: 3d0d3531
conv2d_44.tmp_0: 3cdb696a
batch_norm_14.tmp_3: 3d765d03
(Unnamed Layer* 143) [Shuffle]_output: 3cc18f1e
p2o.Add.29: 3da9b3a6
p2o.Clip.29: 3d418f1e
p2o.Mul.29: 3e678c7d
(Unnamed Layer* 148) [Shuffle]_output: 3d418f1e
batch_norm_14.tmp_4: 3d1a5da8
depthwise_conv2d_7.tmp_0: 3c95bb96
batch_norm_15.tmp_3: 3d779756
(Unnamed Layer* 153) [Shuffle]_output: 3cc18f1e
p2o.Add.31: 3dc09435
p2o.Clip.31: 3d418f1e
p2o.Mul.31: 3ec60d65
(Unnamed Layer* 158) [Shuffle]_output: 3d418f1e
batch_norm_15.tmp_4: 3d8408ee
conv2d_45.tmp_0: 3d023da5
batch_norm_16.tmp_3: 3d3ef44d
(Unnamed Layer* 163) [Shuffle]_output: 3cc18f1e
p2o.Add.33: 3d81db73
p2o.Clip.33: 3d418f1e
p2o.Mul.33: 3e6a63d4
(Unnamed Layer* 168) [Shuffle]_output: 3d418f1e
batch_norm_16.tmp_4: 3d1c428d
depthwise_conv2d_8.tmp_0: 3ca4369e
batch_norm_17.tmp_3: 3d892db8
(Unnamed Layer* 173) [Shuffle]_output: 3cc18f1e
p2o.Add.35: 3d957d35
p2o.Clip.35: 3d418f1e
p2o.Mul.35: 3e6ec44b
(Unnamed Layer* 178) [Shuffle]_output: 3d418f1e
batch_norm_17.tmp_4: 3d1f2d87
conv2d_46.tmp_0: 3d01b404
batch_norm_18.tmp_3: 3d3a107e
(Unnamed Layer* 183) [Shuffle]_output: 3cc18f1e
p2o.Add.37: 3da570f9
p2o.Clip.37: 3d418f1e
p2o.Mul.37: 3e6a74d9
(Unnamed Layer* 188) [Shuffle]_output: 3d418f1e
batch_norm_18.tmp_4: 3d1c4de6
depthwise_conv2d_9.tmp_0: 3c8029be
batch_norm_19.tmp_3: 3d67bdb0
(Unnamed Layer* 193) [Shuffle]_output: 3cc18f1e
p2o.Add.39: 3dc16506
p2o.Clip.39: 3d418f1e
p2o.Mul.39: 3e92fdf7
(Unnamed Layer* 198) [Shuffle]_output: 3d418f1e
batch_norm_19.tmp_4: 3d43fd4a
conv2d_47.tmp_0: 3d066ba8
batch_norm_20.tmp_3: 3d5c57cc
(Unnamed Layer* 203) [Shuffle]_output: 3cc18f1e
p2o.Add.41: 3da6b5ad
p2o.Clip.41: 3d418f1e
p2o.Mul.41: 3e54ba36
(Unnamed Layer* 208) [Shuffle]_output: 3d418f1e
batch_norm_20.tmp_4: 3d0dd17a
depthwise_conv2d_10.tmp_0: 3c7b558e
batch_norm_21.tmp_3: 3da5d438
(Unnamed Layer* 213) [Shuffle]_output: 3cc18f1e
p2o.Add.43: 3df5fb73
p2o.Clip.43: 3d418f1e
p2o.Mul.43: 3ec24333
(Unnamed Layer* 218) [Shuffle]_output: 3d418f1e
batch_norm_21.tmp_4: 3d818221
conv2d_48.tmp_0: 3d16fcdd
batch_norm_22.tmp_3: 3d8bf4cd
(Unnamed Layer* 223) [Shuffle]_output: 3cc18f1e
p2o.Add.45: 3dc7f3c7
p2o.Clip.45: 3d418f1e
p2o.Mul.45: 3e3319f6
(Unnamed Layer* 228) [Shuffle]_output: 3d418f1e
batch_norm_22.tmp_4: 3ceecd49
depthwise_conv2d_11.tmp_0: 3c96a1eb
batch_norm_23.tmp_3: 3e2c8f96
(Unnamed Layer* 233) [Shuffle]_output: 3cc18f1e
p2o.Add.47: 3e101779
p2o.Clip.47: 3d418f1e
p2o.Mul.47: 3f46f928
(Unnamed Layer* 238) [Shuffle]_output: 3d418f1e
batch_norm_23.tmp_4: 3e04a61b
p2o.GlobalAveragePool.1: 3d3be8ab
conv2d_49.tmp_0: 3d9b3362
relu_0.tmp_0: 3d9ea48d
conv2d_50.tmp_0: 3d0ef0a3
hardsigmoid_0.tmp_0: 3bf306c8
p2o.Mul.49: 3d18ea77
conv2d_51.tmp_0: 3c977ce5
batch_norm_24.tmp_3: 3d9024ce
(Unnamed Layer* 249) [Shuffle]_output: 3cc18f1e
p2o.Add.53: 3dae6ba7
p2o.Clip.49: 3d418f1e
p2o.Mul.51: 3e8453f3
(Unnamed Layer* 254) [Shuffle]_output: 3d418f1e
batch_norm_24.tmp_4: 3d306fef
depthwise_conv2d_12.tmp_0: 3c88ffc1
batch_norm_25.tmp_3: 3e6d820e
(Unnamed Layer* 259) [Shuffle]_output: 3cc18f1e
p2o.Add.55: 3da9ea07
p2o.Clip.51: 3d418f1e
p2o.Mul.53: 3ee92016
(Unnamed Layer* 264) [Shuffle]_output: 3d418f1e
batch_norm_25.tmp_4: 3d9b6aba
p2o.GlobalAveragePool.3: 3d166f8c
conv2d_52.tmp_0: 3de92639
relu_1.tmp_0: 3de92639
conv2d_53.tmp_0: 3d071868
hardsigmoid_1.tmp_0: 3b81de23
p2o.Mul.55: 3c01c617
conv2d_54.tmp_0: 3b62b36e
batch_norm_26.tmp_3: 3e6d43fa
(Unnamed Layer* 275) [Shuffle]_output: 3cc18f1e
p2o.Add.61: 3e344e72
p2o.Clip.53: 3d418f1e
p2o.Mul.57: 3f1423ba
(Unnamed Layer* 280) [Shuffle]_output: 3d418f1e
batch_norm_26.tmp_4: 3dc584f9
p2o.AveragePool.1: 3dc584f9
conv2d_55.tmp_0: 3ec944e4
batch_norm_27.tmp_2: 3d611110
(Unnamed Layer* 286) [Shuffle]_output: 3c010a14
p2o.Mul.59: 3d611110
p2o.Sigmoid.1: 3c032d5b
swish_7.tmp_0: 3ce56523
conv2d_56.tmp_0: 3d38d3fa
batch_norm_28.tmp_2: 3d3d0958
(Unnamed Layer* 293) [Shuffle]_output: 3c010a14
p2o.Mul.62: 3d3d0958
p2o.Sigmoid.3: 3c059b2c
swish_8.tmp_0: 3d018486
flatten_0.tmp_0: 3d018486
transpose_0.tmp_0: 3d018486
p2o.ReduceMean.1: 3b83853e
p2o.Sub.1: 3d1fb3e4
(Unnamed Layer* 309) [Shuffle]_output: 3c810a14
p2o.Pow.1: 3db40c65
p2o.ReduceMean.3: 3c335d08
(Unnamed Layer* 313) [Shuffle]_output: 33a9226d
p2o.Add.63: 3c335daf
p2o.Sqrt.1: 3c1e5225
p2o.Div.28: 3d8bddae
(Unnamed Layer* 317) [Constant]_output: 3c067ed8
(Unnamed Layer* 318) [Shuffle]_output: 3c067ed8
p2o.Mul.65: 3d9c5b80
(Unnamed Layer* 320) [Constant]_output: 3b34c6a8
(Unnamed Layer* 321) [Shuffle]_output: 3b34c6a8
p2o.Add.65: 3d91c2e7
(Unnamed Layer* 326) [Constant]_output: 3b393a13
(Unnamed Layer* 327) [Shuffle]_output: 3b393a13
p2o.MatMul.1: 3cee6aac
(Unnamed Layer* 329) [Constant]_output: 3b73e151
(Unnamed Layer* 330) [Shuffle]_output: 3b73e151
p2o.Add.67: 3ceddd0e
reshape2_0.tmp_0: 3ceddd0e
transpose_1.tmp_0: 3ceddd0e
p2o.Slice.5: 3d445891
transpose_1.tmp_0_slice_0: 3d445891
(Unnamed Layer* 347) [Shuffle]_output: 3b054573
p2o.Mul.67: 3c4ac906
p2o.Slice.7: 3cd1fa64
transpose_1.tmp_0_slice_1: 3cd1fa64
p2o.Slice.9: 3cba1017
transpose_1.tmp_0_slice_2: 3cba1017
transpose_2.tmp_0: 3cd1fa64
p2o.MatMul.3: 3d7bc88a
(Unnamed Layer* 355) [Shuffle]_output: 3d7bc88a
(Unnamed Layer* 356) [Softmax]_output: 3bd13f52
softmax_0.tmp_0: 3bd13f52
p2o.MatMul.5: 3c64680e
transpose_3.tmp_0: 3c64680e
reshape2_1.tmp_0: 3c64680e
(Unnamed Layer* 367) [Constant]_output: 3b13ea4f
(Unnamed Layer* 368) [Shuffle]_output: 3b13ea4f
p2o.MatMul.7: 3c1c9389
(Unnamed Layer* 370) [Constant]_output: 3afd2288
(Unnamed Layer* 371) [Shuffle]_output: 3afd2288
p2o.Add.69: 3c341c90
p2o.Add.71: 3d2d29df
p2o.ReduceMean.5: 3b8dda2d
p2o.Sub.3: 3d4b8d23
(Unnamed Layer* 377) [Shuffle]_output: 3c810a14
p2o.Pow.3: 3dcb8ea2
p2o.ReduceMean.7: 3c38090e
(Unnamed Layer* 381) [Shuffle]_output: 33a9226d
p2o.Add.73: 3c0f3121
p2o.Sqrt.3: 3c190e64
p2o.Div.30: 3d8c1f4f
(Unnamed Layer* 385) [Constant]_output: 3c90dabb
(Unnamed Layer* 386) [Shuffle]_output: 3c90dabb
p2o.Mul.69: 3da551ae
(Unnamed Layer* 388) [Constant]_output: 3c9caeff
(Unnamed Layer* 389) [Shuffle]_output: 3c9caeff
p2o.Add.75: 3dbbc034
(Unnamed Layer* 391) [Constant]_output: 3b328388
(Unnamed Layer* 392) [Shuffle]_output: 3b328388
p2o.MatMul.9: 3d621e72
(Unnamed Layer* 394) [Constant]_output: 3a5dad63
(Unnamed Layer* 395) [Shuffle]_output: 3a5dad63
p2o.Add.77: 3d7018c4
(Unnamed Layer* 398) [Shuffle]_output: 3c010a14
p2o.Mul.71: 3d7018c4
p2o.Sigmoid.5: 3bf51711
swish_9.tmp_0: 3b107941
(Unnamed Layer* 402) [Constant]_output: 3aff1535
(Unnamed Layer* 403) [Shuffle]_output: 3aff1535
p2o.MatMul.11: 3bf406d7
(Unnamed Layer* 405) [Constant]_output: 3acd3e09
(Unnamed Layer* 406) [Shuffle]_output: 3acd3e09
p2o.Add.79: 3c092019
p2o.Add.81: 3d478124
p2o.ReduceMean.9: 3b935518
p2o.Sub.5: 3d4ca697
(Unnamed Layer* 412) [Shuffle]_output: 3c810a14
p2o.Pow.5: 3da72c83
p2o.ReduceMean.11: 3c1200f8
(Unnamed Layer* 416) [Shuffle]_output: 33a9226d
p2o.Add.83: 3c12017f
p2o.Sqrt.5: 3c1b9218
p2o.Div.32: 3d9177be
(Unnamed Layer* 420) [Constant]_output: 3c07ac6f
(Unnamed Layer* 421) [Shuffle]_output: 3c07ac6f
p2o.Mul.74: 3d729995
(Unnamed Layer* 423) [Constant]_output: 3b36bb58
(Unnamed Layer* 424) [Shuffle]_output: 3b36bb58
p2o.Add.85: 3d6e62cf
(Unnamed Layer* 429) [Constant]_output: 3b8b423d
(Unnamed Layer* 430) [Shuffle]_output: 3b8b423d
p2o.MatMul.13: 3d340f79
(Unnamed Layer* 432) [Constant]_output: 3bba0697
(Unnamed Layer* 433) [Shuffle]_output: 3bba0697
p2o.Add.87: 3d342be6
reshape2_2.tmp_0: 3d342be6
transpose_4.tmp_0: 3d342be6
p2o.Slice.14: 3d4650a6
transpose_4.tmp_0_slice_0: 3d4650a6
(Unnamed Layer* 450) [Shuffle]_output: 3b054573
p2o.Mul.76: 3c4cd1a1
p2o.Slice.16: 3d187fa3
transpose_4.tmp_0_slice_1: 3d187fa3
p2o.Slice.18: 3d350800
transpose_4.tmp_0_slice_2: 3d350800
transpose_5.tmp_0: 3d187fa3
p2o.MatMul.15: 3d71ef4f
(Unnamed Layer* 458) [Shuffle]_output: 3d71ef4f
(Unnamed Layer* 459) [Softmax]_output: 3bdad597
softmax_1.tmp_0: 3bdad597
p2o.MatMul.17: 3cdbdfba
transpose_6.tmp_0: 3cdbdfba
reshape2_3.tmp_0: 3cdbdfba
(Unnamed Layer* 470) [Constant]_output: 3b52d4c1
(Unnamed Layer* 471) [Shuffle]_output: 3b52d4c1
p2o.MatMul.19: 3cb289a5
(Unnamed Layer* 473) [Constant]_output: 3ae9fcd5
(Unnamed Layer* 474) [Shuffle]_output: 3ae9fcd5
p2o.Add.89: 3ce9a1e6
p2o.Add.91: 3d8aa9ce
p2o.ReduceMean.13: 3b88899f
p2o.Sub.7: 3d57e73f
(Unnamed Layer* 480) [Shuffle]_output: 3c810a14
p2o.Pow.7: 3df581d7
p2o.ReduceMean.15: 3c3c9aad
(Unnamed Layer* 484) [Shuffle]_output: 33a9226d
p2o.Add.93: 3c3c9b3b
p2o.Sqrt.7: 3c3e4f09
p2o.Div.34: 3d5baa18
(Unnamed Layer* 488) [Constant]_output: 3c82cc7f
(Unnamed Layer* 489) [Shuffle]_output: 3c82cc7f
p2o.Mul.78: 3da5fb03
(Unnamed Layer* 491) [Constant]_output: 3c856089
(Unnamed Layer* 492) [Shuffle]_output: 3c856089
p2o.Add.95: 3dc83538
(Unnamed Layer* 494) [Constant]_output: 3b3b0772
(Unnamed Layer* 495) [Shuffle]_output: 3b3b0772
p2o.MatMul.21: 3d8c440a
(Unnamed Layer* 497) [Constant]_output: 3a9bf351
(Unnamed Layer* 498) [Shuffle]_output: 3a9bf351
p2o.Add.97: 3d908df9
(Unnamed Layer* 501) [Shuffle]_output: 3c010a14
p2o.Mul.80: 3d908df9
p2o.Sigmoid.7: 3c05245d
swish_10.tmp_0: 3d11ef56
(Unnamed Layer* 505) [Constant]_output: 3b809044
(Unnamed Layer* 506) [Shuffle]_output: 3b809044
p2o.MatMul.23: 3e2647db
(Unnamed Layer* 508) [Constant]_output: 3b0d968f
(Unnamed Layer* 509) [Shuffle]_output: 3b0d968f
p2o.Add.99: 3e286f21
p2o.Add.101: 3e4571b7
p2o.ReduceMean.17: 3c270fc4
p2o.Sub.9: 3e4493ba
(Unnamed Layer* 515) [Shuffle]_output: 3c810a14
p2o.Pow.9: 3fd491c4
p2o.ReduceMean.19: 3e99c77a
(Unnamed Layer* 519) [Shuffle]_output: 32074ebd
p2o.Add.103: 3e99c77b
p2o.Sqrt.9: 3d4b25f9
p2o.Div.36: 3d24b018
(Unnamed Layer* 523) [Constant]_output: 3b76b93d
(Unnamed Layer* 524) [Shuffle]_output: 3b76b93d
p2o.Mul.83: 3bffafdc
(Unnamed Layer* 526) [Constant]_output: 36f99819
(Unnamed Layer* 527) [Shuffle]_output: 36f99819
p2o.Add.105: 3c0f4bba
reshape2_4.tmp_0: 3c0f4bba
transpose_7.tmp_0: 3c0f4bba
conv2d_57.tmp_0: 3c257d46
batch_norm_29.tmp_2: 3d840cf7
(Unnamed Layer* 542) [Shuffle]_output: 3c010a14
p2o.Mul.85: 3d840cf7
p2o.Sigmoid.9: 3c01ae5b
swish_11.tmp_0: 3d42e624
p2o.Concat.8: 3dc35a23
conv2d_58.tmp_0: 3e5d8296
batch_norm_30.tmp_2: 3d26020a
(Unnamed Layer* 550) [Shuffle]_output: 3c010a14
p2o.Mul.88: 3d26020a
p2o.Sigmoid.11: 3c02af92
swish_12.tmp_0: 3cd1d527
conv2d_59.tmp_0: 3d41816a
batch_norm_31.tmp_2: 3d955a49
(Unnamed Layer* 557) [Shuffle]_output: 3c010a14
p2o.Mul.91: 3d955a49
p2o.Sigmoid.13: 3c00df1b
swish_13.tmp_0: 3d69ef4e
squeeze_0.tmp_0: 3d69ef4e
transpose_8.tmp_0: 3d69ef4e
(Unnamed Layer* 563) [Constant]_output: 3c28809b
(Unnamed Layer* 564) [Shuffle]_output: 3c28809b
p2o.MatMul.25: 3e6cbb95
(Unnamed Layer* 566) [Constant]_output: 3b49b65f
(Unnamed Layer* 567) [Shuffle]_output: 3b49b65f
p2o.Add.107: 3e669ddb
(Unnamed Layer* 569) [Shuffle]_output: 3e669ddb
(Unnamed Layer* 570) [Softmax]_output: 3c010a14
output: 3c010a14
