#!/bin/bash
# sudo apt install ros-foxy-backward-ros
# sudo apt install ros-humble-backward-ros

# Check if at least two arguments are passed (package and executable)
if [ "$#" -lt 2 ]; then
  echo "Usage: $0 <package> <executable> [args...]"
  exit 1
fi

PACKAGE=$1
EXECUTABLE=$2
shift 2  # Shift the first two arguments to access the remaining ones

# Run the ROS 2 command with gdb and all remaining arguments
ros2 run --prefix 'gdb -ex run --args' $PACKAGE $EXECUTABLE "$@"
