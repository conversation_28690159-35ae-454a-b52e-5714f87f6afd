#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp" 
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <string>
#include <numeric>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/opencv.hpp"

#include "trt_ocr.hpp"
#include "trt_preprocess.hpp"
#include "utils.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace ocr {

/*
    classification model的初始化相关内容。
    包括设置input/output bindings, 分配host/device的memory等
*/
void OCRModel::setup(void const* data, size_t size) {
    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    m_outputDims  = m_context->getBindingDimensions(1);
    // 考虑到大多数classification model都是1 input, 1 output, 这边这么写。如果像BEVFusion这种有多输出的需要修改

    CUDA_CHECK(cudaStreamCreate(&m_stream));
    
    m_inputSize     = m_params->batch_size * m_params->img.c * m_params->img.h * m_params->img.w * sizeof(float);
    m_outputSize    = m_params->batch_size * m_context->getBindingDimensions(1).d[1] * m_context->getBindingDimensions(1).d[2] * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;

    m_sequence_length = m_context->getBindingDimensions(1).d[1];
    m_num_classes     = m_context->getBindingDimensions(1).d[2];

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_outputMemory[0], m_outputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_outputMemory[1], m_outputSize));

    // //创建m_bindings，之后再寻址就直接从这里找
    m_bindings[0] = m_inputMemory[1];
    m_bindings[1] = m_outputMemory[1];

}

void OCRModel::reset_task(){}

bool OCRModel::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    /*Preprocess -- 获取mean, std*/
    float mean[]       = {0.5, 0.5, 0.5};
    float std[]        = {0.5, 0.5, 0.5};

    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i); 
            return false;
        }
    }

    /*Preprocess -- 测速*/
    // m_timer->start_cpu();

    /*Preprocess -- 处理每张图像*/
    for (int b = 0; b < m_activedBatchSize; b++) {
        // 计算宽高比
        float h = m_inputImages[b].rows;
        float w = m_inputImages[b].cols;
        float ratio = w / h;
        
        // 确定调整后的宽度
        int resized_w;
        if (std::ceil(m_params->img.h * ratio) > m_params->img.w) {
            resized_w = m_params->img.w;
        } else {
            resized_w = std::ceil(m_params->img.h * ratio);
        }
        
        // 调整图像大小
        cv::Mat resized_image;
        cv::resize(m_inputImages[b], resized_image, cv::Size(resized_w, m_params->img.h));
        
        // 转换为浮点型并归一化到[0,1]
        resized_image.convertTo(resized_image, CV_32F, 1.0/255.0);
        
        // 分离通道
        std::vector<cv::Mat> channels;
        cv::split(resized_image, channels);
        
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        
        // 填充图像数据并进行归一化
        for (int c = 0; c < m_params->img.c; ++c) {
            // 每个通道在当前批次内的起始位置
            const int offset_ch = offset_batch + c * m_imgArea;
            
            for (int i = 0; i < m_params->img.h; ++i) {
                for (int j = 0; j < m_params->img.w; ++j) {
                    if (j < resized_w) {
                        // 有效区域：应用归一化
                        float pixel_value = channels[c].at<float>(i, j);
                        m_inputMemory[0][offset_ch + i * m_params->img.w + j] = 
                            (pixel_value - mean[c]) / std[c];
                    } else {
                        // 填充区域：设为0
                        m_inputMemory[0][offset_ch + i * m_params->img.w + j] = 0;
                    }
                }
            }
        }
    }
    
    // 处理未激活的批次
    for (int b = m_activedBatchSize; b < m_params->batch_size; b++) {
        const int offset_batch = b * m_imgArea * m_params->img.c;
        for (int c = 0; c < m_params->img.c; ++c) {
            const int offset_ch = offset_batch + c * m_imgArea;
            for (int i = 0; i < m_imgArea; ++i) {
                m_inputMemory[0][offset_ch + i] = 0;
            }
        }
    }

    /*Preprocess -- 将host的数据移动到device上*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));

    // m_timer->stop_cpu<timer::Timer::ms>("preprocess(CPU)");

    return true;
}

bool OCRModel::preprocess_gpu() {
    /*Preprocess -- 获取mean, std*/
    float mean[]       = {0.406, 0.456, 0.485};
    float std[]        = {0.225, 0.224, 0.229};

    /*Preprocess -- 读取数据*/
    cv::Mat input_image;
    input_image = cv::imread(m_imagePath);
    if (input_image.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }

    /*Preprocess -- 测速*/
    // m_timer->start_gpu();
    
    /*Preprocess -- 使用GPU进行双线性插值, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(input_image, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w, 
                                   mean, std, preprocess::tactics::GPU_BILINEAR);

    // m_timer->stop_gpu("preprocess(GPU)");
    return true;
}

bool OCRModel::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);
    /*Postprocess -- 测速*/
    // m_timer->start_cpu();

    /*Postprocess -- 将device上的数据移动到host上*/
    CUDA_CHECK(cudaMemcpyAsync(m_outputMemory[0], m_outputMemory[1], m_outputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    // 字符字典
    std::vector<std::string> charDict = {
        " ", 
        "0", "1", "2", "3", "4", "5", "6", "7", "8", "9",
        "A", "B", "C", "D", "E", "F", "G", "H", "J", "K", "L", "M", "N", 
        "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z",
        "京", "沪", "津", "渝", "冀", "晋", "蒙", "辽", "吉", "黑", "苏", "浙", 
        "皖", "闽", "赣", "鲁", "豫", "鄂", "湘", "粤", "桂", "琼", "川", "贵", 
        "云", "藏", "陕", "甘", "青", "宁", "新", "学", "警", "使", "领", "港", 
        "澳", "挂", "临", "时", "入", "境", "民", "航", "危", "险", "品", "应", "急",
        " "
    };

    for (int b = 0; b < m_activedBatchSize; b++) {
        // 获取当前批次的输出数据
        float* output = m_outputMemory[0] + b * m_sequence_length * m_num_classes;
        
        // 处理输出结果 - 找到每个时间步的最大概率字符
        std::vector<int> preds_idx(m_sequence_length);
        std::vector<float> preds_prob(m_sequence_length);
        for (int t = 0; t < m_sequence_length; ++t) {
            float max_prob = -1.0f;
            int max_idx = 0;
            for (int c = 0; c < m_num_classes; ++c) {
                float prob = output[t * m_num_classes + c];
                if (prob > max_prob) {
                    max_prob = prob;
                    max_idx = c;
                }
            }
            
            preds_idx[t] = max_idx;
            preds_prob[t] = max_prob;
        }
        
        // 解码预测结果 - 类似于Python中的decode函数
        std::string plateNumber;
        float avg_confidence = 0.0f;
        std::vector<float> char_confidences;
        
        // 去除重复字符和空白字符(blank=0)
        bool prev_is_blank = true;
        for (int i = 0; i < m_sequence_length; ++i) {
            int idx = preds_idx[i];
            // 跳过空白字符
            if (idx == 0) {
                prev_is_blank = true;
                continue;
            }
            // 去除重复字符
            if (i > 0 && idx == preds_idx[i-1] && !prev_is_blank) {
                continue;
            }
            // 添加有效字符
            if (idx < charDict.size()) {
                plateNumber += charDict[idx];
                char_confidences.push_back(preds_prob[i]);
            }
            prev_is_blank = false;
        }
        
        // 计算平均置信度
        if (!char_confidences.empty()) {
            avg_confidence = std::accumulate(char_confidences.begin(), char_confidences.end(), 0.0f) / char_confidences.size();
        }
        
        // 输出结果
        LOG("\tBatch %d - 车牌号码: %s", b, plateNumber.c_str());
        LOG("\tBatch %d - 平均置信度: %.3f%%", b, avg_confidence * 100);
    }

    // m_timer->stop_cpu<timer::Timer::ms>("postprocess(CPU)");
    // m_timer->show();

    return true;
}


bool OCRModel::postprocess_gpu() {
    /*
        由于classification task的postprocess比较简单，所以CPU/GPU的处理这里用一样的
        对于像yolo这种detection model, postprocess会包含decode, nms这些处理。可以选择在CPU还是在GPU上跑
    */
    return postprocess_cpu();

}


bool OCRModel::check_model() {
    return true;
}


shared_ptr<OCRModel> make_ocr_model(
    std::string onnx_path, logger::Level level, model::Params params)
{
    return make_shared<OCRModel>(onnx_path, level, params);
}

}; // namespace ocr

}; // namespace model
