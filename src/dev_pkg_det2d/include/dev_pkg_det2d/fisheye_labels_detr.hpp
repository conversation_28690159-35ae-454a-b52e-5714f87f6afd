#include <iostream>

#include <string>
#include <vector>
#include "assert.h"
#include <time.h>
#include "opencv2/core/core.hpp"
#include "opencv2/core/types.hpp"
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"

using namespace std;

class FisheyeLabelsDetr {
public:
    FisheyeLabelsDetr() {
        // Initialize with 91 labels
        for(int i = 0; i < 91; i++) {
            string x = "NA";
            mLabels.push_back(x);
        }

        mLabels[0]  = "background";
        mLabels[1]  = "person";
        mLabels[2]  = "bicycle";
        mLabels[3]  = "car";
        mLabels[4]  = "motorcycle";
        mLabels[5]  = "airplane";
        mLabels[6]  = "bus";
        mLabels[7]  = "train";
        mLabels[8]  = "truck";
        mLabels[9]  = "boat";
        mLabels[10] = "traffic light";
        mLabels[11] = "fire hydrant";
        mLabels[13] = "stop sign";
        mLabels[14] = "parking meter";
        mLabels[15] = "bench";
        mLabels[16] = "bird";
        mLabels[17] = "cat";
        mLabels[18] = "dog";
        mLabels[19] = "horse";
        mLabels[20] = "sheep";
        mLabels[21] = "cow";
        mLabels[22] = "elephant";
        mLabels[23] = "bear";
        mLabels[24] = "zebra";
        mLabels[25] = "giraffe";
        mLabels[27] = "backpack";
        mLabels[28] = "umbrella";
        mLabels[31] = "handbag";
        mLabels[32] = "tie";
        mLabels[33] = "suitcase";
        mLabels[34] = "frisbee";
        mLabels[35] = "skis";
        mLabels[36] = "snowboard";
        mLabels[37] = "sports ball";
        mLabels[38] = "kite";
        mLabels[39] = "baseball bat";
        mLabels[40] = "baseball glove";
        mLabels[41] = "skateboard";
        mLabels[42] = "surfboard";
        mLabels[43] = "tennis racket";
        mLabels[44] = "bottle";
        mLabels[46] = "wine glass";
        mLabels[47] = "cup";
        mLabels[48] = "fork";
        mLabels[49] = "knife";
        mLabels[50] = "spoon";
        mLabels[51] = "bowl";
        mLabels[52] = "banana";
        mLabels[53] = "apple";
        mLabels[54] = "sandwich";
        mLabels[55] = "orange";
        mLabels[56] = "broccoli";
        mLabels[57] = "carrot";
        mLabels[58] = "hot dog";
        mLabels[59] = "pizza";
        mLabels[60] = "donut";
        mLabels[61] = "cake";
        mLabels[62] = "chair";
        mLabels[63] = "couch";
        mLabels[64] = "potted plant";
        mLabels[65] = "bed";
        mLabels[67] = "dining table";
        mLabels[70] = "toilet";
        mLabels[72] = "tv";
        mLabels[73] = "laptop";
        mLabels[74] = "mouse";
        mLabels[75] = "remote";
        mLabels[76] = "keyboard";
        mLabels[77] = "cell phone";
        mLabels[78] = "microwave";
        mLabels[79] = "oven";
        mLabels[80] = "toaster";
        mLabels[81] = "sink";
        mLabels[82] = "refrigerator";
        mLabels[84] = "book";
        mLabels[85] = "clock";
        mLabels[86] = "vase";
        mLabels[87] = "scissors";
        mLabels[88] = "teddy bear";
        mLabels[89] = "hair drier";
        mLabels[90] = "toothbrush";
    }

    string coco_get_label(int i) {
        assert(i >= 0 && i < 91);
        return mLabels[i];
    }

    cv::Scalar coco_get_color(int i) {
        float r;
        srand(i);
        r = (float)rand() / RAND_MAX;
        int red    = int(r * 255);

        srand(i + 1);
        r = (float)rand() / RAND_MAX;
        int green    = int(r * 255);

        srand(i + 2);
        r = (float)rand() / RAND_MAX;
        int blue    = int(r * 255);

        return cv::Scalar(blue, green, red);
    }

    cv::Scalar get_inverse_color(cv::Scalar color) {
        int blue = 255 - color[0];
        int green = 255 - color[1];
        int red = 255 - color[2];
        return cv::Scalar(blue, green, red);
    }


private:
  vector<string> mLabels;

};
