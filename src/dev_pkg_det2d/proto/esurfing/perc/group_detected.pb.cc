// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/perc/group_detected.proto

#include "esurfing/perc/group_detected.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace perception {
namespace detect {
PROTOBUF_CONSTEXPR DetectedObjects::DetectedObjects(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.objects_)*/{}
  , /*decltype(_impl_.group_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.group_info_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.time_meas_)*/int64_t{0}
  , /*decltype(_impl_.time_pub_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DetectedObjectsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DetectedObjectsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DetectedObjectsDefaultTypeInternal() {}
  union {
    DetectedObjects _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DetectedObjectsDefaultTypeInternal _DetectedObjects_default_instance_;
PROTOBUF_CONSTEXPR LicenePlate::LicenePlate(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.number_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.color_)*/0
  , /*decltype(_impl_.color_confidence_)*/0
  , /*decltype(_impl_.number_confidence_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LicenePlateDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LicenePlateDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LicenePlateDefaultTypeInternal() {}
  union {
    LicenePlate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LicenePlateDefaultTypeInternal _LicenePlate_default_instance_;
PROTOBUF_CONSTEXPR TrackConfidence::TrackConfidence(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.color_)*/0u
  , /*decltype(_impl_.type_)*/0u
  , /*decltype(_impl_.plate_number_)*/0u
  , /*decltype(_impl_.plate_color_)*/0u
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct TrackConfidenceDefaultTypeInternal {
  PROTOBUF_CONSTEXPR TrackConfidenceDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~TrackConfidenceDefaultTypeInternal() {}
  union {
    TrackConfidence _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 TrackConfidenceDefaultTypeInternal _TrackConfidence_default_instance_;
PROTOBUF_CONSTEXPR DetectedObject::DetectedObject(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.feature_)*/{}
  , /*decltype(_impl_.trajectories_)*/{}
  , /*decltype(_impl_.str_array_)*/{}
  , /*decltype(_impl_.int_array_)*/{}
  , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
  , /*decltype(_impl_.overlap_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.group_id_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.camera_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.position_)*/nullptr
  , /*decltype(_impl_.shape_)*/nullptr
  , /*decltype(_impl_.hull_)*/nullptr
  , /*decltype(_impl_.velocity_)*/nullptr
  , /*decltype(_impl_.color_)*/nullptr
  , /*decltype(_impl_.plate_)*/nullptr
  , /*decltype(_impl_.track_conf_)*/nullptr
  , /*decltype(_impl_.position_image_)*/nullptr
  , /*decltype(_impl_.shape_image_)*/nullptr
  , /*decltype(_impl_.uuid_)*/uint64_t{0u}
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.confidence_)*/0
  , /*decltype(_impl_.orientation_)*/0
  , /*decltype(_impl_.is_static_)*/false
  , /*decltype(_impl_.parking_time_)*/int64_t{0}
  , /*decltype(_impl_.obj_color_)*/0
  , /*decltype(_impl_.obj_color_conf_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct DetectedObjectDefaultTypeInternal {
  PROTOBUF_CONSTEXPR DetectedObjectDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~DetectedObjectDefaultTypeInternal() {}
  union {
    DetectedObject _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 DetectedObjectDefaultTypeInternal _DetectedObject_default_instance_;
PROTOBUF_CONSTEXPR Velocity::Velocity(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.heading_)*/0
  , /*decltype(_impl_.speed_)*/0
  , /*decltype(_impl_.acceleration_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct VelocityDefaultTypeInternal {
  PROTOBUF_CONSTEXPR VelocityDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~VelocityDefaultTypeInternal() {}
  union {
    Velocity _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 VelocityDefaultTypeInternal _Velocity_default_instance_;
PROTOBUF_CONSTEXPR WayPoints::WayPoints(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.waypoints_)*/{}
  , /*decltype(_impl_.probability_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct WayPointsDefaultTypeInternal {
  PROTOBUF_CONSTEXPR WayPointsDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~WayPointsDefaultTypeInternal() {}
  union {
    WayPoints _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 WayPointsDefaultTypeInternal _WayPoints_default_instance_;
PROTOBUF_CONSTEXPR WayPoint::WayPoint(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.position_)*/nullptr
  , /*decltype(_impl_.velocity_)*/nullptr
  , /*decltype(_impl_.time_meas_)*/int64_t{0}
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct WayPointDefaultTypeInternal {
  PROTOBUF_CONSTEXPR WayPointDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~WayPointDefaultTypeInternal() {}
  union {
    WayPoint _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 WayPointDefaultTypeInternal _WayPoint_default_instance_;
}  // namespace detect
}  // namespace perception
static ::_pb::Metadata file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[7];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_esurfing_2fperc_2fgroup_5fdetected_2eproto[1];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_esurfing_2fperc_2fgroup_5fdetected_2eproto = nullptr;

const uint32_t TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _impl_.time_meas_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _impl_.time_pub_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _impl_.group_name_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _impl_.group_info_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObjects, _impl_.objects_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::LicenePlate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::LicenePlate, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::LicenePlate, _impl_.color_confidence_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::LicenePlate, _impl_.number_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::LicenePlate, _impl_.number_confidence_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::TrackConfidence, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::TrackConfidence, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::TrackConfidence, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::TrackConfidence, _impl_.plate_number_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::TrackConfidence, _impl_.plate_color_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.overlap_name_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.uuid_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.confidence_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.position_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.shape_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.hull_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.orientation_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.velocity_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.is_static_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.color_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.feature_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.trajectories_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.str_array_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.int_array_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.parking_time_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.plate_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.obj_color_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.group_id_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.obj_color_conf_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.track_conf_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.camera_name_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.position_image_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::DetectedObject, _impl_.shape_image_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::Velocity, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::Velocity, _impl_.heading_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::Velocity, _impl_.speed_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::Velocity, _impl_.acceleration_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoints, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoints, _impl_.waypoints_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoints, _impl_.probability_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoint, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoint, _impl_.time_meas_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoint, _impl_.position_),
  PROTOBUF_FIELD_OFFSET(::perception::detect::WayPoint, _impl_.velocity_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::perception::detect::DetectedObjects)},
  { 11, -1, -1, sizeof(::perception::detect::LicenePlate)},
  { 21, -1, -1, sizeof(::perception::detect::TrackConfidence)},
  { 31, -1, -1, sizeof(::perception::detect::DetectedObject)},
  { 61, -1, -1, sizeof(::perception::detect::Velocity)},
  { 70, -1, -1, sizeof(::perception::detect::WayPoints)},
  { 78, -1, -1, sizeof(::perception::detect::WayPoint)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::perception::detect::_DetectedObjects_default_instance_._instance,
  &::perception::detect::_LicenePlate_default_instance_._instance,
  &::perception::detect::_TrackConfidence_default_instance_._instance,
  &::perception::detect::_DetectedObject_default_instance_._instance,
  &::perception::detect::_Velocity_default_instance_._instance,
  &::perception::detect::_WayPoints_default_instance_._instance,
  &::perception::detect::_WayPoint_default_instance_._instance,
};

const char descriptor_table_protodef_esurfing_2fperc_2fgroup_5fdetected_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\"esurfing/perc/group_detected.proto\022\021pe"
  "rception.detect\032\027esurfing/math/geo.proto"
  "\032\031esurfing/math/color.proto\"\222\001\n\017Detected"
  "Objects\022\021\n\ttime_meas\030\001 \001(\020\022\020\n\010time_pub\030\002"
  " \001(\020\022\022\n\ngroup_name\030\003 \001(\014\022\022\n\ngroup_info\030\004"
  " \001(\014\0222\n\007objects\030\005 \003(\0132!.perception.detec"
  "t.DetectedObject\"a\n\013LicenePlate\022\r\n\005color"
  "\030\001 \001(\005\022\030\n\020color_confidence\030\002 \001(\002\022\016\n\006numb"
  "er\030\003 \001(\t\022\031\n\021number_confidence\030\004 \001(\002\"Y\n\017T"
  "rackConfidence\022\r\n\005color\030\001 \001(\r\022\014\n\004type\030\002 "
  "\001(\r\022\024\n\014plate_number\030\003 \001(\r\022\023\n\013plate_color"
  "\030\004 \001(\r\"\374\005\n\016DetectedObject\022\024\n\014overlap_nam"
  "e\030\001 \001(\014\022\014\n\004uuid\030\002 \001(\006\022\014\n\004type\030\003 \001(\021\022\022\n\nc"
  "onfidence\030\004 \001(\002\022-\n\010position\030\005 \001(\0132\033.perc"
  "eption.detect.Vector3f\022*\n\005shape\030\006 \001(\0132\033."
  "perception.detect.Vector3f\022(\n\004hull\030\007 \001(\013"
  "2\032.perception.detect.Polygon\022\023\n\013orientat"
  "ion\030\010 \001(\002\022-\n\010velocity\030\t \001(\0132\033.perception"
  ".detect.Velocity\022\021\n\tis_static\030\n \001(\010\022\'\n\005c"
  "olor\030\013 \001(\0132\030.perception.detect.Color\022\017\n\007"
  "feature\030\014 \003(\002\0222\n\014trajectories\030\r \003(\0132\034.pe"
  "rception.detect.WayPoints\022\021\n\tstr_array\030\016"
  " \003(\014\022\021\n\tint_array\030\017 \003(\021\022\024\n\014parking_time\030"
  "\020 \001(\003\022-\n\005plate\030\021 \001(\0132\036.perception.detect"
  ".LicenePlate\022\021\n\tobj_color\030\022 \001(\005\022\020\n\010group"
  "_id\030\023 \001(\t\022\026\n\016obj_color_conf\030\024 \001(\002\0226\n\ntra"
  "ck_conf\030\025 \001(\0132\".perception.detect.TrackC"
  "onfidence\022\023\n\013camera_name\030\026 \001(\t\0223\n\016positi"
  "on_image\030\027 \001(\0132\033.perception.detect.Vecto"
  "r3f\0220\n\013shape_image\030\030 \001(\0132\033.perception.de"
  "tect.Vector2f\"@\n\010Velocity\022\017\n\007heading\030\001 \001"
  "(\002\022\r\n\005speed\030\002 \001(\002\022\024\n\014acceleration\030\003 \001(\002\""
  "P\n\tWayPoints\022.\n\twaypoints\030\001 \003(\0132\033.percep"
  "tion.detect.WayPoint\022\023\n\013probability\030\002 \001("
  "\002\"{\n\010WayPoint\022\021\n\ttime_meas\030\001 \001(\020\022-\n\010posi"
  "tion\030\002 \001(\0132\033.perception.detect.Vector3f\022"
  "-\n\010velocity\030\003 \001(\0132\033.perception.detect.Ve"
  "locity*\343\001\n\nObjectType\022\013\n\007UNKNOWN\020\000\022\007\n\003CA"
  "R\020\001\022\016\n\nPEDESTRIAN\020\002\022\013\n\007CYCLIST\020\003\022\t\n\005TRUC"
  "K\020\004\022\007\n\003VAN\020\005\022\007\n\003BUS\020\006\022\n\n\006STATIC\020\007\022\017\n\013STA"
  "TIC_EDGE\020\010\022\010\n\004CONE\020\t\022\013\n\007TROLLEY\020\n\022\t\n\005ROB"
  "OT\020\013\022\010\n\004GATE\020\014\022\014\n\010AIRPLANE\020\r\022\t\n\005DRONE\020\016\022"
  "\016\n\nHELICOPTER\020\017\022\t\n\005EVTOL\020\020\022\010\n\004BIRD\020\021B\016B\n"
  "PercDetectP\000b\006proto3"
  ;
static const ::_pbi::DescriptorTable* const descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_deps[2] = {
  &::descriptor_table_esurfing_2fmath_2fcolor_2eproto,
  &::descriptor_table_esurfing_2fmath_2fgeo_2eproto,
};
static ::_pbi::once_flag descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto = {
    false, false, 1740, descriptor_table_protodef_esurfing_2fperc_2fgroup_5fdetected_2eproto,
    "esurfing/perc/group_detected.proto",
    &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once, descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_deps, 2, 7,
    schemas, file_default_instances, TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto::offsets,
    file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto, file_level_enum_descriptors_esurfing_2fperc_2fgroup_5fdetected_2eproto,
    file_level_service_descriptors_esurfing_2fperc_2fgroup_5fdetected_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter() {
  return &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_esurfing_2fperc_2fgroup_5fdetected_2eproto(&descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto);
namespace perception {
namespace detect {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ObjectType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto);
  return file_level_enum_descriptors_esurfing_2fperc_2fgroup_5fdetected_2eproto[0];
}
bool ObjectType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class DetectedObjects::_Internal {
 public:
};

DetectedObjects::DetectedObjects(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.DetectedObjects)
}
DetectedObjects::DetectedObjects(const DetectedObjects& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DetectedObjects* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){from._impl_.objects_}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.group_info_){}
    , decltype(_impl_.time_meas_){}
    , decltype(_impl_.time_pub_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_name().empty()) {
    _this->_impl_.group_name_.Set(from._internal_group_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.group_info_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_info_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_info().empty()) {
    _this->_impl_.group_info_.Set(from._internal_group_info(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.time_meas_, &from._impl_.time_meas_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.time_pub_) -
    reinterpret_cast<char*>(&_impl_.time_meas_)) + sizeof(_impl_.time_pub_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.DetectedObjects)
}

inline void DetectedObjects::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.objects_){arena}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.group_info_){}
    , decltype(_impl_.time_meas_){int64_t{0}}
    , decltype(_impl_.time_pub_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.group_info_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_info_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DetectedObjects::~DetectedObjects() {
  // @@protoc_insertion_point(destructor:perception.detect.DetectedObjects)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DetectedObjects::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.objects_.~RepeatedPtrField();
  _impl_.group_name_.Destroy();
  _impl_.group_info_.Destroy();
}

void DetectedObjects::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DetectedObjects::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.DetectedObjects)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.objects_.Clear();
  _impl_.group_name_.ClearToEmpty();
  _impl_.group_info_.ClearToEmpty();
  ::memset(&_impl_.time_meas_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.time_pub_) -
      reinterpret_cast<char*>(&_impl_.time_meas_)) + sizeof(_impl_.time_pub_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectedObjects::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // sfixed64 time_meas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.time_meas_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // sfixed64 time_pub = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.time_pub_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // bytes group_name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_group_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes group_info = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_group_info();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.detect.DetectedObject objects = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_objects(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectedObjects::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.DetectedObjects)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(1, this->_internal_time_meas(), target);
  }

  // sfixed64 time_pub = 2;
  if (this->_internal_time_pub() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(2, this->_internal_time_pub(), target);
  }

  // bytes group_name = 3;
  if (!this->_internal_group_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        3, this->_internal_group_name(), target);
  }

  // bytes group_info = 4;
  if (!this->_internal_group_info().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_group_info(), target);
  }

  // repeated .perception.detect.DetectedObject objects = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_objects_size()); i < n; i++) {
    const auto& repfield = this->_internal_objects(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.DetectedObjects)
  return target;
}

size_t DetectedObjects::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.DetectedObjects)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.detect.DetectedObject objects = 5;
  total_size += 1UL * this->_internal_objects_size();
  for (const auto& msg : this->_impl_.objects_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes group_name = 3;
  if (!this->_internal_group_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_group_name());
  }

  // bytes group_info = 4;
  if (!this->_internal_group_info().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_group_info());
  }

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    total_size += 1 + 8;
  }

  // sfixed64 time_pub = 2;
  if (this->_internal_time_pub() != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectedObjects::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DetectedObjects::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectedObjects::GetClassData() const { return &_class_data_; }


void DetectedObjects::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DetectedObjects*>(&to_msg);
  auto& from = static_cast<const DetectedObjects&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.DetectedObjects)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.objects_.MergeFrom(from._impl_.objects_);
  if (!from._internal_group_name().empty()) {
    _this->_internal_set_group_name(from._internal_group_name());
  }
  if (!from._internal_group_info().empty()) {
    _this->_internal_set_group_info(from._internal_group_info());
  }
  if (from._internal_time_meas() != 0) {
    _this->_internal_set_time_meas(from._internal_time_meas());
  }
  if (from._internal_time_pub() != 0) {
    _this->_internal_set_time_pub(from._internal_time_pub());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectedObjects::CopyFrom(const DetectedObjects& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.DetectedObjects)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectedObjects::IsInitialized() const {
  return true;
}

void DetectedObjects::InternalSwap(DetectedObjects* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.objects_.InternalSwap(&other->_impl_.objects_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_name_, lhs_arena,
      &other->_impl_.group_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_info_, lhs_arena,
      &other->_impl_.group_info_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DetectedObjects, _impl_.time_pub_)
      + sizeof(DetectedObjects::_impl_.time_pub_)
      - PROTOBUF_FIELD_OFFSET(DetectedObjects, _impl_.time_meas_)>(
          reinterpret_cast<char*>(&_impl_.time_meas_),
          reinterpret_cast<char*>(&other->_impl_.time_meas_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectedObjects::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[0]);
}

// ===================================================================

class LicenePlate::_Internal {
 public:
};

LicenePlate::LicenePlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.LicenePlate)
}
LicenePlate::LicenePlate(const LicenePlate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LicenePlate* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.number_){}
    , decltype(_impl_.color_){}
    , decltype(_impl_.color_confidence_){}
    , decltype(_impl_.number_confidence_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.number_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.number_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_number().empty()) {
    _this->_impl_.number_.Set(from._internal_number(), 
      _this->GetArenaForAllocation());
  }
  ::memcpy(&_impl_.color_, &from._impl_.color_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.number_confidence_) -
    reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.number_confidence_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.LicenePlate)
}

inline void LicenePlate::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.number_){}
    , decltype(_impl_.color_){0}
    , decltype(_impl_.color_confidence_){0}
    , decltype(_impl_.number_confidence_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.number_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.number_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

LicenePlate::~LicenePlate() {
  // @@protoc_insertion_point(destructor:perception.detect.LicenePlate)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LicenePlate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.number_.Destroy();
}

void LicenePlate::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LicenePlate::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.LicenePlate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.number_.ClearToEmpty();
  ::memset(&_impl_.color_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.number_confidence_) -
      reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.number_confidence_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LicenePlate::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 color = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float color_confidence = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.color_confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string number = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_number();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.detect.LicenePlate.number"));
        } else
          goto handle_unusual;
        continue;
      // float number_confidence = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.number_confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LicenePlate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.LicenePlate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 color = 1;
  if (this->_internal_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(1, this->_internal_color(), target);
  }

  // float color_confidence = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = this->_internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_color_confidence(), target);
  }

  // string number = 3;
  if (!this->_internal_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_number().data(), static_cast<int>(this->_internal_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.detect.LicenePlate.number");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_number(), target);
  }

  // float number_confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = this->_internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_number_confidence(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.LicenePlate)
  return target;
}

size_t LicenePlate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.LicenePlate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string number = 3;
  if (!this->_internal_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_number());
  }

  // int32 color = 1;
  if (this->_internal_color() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_color());
  }

  // float color_confidence = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = this->_internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    total_size += 1 + 4;
  }

  // float number_confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = this->_internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LicenePlate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LicenePlate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LicenePlate::GetClassData() const { return &_class_data_; }


void LicenePlate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LicenePlate*>(&to_msg);
  auto& from = static_cast<const LicenePlate&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.LicenePlate)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_number().empty()) {
    _this->_internal_set_number(from._internal_number());
  }
  if (from._internal_color() != 0) {
    _this->_internal_set_color(from._internal_color());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_color_confidence = from._internal_color_confidence();
  uint32_t raw_color_confidence;
  memcpy(&raw_color_confidence, &tmp_color_confidence, sizeof(tmp_color_confidence));
  if (raw_color_confidence != 0) {
    _this->_internal_set_color_confidence(from._internal_color_confidence());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_number_confidence = from._internal_number_confidence();
  uint32_t raw_number_confidence;
  memcpy(&raw_number_confidence, &tmp_number_confidence, sizeof(tmp_number_confidence));
  if (raw_number_confidence != 0) {
    _this->_internal_set_number_confidence(from._internal_number_confidence());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LicenePlate::CopyFrom(const LicenePlate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.LicenePlate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LicenePlate::IsInitialized() const {
  return true;
}

void LicenePlate::InternalSwap(LicenePlate* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.number_, lhs_arena,
      &other->_impl_.number_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LicenePlate, _impl_.number_confidence_)
      + sizeof(LicenePlate::_impl_.number_confidence_)
      - PROTOBUF_FIELD_OFFSET(LicenePlate, _impl_.color_)>(
          reinterpret_cast<char*>(&_impl_.color_),
          reinterpret_cast<char*>(&other->_impl_.color_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LicenePlate::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[1]);
}

// ===================================================================

class TrackConfidence::_Internal {
 public:
};

TrackConfidence::TrackConfidence(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.TrackConfidence)
}
TrackConfidence::TrackConfidence(const TrackConfidence& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  TrackConfidence* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.color_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.plate_number_){}
    , decltype(_impl_.plate_color_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.color_, &from._impl_.color_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.plate_color_) -
    reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.plate_color_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.TrackConfidence)
}

inline void TrackConfidence::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.color_){0u}
    , decltype(_impl_.type_){0u}
    , decltype(_impl_.plate_number_){0u}
    , decltype(_impl_.plate_color_){0u}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

TrackConfidence::~TrackConfidence() {
  // @@protoc_insertion_point(destructor:perception.detect.TrackConfidence)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void TrackConfidence::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TrackConfidence::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void TrackConfidence::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.TrackConfidence)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.color_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.plate_color_) -
      reinterpret_cast<char*>(&_impl_.color_)) + sizeof(_impl_.plate_color_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TrackConfidence::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 color = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 plate_number = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.plate_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 plate_color = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _impl_.plate_color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TrackConfidence::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.TrackConfidence)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 color = 1;
  if (this->_internal_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(1, this->_internal_color(), target);
  }

  // uint32 type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(2, this->_internal_type(), target);
  }

  // uint32 plate_number = 3;
  if (this->_internal_plate_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(3, this->_internal_plate_number(), target);
  }

  // uint32 plate_color = 4;
  if (this->_internal_plate_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteUInt32ToArray(4, this->_internal_plate_color(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.TrackConfidence)
  return target;
}

size_t TrackConfidence::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.TrackConfidence)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 color = 1;
  if (this->_internal_color() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_color());
  }

  // uint32 type = 2;
  if (this->_internal_type() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_type());
  }

  // uint32 plate_number = 3;
  if (this->_internal_plate_number() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_plate_number());
  }

  // uint32 plate_color = 4;
  if (this->_internal_plate_color() != 0) {
    total_size += ::_pbi::WireFormatLite::UInt32SizePlusOne(this->_internal_plate_color());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TrackConfidence::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    TrackConfidence::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TrackConfidence::GetClassData() const { return &_class_data_; }


void TrackConfidence::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<TrackConfidence*>(&to_msg);
  auto& from = static_cast<const TrackConfidence&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.TrackConfidence)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_color() != 0) {
    _this->_internal_set_color(from._internal_color());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  if (from._internal_plate_number() != 0) {
    _this->_internal_set_plate_number(from._internal_plate_number());
  }
  if (from._internal_plate_color() != 0) {
    _this->_internal_set_plate_color(from._internal_plate_color());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TrackConfidence::CopyFrom(const TrackConfidence& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.TrackConfidence)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TrackConfidence::IsInitialized() const {
  return true;
}

void TrackConfidence::InternalSwap(TrackConfidence* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TrackConfidence, _impl_.plate_color_)
      + sizeof(TrackConfidence::_impl_.plate_color_)
      - PROTOBUF_FIELD_OFFSET(TrackConfidence, _impl_.color_)>(
          reinterpret_cast<char*>(&_impl_.color_),
          reinterpret_cast<char*>(&other->_impl_.color_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TrackConfidence::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[2]);
}

// ===================================================================

class DetectedObject::_Internal {
 public:
  static const ::perception::detect::Vector3f& position(const DetectedObject* msg);
  static const ::perception::detect::Vector3f& shape(const DetectedObject* msg);
  static const ::perception::detect::Polygon& hull(const DetectedObject* msg);
  static const ::perception::detect::Velocity& velocity(const DetectedObject* msg);
  static const ::perception::detect::Color& color(const DetectedObject* msg);
  static const ::perception::detect::LicenePlate& plate(const DetectedObject* msg);
  static const ::perception::detect::TrackConfidence& track_conf(const DetectedObject* msg);
  static const ::perception::detect::Vector3f& position_image(const DetectedObject* msg);
  static const ::perception::detect::Vector2f& shape_image(const DetectedObject* msg);
};

const ::perception::detect::Vector3f&
DetectedObject::_Internal::position(const DetectedObject* msg) {
  return *msg->_impl_.position_;
}
const ::perception::detect::Vector3f&
DetectedObject::_Internal::shape(const DetectedObject* msg) {
  return *msg->_impl_.shape_;
}
const ::perception::detect::Polygon&
DetectedObject::_Internal::hull(const DetectedObject* msg) {
  return *msg->_impl_.hull_;
}
const ::perception::detect::Velocity&
DetectedObject::_Internal::velocity(const DetectedObject* msg) {
  return *msg->_impl_.velocity_;
}
const ::perception::detect::Color&
DetectedObject::_Internal::color(const DetectedObject* msg) {
  return *msg->_impl_.color_;
}
const ::perception::detect::LicenePlate&
DetectedObject::_Internal::plate(const DetectedObject* msg) {
  return *msg->_impl_.plate_;
}
const ::perception::detect::TrackConfidence&
DetectedObject::_Internal::track_conf(const DetectedObject* msg) {
  return *msg->_impl_.track_conf_;
}
const ::perception::detect::Vector3f&
DetectedObject::_Internal::position_image(const DetectedObject* msg) {
  return *msg->_impl_.position_image_;
}
const ::perception::detect::Vector2f&
DetectedObject::_Internal::shape_image(const DetectedObject* msg) {
  return *msg->_impl_.shape_image_;
}
void DetectedObject::clear_position() {
  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
}
void DetectedObject::clear_shape() {
  if (GetArenaForAllocation() == nullptr && _impl_.shape_ != nullptr) {
    delete _impl_.shape_;
  }
  _impl_.shape_ = nullptr;
}
void DetectedObject::clear_hull() {
  if (GetArenaForAllocation() == nullptr && _impl_.hull_ != nullptr) {
    delete _impl_.hull_;
  }
  _impl_.hull_ = nullptr;
}
void DetectedObject::clear_color() {
  if (GetArenaForAllocation() == nullptr && _impl_.color_ != nullptr) {
    delete _impl_.color_;
  }
  _impl_.color_ = nullptr;
}
void DetectedObject::clear_position_image() {
  if (GetArenaForAllocation() == nullptr && _impl_.position_image_ != nullptr) {
    delete _impl_.position_image_;
  }
  _impl_.position_image_ = nullptr;
}
void DetectedObject::clear_shape_image() {
  if (GetArenaForAllocation() == nullptr && _impl_.shape_image_ != nullptr) {
    delete _impl_.shape_image_;
  }
  _impl_.shape_image_ = nullptr;
}
DetectedObject::DetectedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.DetectedObject)
}
DetectedObject::DetectedObject(const DetectedObject& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  DetectedObject* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.feature_){from._impl_.feature_}
    , decltype(_impl_.trajectories_){from._impl_.trajectories_}
    , decltype(_impl_.str_array_){from._impl_.str_array_}
    , decltype(_impl_.int_array_){from._impl_.int_array_}
    , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
    , decltype(_impl_.overlap_name_){}
    , decltype(_impl_.group_id_){}
    , decltype(_impl_.camera_name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.shape_){nullptr}
    , decltype(_impl_.hull_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.color_){nullptr}
    , decltype(_impl_.plate_){nullptr}
    , decltype(_impl_.track_conf_){nullptr}
    , decltype(_impl_.position_image_){nullptr}
    , decltype(_impl_.shape_image_){nullptr}
    , decltype(_impl_.uuid_){}
    , decltype(_impl_.type_){}
    , decltype(_impl_.confidence_){}
    , decltype(_impl_.orientation_){}
    , decltype(_impl_.is_static_){}
    , decltype(_impl_.parking_time_){}
    , decltype(_impl_.obj_color_){}
    , decltype(_impl_.obj_color_conf_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.overlap_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_overlap_name().empty()) {
    _this->_impl_.overlap_name_.Set(from._internal_overlap_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.group_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_id().empty()) {
    _this->_impl_.group_id_.Set(from._internal_group_id(), 
      _this->GetArenaForAllocation());
  }
  _impl_.camera_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.camera_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera_name().empty()) {
    _this->_impl_.camera_name_.Set(from._internal_camera_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_position()) {
    _this->_impl_.position_ = new ::perception::detect::Vector3f(*from._impl_.position_);
  }
  if (from._internal_has_shape()) {
    _this->_impl_.shape_ = new ::perception::detect::Vector3f(*from._impl_.shape_);
  }
  if (from._internal_has_hull()) {
    _this->_impl_.hull_ = new ::perception::detect::Polygon(*from._impl_.hull_);
  }
  if (from._internal_has_velocity()) {
    _this->_impl_.velocity_ = new ::perception::detect::Velocity(*from._impl_.velocity_);
  }
  if (from._internal_has_color()) {
    _this->_impl_.color_ = new ::perception::detect::Color(*from._impl_.color_);
  }
  if (from._internal_has_plate()) {
    _this->_impl_.plate_ = new ::perception::detect::LicenePlate(*from._impl_.plate_);
  }
  if (from._internal_has_track_conf()) {
    _this->_impl_.track_conf_ = new ::perception::detect::TrackConfidence(*from._impl_.track_conf_);
  }
  if (from._internal_has_position_image()) {
    _this->_impl_.position_image_ = new ::perception::detect::Vector3f(*from._impl_.position_image_);
  }
  if (from._internal_has_shape_image()) {
    _this->_impl_.shape_image_ = new ::perception::detect::Vector2f(*from._impl_.shape_image_);
  }
  ::memcpy(&_impl_.uuid_, &from._impl_.uuid_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.obj_color_conf_) -
    reinterpret_cast<char*>(&_impl_.uuid_)) + sizeof(_impl_.obj_color_conf_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.DetectedObject)
}

inline void DetectedObject::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.feature_){arena}
    , decltype(_impl_.trajectories_){arena}
    , decltype(_impl_.str_array_){arena}
    , decltype(_impl_.int_array_){arena}
    , /*decltype(_impl_._int_array_cached_byte_size_)*/{0}
    , decltype(_impl_.overlap_name_){}
    , decltype(_impl_.group_id_){}
    , decltype(_impl_.camera_name_){}
    , decltype(_impl_.position_){nullptr}
    , decltype(_impl_.shape_){nullptr}
    , decltype(_impl_.hull_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.color_){nullptr}
    , decltype(_impl_.plate_){nullptr}
    , decltype(_impl_.track_conf_){nullptr}
    , decltype(_impl_.position_image_){nullptr}
    , decltype(_impl_.shape_image_){nullptr}
    , decltype(_impl_.uuid_){uint64_t{0u}}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.confidence_){0}
    , decltype(_impl_.orientation_){0}
    , decltype(_impl_.is_static_){false}
    , decltype(_impl_.parking_time_){int64_t{0}}
    , decltype(_impl_.obj_color_){0}
    , decltype(_impl_.obj_color_conf_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.overlap_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.group_id_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_id_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.camera_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.camera_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DetectedObject::~DetectedObject() {
  // @@protoc_insertion_point(destructor:perception.detect.DetectedObject)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void DetectedObject::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.feature_.~RepeatedField();
  _impl_.trajectories_.~RepeatedPtrField();
  _impl_.str_array_.~RepeatedPtrField();
  _impl_.int_array_.~RepeatedField();
  _impl_.overlap_name_.Destroy();
  _impl_.group_id_.Destroy();
  _impl_.camera_name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.position_;
  if (this != internal_default_instance()) delete _impl_.shape_;
  if (this != internal_default_instance()) delete _impl_.hull_;
  if (this != internal_default_instance()) delete _impl_.velocity_;
  if (this != internal_default_instance()) delete _impl_.color_;
  if (this != internal_default_instance()) delete _impl_.plate_;
  if (this != internal_default_instance()) delete _impl_.track_conf_;
  if (this != internal_default_instance()) delete _impl_.position_image_;
  if (this != internal_default_instance()) delete _impl_.shape_image_;
}

void DetectedObject::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void DetectedObject::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.DetectedObject)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.feature_.Clear();
  _impl_.trajectories_.Clear();
  _impl_.str_array_.Clear();
  _impl_.int_array_.Clear();
  _impl_.overlap_name_.ClearToEmpty();
  _impl_.group_id_.ClearToEmpty();
  _impl_.camera_name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.shape_ != nullptr) {
    delete _impl_.shape_;
  }
  _impl_.shape_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.hull_ != nullptr) {
    delete _impl_.hull_;
  }
  _impl_.hull_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.color_ != nullptr) {
    delete _impl_.color_;
  }
  _impl_.color_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.plate_ != nullptr) {
    delete _impl_.plate_;
  }
  _impl_.plate_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.track_conf_ != nullptr) {
    delete _impl_.track_conf_;
  }
  _impl_.track_conf_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.position_image_ != nullptr) {
    delete _impl_.position_image_;
  }
  _impl_.position_image_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.shape_image_ != nullptr) {
    delete _impl_.shape_image_;
  }
  _impl_.shape_image_ = nullptr;
  ::memset(&_impl_.uuid_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.obj_color_conf_) -
      reinterpret_cast<char*>(&_impl_.uuid_)) + sizeof(_impl_.obj_color_conf_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectedObject::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes overlap_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_overlap_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // fixed64 uuid = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.uuid_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<uint64_t>(ptr);
          ptr += sizeof(uint64_t);
        } else
          goto handle_unusual;
        continue;
      // sint32 type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float confidence = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.confidence_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Vector3f position = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Vector3f shape = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_shape(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Polygon hull = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_hull(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float orientation = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.orientation_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Velocity velocity = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_velocity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_static = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          _impl_.is_static_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Color color = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_color(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated float feature = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_feature(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 101) {
          _internal_add_feature(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.detect.WayPoints trajectories = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_trajectories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes str_array = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_str_array();
            ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated sint32 int_array = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedSInt32Parser(_internal_mutable_int_array(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 120) {
          _internal_add_int_array(::PROTOBUF_NAMESPACE_ID::internal::ReadVarintZigZag32(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 parking_time = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          _impl_.parking_time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.LicenePlate plate = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_plate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 obj_color = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 144)) {
          _impl_.obj_color_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string group_id = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          auto str = _internal_mutable_group_id();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.detect.DetectedObject.group_id"));
        } else
          goto handle_unusual;
        continue;
      // float obj_color_conf = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 165)) {
          _impl_.obj_color_conf_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.TrackConfidence track_conf = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr = ctx->ParseMessage(_internal_mutable_track_conf(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string camera_name = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          auto str = _internal_mutable_camera_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
          CHK_(::_pbi::VerifyUTF8(str, "perception.detect.DetectedObject.camera_name"));
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Vector3f position_image = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 186)) {
          ptr = ctx->ParseMessage(_internal_mutable_position_image(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Vector2f shape_image = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 194)) {
          ptr = ctx->ParseMessage(_internal_mutable_shape_image(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectedObject::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.DetectedObject)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes overlap_name = 1;
  if (!this->_internal_overlap_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_overlap_name(), target);
  }

  // fixed64 uuid = 2;
  if (this->_internal_uuid() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFixed64ToArray(2, this->_internal_uuid(), target);
  }

  // sint32 type = 3;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSInt32ToArray(3, this->_internal_type(), target);
  }

  // float confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_confidence(), target);
  }

  // .perception.detect.Vector3f position = 5;
  if (this->_internal_has_position()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(5, _Internal::position(this),
        _Internal::position(this).GetCachedSize(), target, stream);
  }

  // .perception.detect.Vector3f shape = 6;
  if (this->_internal_has_shape()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::shape(this),
        _Internal::shape(this).GetCachedSize(), target, stream);
  }

  // .perception.detect.Polygon hull = 7;
  if (this->_internal_has_hull()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, _Internal::hull(this),
        _Internal::hull(this).GetCachedSize(), target, stream);
  }

  // float orientation = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_orientation(), target);
  }

  // .perception.detect.Velocity velocity = 9;
  if (this->_internal_has_velocity()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(9, _Internal::velocity(this),
        _Internal::velocity(this).GetCachedSize(), target, stream);
  }

  // bool is_static = 10;
  if (this->_internal_is_static() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(10, this->_internal_is_static(), target);
  }

  // .perception.detect.Color color = 11;
  if (this->_internal_has_color()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(11, _Internal::color(this),
        _Internal::color(this).GetCachedSize(), target, stream);
  }

  // repeated float feature = 12;
  if (this->_internal_feature_size() > 0) {
    target = stream->WriteFixedPacked(12, _internal_feature(), target);
  }

  // repeated .perception.detect.WayPoints trajectories = 13;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_trajectories_size()); i < n; i++) {
    const auto& repfield = this->_internal_trajectories(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(13, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated bytes str_array = 14;
  for (int i = 0, n = this->_internal_str_array_size(); i < n; i++) {
    const auto& s = this->_internal_str_array(i);
    target = stream->WriteBytes(14, s, target);
  }

  // repeated sint32 int_array = 15;
  {
    int byte_size = _impl_._int_array_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteSInt32Packed(
          15, _internal_int_array(), byte_size, target);
    }
  }

  // int64 parking_time = 16;
  if (this->_internal_parking_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt64ToArray(16, this->_internal_parking_time(), target);
  }

  // .perception.detect.LicenePlate plate = 17;
  if (this->_internal_has_plate()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(17, _Internal::plate(this),
        _Internal::plate(this).GetCachedSize(), target, stream);
  }

  // int32 obj_color = 18;
  if (this->_internal_obj_color() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(18, this->_internal_obj_color(), target);
  }

  // string group_id = 19;
  if (!this->_internal_group_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_group_id().data(), static_cast<int>(this->_internal_group_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.detect.DetectedObject.group_id");
    target = stream->WriteStringMaybeAliased(
        19, this->_internal_group_id(), target);
  }

  // float obj_color_conf = 20;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_obj_color_conf = this->_internal_obj_color_conf();
  uint32_t raw_obj_color_conf;
  memcpy(&raw_obj_color_conf, &tmp_obj_color_conf, sizeof(tmp_obj_color_conf));
  if (raw_obj_color_conf != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(20, this->_internal_obj_color_conf(), target);
  }

  // .perception.detect.TrackConfidence track_conf = 21;
  if (this->_internal_has_track_conf()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(21, _Internal::track_conf(this),
        _Internal::track_conf(this).GetCachedSize(), target, stream);
  }

  // string camera_name = 22;
  if (!this->_internal_camera_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera_name().data(), static_cast<int>(this->_internal_camera_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "perception.detect.DetectedObject.camera_name");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_camera_name(), target);
  }

  // .perception.detect.Vector3f position_image = 23;
  if (this->_internal_has_position_image()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(23, _Internal::position_image(this),
        _Internal::position_image(this).GetCachedSize(), target, stream);
  }

  // .perception.detect.Vector2f shape_image = 24;
  if (this->_internal_has_shape_image()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(24, _Internal::shape_image(this),
        _Internal::shape_image(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.DetectedObject)
  return target;
}

size_t DetectedObject::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.DetectedObject)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float feature = 12;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_feature_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated .perception.detect.WayPoints trajectories = 13;
  total_size += 1UL * this->_internal_trajectories_size();
  for (const auto& msg : this->_impl_.trajectories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated bytes str_array = 14;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(_impl_.str_array_.size());
  for (int i = 0, n = _impl_.str_array_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      _impl_.str_array_.Get(i));
  }

  // repeated sint32 int_array = 15;
  {
    size_t data_size = ::_pbi::WireFormatLite::
      SInt32Size(this->_impl_.int_array_);
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    int cached_size = ::_pbi::ToCachedSize(data_size);
    _impl_._int_array_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // bytes overlap_name = 1;
  if (!this->_internal_overlap_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_overlap_name());
  }

  // string group_id = 19;
  if (!this->_internal_group_id().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_group_id());
  }

  // string camera_name = 22;
  if (!this->_internal_camera_name().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera_name());
  }

  // .perception.detect.Vector3f position = 5;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_);
  }

  // .perception.detect.Vector3f shape = 6;
  if (this->_internal_has_shape()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.shape_);
  }

  // .perception.detect.Polygon hull = 7;
  if (this->_internal_has_hull()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.hull_);
  }

  // .perception.detect.Velocity velocity = 9;
  if (this->_internal_has_velocity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.velocity_);
  }

  // .perception.detect.Color color = 11;
  if (this->_internal_has_color()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.color_);
  }

  // .perception.detect.LicenePlate plate = 17;
  if (this->_internal_has_plate()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.plate_);
  }

  // .perception.detect.TrackConfidence track_conf = 21;
  if (this->_internal_has_track_conf()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.track_conf_);
  }

  // .perception.detect.Vector3f position_image = 23;
  if (this->_internal_has_position_image()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_image_);
  }

  // .perception.detect.Vector2f shape_image = 24;
  if (this->_internal_has_shape_image()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.shape_image_);
  }

  // fixed64 uuid = 2;
  if (this->_internal_uuid() != 0) {
    total_size += 1 + 8;
  }

  // sint32 type = 3;
  if (this->_internal_type() != 0) {
    total_size += ::_pbi::WireFormatLite::SInt32SizePlusOne(this->_internal_type());
  }

  // float confidence = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = this->_internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    total_size += 1 + 4;
  }

  // float orientation = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = this->_internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    total_size += 1 + 4;
  }

  // bool is_static = 10;
  if (this->_internal_is_static() != 0) {
    total_size += 1 + 1;
  }

  // int64 parking_time = 16;
  if (this->_internal_parking_time() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int64Size(
        this->_internal_parking_time());
  }

  // int32 obj_color = 18;
  if (this->_internal_obj_color() != 0) {
    total_size += 2 +
      ::_pbi::WireFormatLite::Int32Size(
        this->_internal_obj_color());
  }

  // float obj_color_conf = 20;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_obj_color_conf = this->_internal_obj_color_conf();
  uint32_t raw_obj_color_conf;
  memcpy(&raw_obj_color_conf, &tmp_obj_color_conf, sizeof(tmp_obj_color_conf));
  if (raw_obj_color_conf != 0) {
    total_size += 2 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectedObject::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    DetectedObject::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectedObject::GetClassData() const { return &_class_data_; }


void DetectedObject::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<DetectedObject*>(&to_msg);
  auto& from = static_cast<const DetectedObject&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.DetectedObject)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.feature_.MergeFrom(from._impl_.feature_);
  _this->_impl_.trajectories_.MergeFrom(from._impl_.trajectories_);
  _this->_impl_.str_array_.MergeFrom(from._impl_.str_array_);
  _this->_impl_.int_array_.MergeFrom(from._impl_.int_array_);
  if (!from._internal_overlap_name().empty()) {
    _this->_internal_set_overlap_name(from._internal_overlap_name());
  }
  if (!from._internal_group_id().empty()) {
    _this->_internal_set_group_id(from._internal_group_id());
  }
  if (!from._internal_camera_name().empty()) {
    _this->_internal_set_camera_name(from._internal_camera_name());
  }
  if (from._internal_has_position()) {
    _this->_internal_mutable_position()->::perception::detect::Vector3f::MergeFrom(
        from._internal_position());
  }
  if (from._internal_has_shape()) {
    _this->_internal_mutable_shape()->::perception::detect::Vector3f::MergeFrom(
        from._internal_shape());
  }
  if (from._internal_has_hull()) {
    _this->_internal_mutable_hull()->::perception::detect::Polygon::MergeFrom(
        from._internal_hull());
  }
  if (from._internal_has_velocity()) {
    _this->_internal_mutable_velocity()->::perception::detect::Velocity::MergeFrom(
        from._internal_velocity());
  }
  if (from._internal_has_color()) {
    _this->_internal_mutable_color()->::perception::detect::Color::MergeFrom(
        from._internal_color());
  }
  if (from._internal_has_plate()) {
    _this->_internal_mutable_plate()->::perception::detect::LicenePlate::MergeFrom(
        from._internal_plate());
  }
  if (from._internal_has_track_conf()) {
    _this->_internal_mutable_track_conf()->::perception::detect::TrackConfidence::MergeFrom(
        from._internal_track_conf());
  }
  if (from._internal_has_position_image()) {
    _this->_internal_mutable_position_image()->::perception::detect::Vector3f::MergeFrom(
        from._internal_position_image());
  }
  if (from._internal_has_shape_image()) {
    _this->_internal_mutable_shape_image()->::perception::detect::Vector2f::MergeFrom(
        from._internal_shape_image());
  }
  if (from._internal_uuid() != 0) {
    _this->_internal_set_uuid(from._internal_uuid());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_confidence = from._internal_confidence();
  uint32_t raw_confidence;
  memcpy(&raw_confidence, &tmp_confidence, sizeof(tmp_confidence));
  if (raw_confidence != 0) {
    _this->_internal_set_confidence(from._internal_confidence());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_orientation = from._internal_orientation();
  uint32_t raw_orientation;
  memcpy(&raw_orientation, &tmp_orientation, sizeof(tmp_orientation));
  if (raw_orientation != 0) {
    _this->_internal_set_orientation(from._internal_orientation());
  }
  if (from._internal_is_static() != 0) {
    _this->_internal_set_is_static(from._internal_is_static());
  }
  if (from._internal_parking_time() != 0) {
    _this->_internal_set_parking_time(from._internal_parking_time());
  }
  if (from._internal_obj_color() != 0) {
    _this->_internal_set_obj_color(from._internal_obj_color());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_obj_color_conf = from._internal_obj_color_conf();
  uint32_t raw_obj_color_conf;
  memcpy(&raw_obj_color_conf, &tmp_obj_color_conf, sizeof(tmp_obj_color_conf));
  if (raw_obj_color_conf != 0) {
    _this->_internal_set_obj_color_conf(from._internal_obj_color_conf());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectedObject::CopyFrom(const DetectedObject& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.DetectedObject)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectedObject::IsInitialized() const {
  return true;
}

void DetectedObject::InternalSwap(DetectedObject* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.feature_.InternalSwap(&other->_impl_.feature_);
  _impl_.trajectories_.InternalSwap(&other->_impl_.trajectories_);
  _impl_.str_array_.InternalSwap(&other->_impl_.str_array_);
  _impl_.int_array_.InternalSwap(&other->_impl_.int_array_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.overlap_name_, lhs_arena,
      &other->_impl_.overlap_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_id_, lhs_arena,
      &other->_impl_.group_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.camera_name_, lhs_arena,
      &other->_impl_.camera_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DetectedObject, _impl_.obj_color_conf_)
      + sizeof(DetectedObject::_impl_.obj_color_conf_)
      - PROTOBUF_FIELD_OFFSET(DetectedObject, _impl_.position_)>(
          reinterpret_cast<char*>(&_impl_.position_),
          reinterpret_cast<char*>(&other->_impl_.position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectedObject::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[3]);
}

// ===================================================================

class Velocity::_Internal {
 public:
};

Velocity::Velocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.Velocity)
}
Velocity::Velocity(const Velocity& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Velocity* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.heading_){}
    , decltype(_impl_.speed_){}
    , decltype(_impl_.acceleration_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.heading_, &from._impl_.heading_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.acceleration_) -
    reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.acceleration_));
  // @@protoc_insertion_point(copy_constructor:perception.detect.Velocity)
}

inline void Velocity::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.heading_){0}
    , decltype(_impl_.speed_){0}
    , decltype(_impl_.acceleration_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Velocity::~Velocity() {
  // @@protoc_insertion_point(destructor:perception.detect.Velocity)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Velocity::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Velocity::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Velocity::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.Velocity)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.heading_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.acceleration_) -
      reinterpret_cast<char*>(&_impl_.heading_)) + sizeof(_impl_.acceleration_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Velocity::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float heading = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          _impl_.heading_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float speed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.speed_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float acceleration = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          _impl_.acceleration_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Velocity::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.Velocity)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(1, this->_internal_heading(), target);
  }

  // float speed = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = this->_internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_speed(), target);
  }

  // float acceleration = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = this->_internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(3, this->_internal_acceleration(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.Velocity)
  return target;
}

size_t Velocity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.Velocity)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float heading = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = this->_internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    total_size += 1 + 4;
  }

  // float speed = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = this->_internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    total_size += 1 + 4;
  }

  // float acceleration = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = this->_internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Velocity::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Velocity::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Velocity::GetClassData() const { return &_class_data_; }


void Velocity::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Velocity*>(&to_msg);
  auto& from = static_cast<const Velocity&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.Velocity)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_heading = from._internal_heading();
  uint32_t raw_heading;
  memcpy(&raw_heading, &tmp_heading, sizeof(tmp_heading));
  if (raw_heading != 0) {
    _this->_internal_set_heading(from._internal_heading());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_speed = from._internal_speed();
  uint32_t raw_speed;
  memcpy(&raw_speed, &tmp_speed, sizeof(tmp_speed));
  if (raw_speed != 0) {
    _this->_internal_set_speed(from._internal_speed());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_acceleration = from._internal_acceleration();
  uint32_t raw_acceleration;
  memcpy(&raw_acceleration, &tmp_acceleration, sizeof(tmp_acceleration));
  if (raw_acceleration != 0) {
    _this->_internal_set_acceleration(from._internal_acceleration());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Velocity::CopyFrom(const Velocity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.Velocity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Velocity::IsInitialized() const {
  return true;
}

void Velocity::InternalSwap(Velocity* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Velocity, _impl_.acceleration_)
      + sizeof(Velocity::_impl_.acceleration_)
      - PROTOBUF_FIELD_OFFSET(Velocity, _impl_.heading_)>(
          reinterpret_cast<char*>(&_impl_.heading_),
          reinterpret_cast<char*>(&other->_impl_.heading_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Velocity::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[4]);
}

// ===================================================================

class WayPoints::_Internal {
 public:
};

WayPoints::WayPoints(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.WayPoints)
}
WayPoints::WayPoints(const WayPoints& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  WayPoints* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.waypoints_){from._impl_.waypoints_}
    , decltype(_impl_.probability_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _this->_impl_.probability_ = from._impl_.probability_;
  // @@protoc_insertion_point(copy_constructor:perception.detect.WayPoints)
}

inline void WayPoints::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.waypoints_){arena}
    , decltype(_impl_.probability_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

WayPoints::~WayPoints() {
  // @@protoc_insertion_point(destructor:perception.detect.WayPoints)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void WayPoints::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.waypoints_.~RepeatedPtrField();
}

void WayPoints::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void WayPoints::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.WayPoints)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.waypoints_.Clear();
  _impl_.probability_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WayPoints::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .perception.detect.WayPoint waypoints = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_waypoints(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float probability = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          _impl_.probability_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WayPoints::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.WayPoints)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .perception.detect.WayPoint waypoints = 1;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_waypoints_size()); i < n; i++) {
    const auto& repfield = this->_internal_waypoints(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(1, repfield, repfield.GetCachedSize(), target, stream);
  }

  // float probability = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = this->_internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(2, this->_internal_probability(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.WayPoints)
  return target;
}

size_t WayPoints::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.WayPoints)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.detect.WayPoint waypoints = 1;
  total_size += 1UL * this->_internal_waypoints_size();
  for (const auto& msg : this->_impl_.waypoints_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // float probability = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = this->_internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WayPoints::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    WayPoints::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WayPoints::GetClassData() const { return &_class_data_; }


void WayPoints::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<WayPoints*>(&to_msg);
  auto& from = static_cast<const WayPoints&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.WayPoints)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.waypoints_.MergeFrom(from._impl_.waypoints_);
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_probability = from._internal_probability();
  uint32_t raw_probability;
  memcpy(&raw_probability, &tmp_probability, sizeof(tmp_probability));
  if (raw_probability != 0) {
    _this->_internal_set_probability(from._internal_probability());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WayPoints::CopyFrom(const WayPoints& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.WayPoints)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WayPoints::IsInitialized() const {
  return true;
}

void WayPoints::InternalSwap(WayPoints* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.waypoints_.InternalSwap(&other->_impl_.waypoints_);
  swap(_impl_.probability_, other->_impl_.probability_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WayPoints::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[5]);
}

// ===================================================================

class WayPoint::_Internal {
 public:
  static const ::perception::detect::Vector3f& position(const WayPoint* msg);
  static const ::perception::detect::Velocity& velocity(const WayPoint* msg);
};

const ::perception::detect::Vector3f&
WayPoint::_Internal::position(const WayPoint* msg) {
  return *msg->_impl_.position_;
}
const ::perception::detect::Velocity&
WayPoint::_Internal::velocity(const WayPoint* msg) {
  return *msg->_impl_.velocity_;
}
void WayPoint::clear_position() {
  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
}
WayPoint::WayPoint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.detect.WayPoint)
}
WayPoint::WayPoint(const WayPoint& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  WayPoint* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.position_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.time_meas_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_position()) {
    _this->_impl_.position_ = new ::perception::detect::Vector3f(*from._impl_.position_);
  }
  if (from._internal_has_velocity()) {
    _this->_impl_.velocity_ = new ::perception::detect::Velocity(*from._impl_.velocity_);
  }
  _this->_impl_.time_meas_ = from._impl_.time_meas_;
  // @@protoc_insertion_point(copy_constructor:perception.detect.WayPoint)
}

inline void WayPoint::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.position_){nullptr}
    , decltype(_impl_.velocity_){nullptr}
    , decltype(_impl_.time_meas_){int64_t{0}}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

WayPoint::~WayPoint() {
  // @@protoc_insertion_point(destructor:perception.detect.WayPoint)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void WayPoint::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.position_;
  if (this != internal_default_instance()) delete _impl_.velocity_;
}

void WayPoint::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void WayPoint::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.detect.WayPoint)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.position_ != nullptr) {
    delete _impl_.position_;
  }
  _impl_.position_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
  _impl_.time_meas_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WayPoint::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // sfixed64 time_meas = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.time_meas_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<int64_t>(ptr);
          ptr += sizeof(int64_t);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Vector3f position = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_position(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.detect.Velocity velocity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_velocity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WayPoint::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.detect.WayPoint)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteSFixed64ToArray(1, this->_internal_time_meas(), target);
  }

  // .perception.detect.Vector3f position = 2;
  if (this->_internal_has_position()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::position(this),
        _Internal::position(this).GetCachedSize(), target, stream);
  }

  // .perception.detect.Velocity velocity = 3;
  if (this->_internal_has_velocity()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::velocity(this),
        _Internal::velocity(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.detect.WayPoint)
  return target;
}

size_t WayPoint::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.detect.WayPoint)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .perception.detect.Vector3f position = 2;
  if (this->_internal_has_position()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.position_);
  }

  // .perception.detect.Velocity velocity = 3;
  if (this->_internal_has_velocity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.velocity_);
  }

  // sfixed64 time_meas = 1;
  if (this->_internal_time_meas() != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WayPoint::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    WayPoint::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WayPoint::GetClassData() const { return &_class_data_; }


void WayPoint::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<WayPoint*>(&to_msg);
  auto& from = static_cast<const WayPoint&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.detect.WayPoint)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_position()) {
    _this->_internal_mutable_position()->::perception::detect::Vector3f::MergeFrom(
        from._internal_position());
  }
  if (from._internal_has_velocity()) {
    _this->_internal_mutable_velocity()->::perception::detect::Velocity::MergeFrom(
        from._internal_velocity());
  }
  if (from._internal_time_meas() != 0) {
    _this->_internal_set_time_meas(from._internal_time_meas());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WayPoint::CopyFrom(const WayPoint& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.detect.WayPoint)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WayPoint::IsInitialized() const {
  return true;
}

void WayPoint::InternalSwap(WayPoint* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WayPoint, _impl_.time_meas_)
      + sizeof(WayPoint::_impl_.time_meas_)
      - PROTOBUF_FIELD_OFFSET(WayPoint, _impl_.position_)>(
          reinterpret_cast<char*>(&_impl_.position_),
          reinterpret_cast<char*>(&other->_impl_.position_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WayPoint::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_5fdetected_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace detect
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::perception::detect::DetectedObjects*
Arena::CreateMaybeMessage< ::perception::detect::DetectedObjects >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::DetectedObjects >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::LicenePlate*
Arena::CreateMaybeMessage< ::perception::detect::LicenePlate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::LicenePlate >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::TrackConfidence*
Arena::CreateMaybeMessage< ::perception::detect::TrackConfidence >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::TrackConfidence >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::DetectedObject*
Arena::CreateMaybeMessage< ::perception::detect::DetectedObject >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::DetectedObject >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::Velocity*
Arena::CreateMaybeMessage< ::perception::detect::Velocity >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::Velocity >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::WayPoints*
Arena::CreateMaybeMessage< ::perception::detect::WayPoints >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::WayPoints >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::detect::WayPoint*
Arena::CreateMaybeMessage< ::perception::detect::WayPoint >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::detect::WayPoint >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
