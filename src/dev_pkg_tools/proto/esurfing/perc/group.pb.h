// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/perc/group.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021010 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "esurfing/math/geometry.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_esurfing_2fperc_2fgroup_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_esurfing_2fperc_2fgroup_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_esurfing_2fperc_2fgroup_2eproto;
namespace perception {
namespace config {
class Camera;
struct CameraDefaultTypeInternal;
extern CameraDefaultTypeInternal _Camera_default_instance_;
class ConfigGroup;
struct ConfigGroupDefaultTypeInternal;
extern ConfigGroupDefaultTypeInternal _ConfigGroup_default_instance_;
class Lidar;
struct LidarDefaultTypeInternal;
extern LidarDefaultTypeInternal _Lidar_default_instance_;
class LidarIntrisic;
struct LidarIntrisicDefaultTypeInternal;
extern LidarIntrisicDefaultTypeInternal _LidarIntrisic_default_instance_;
class Radar;
struct RadarDefaultTypeInternal;
extern RadarDefaultTypeInternal _Radar_default_instance_;
}  // namespace config
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> ::perception::config::Camera* Arena::CreateMaybeMessage<::perception::config::Camera>(Arena*);
template<> ::perception::config::ConfigGroup* Arena::CreateMaybeMessage<::perception::config::ConfigGroup>(Arena*);
template<> ::perception::config::Lidar* Arena::CreateMaybeMessage<::perception::config::Lidar>(Arena*);
template<> ::perception::config::LidarIntrisic* Arena::CreateMaybeMessage<::perception::config::LidarIntrisic>(Arena*);
template<> ::perception::config::Radar* Arena::CreateMaybeMessage<::perception::config::Radar>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace perception {
namespace config {

enum Lidar_Type : int {
  Lidar_Type_UNKNOWN = 0,
  Lidar_Type_VLP_16 = 1,
  Lidar_Type_VLP_32 = 2,
  Lidar_Type_HESAI_64 = 3,
  Lidar_Type_Lidar_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Lidar_Type_Lidar_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Lidar_Type_IsValid(int value);
constexpr Lidar_Type Lidar_Type_Type_MIN = Lidar_Type_UNKNOWN;
constexpr Lidar_Type Lidar_Type_Type_MAX = Lidar_Type_HESAI_64;
constexpr int Lidar_Type_Type_ARRAYSIZE = Lidar_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lidar_Type_descriptor();
template<typename T>
inline const std::string& Lidar_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Lidar_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Lidar_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Lidar_Type_descriptor(), enum_t_value);
}
inline bool Lidar_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Lidar_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Lidar_Type>(
    Lidar_Type_descriptor(), name, value);
}
enum Radar_Type : int {
  Radar_Type_UNKNOWN = 0,
  Radar_Type_ARBE = 1,
  Radar_Type_Radar_Type_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Radar_Type_Radar_Type_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Radar_Type_IsValid(int value);
constexpr Radar_Type Radar_Type_Type_MIN = Radar_Type_UNKNOWN;
constexpr Radar_Type Radar_Type_Type_MAX = Radar_Type_ARBE;
constexpr int Radar_Type_Type_ARRAYSIZE = Radar_Type_Type_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Radar_Type_descriptor();
template<typename T>
inline const std::string& Radar_Type_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Radar_Type>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Radar_Type_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Radar_Type_descriptor(), enum_t_value);
}
inline bool Radar_Type_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Radar_Type* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Radar_Type>(
    Radar_Type_descriptor(), name, value);
}
// ===================================================================

class LidarIntrisic final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.LidarIntrisic) */ {
 public:
  inline LidarIntrisic() : LidarIntrisic(nullptr) {}
  ~LidarIntrisic() override;
  explicit PROTOBUF_CONSTEXPR LidarIntrisic(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LidarIntrisic(const LidarIntrisic& from);
  LidarIntrisic(LidarIntrisic&& from) noexcept
    : LidarIntrisic() {
    *this = ::std::move(from);
  }

  inline LidarIntrisic& operator=(const LidarIntrisic& from) {
    CopyFrom(from);
    return *this;
  }
  inline LidarIntrisic& operator=(LidarIntrisic&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LidarIntrisic& default_instance() {
    return *internal_default_instance();
  }
  static inline const LidarIntrisic* internal_default_instance() {
    return reinterpret_cast<const LidarIntrisic*>(
               &_LidarIntrisic_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LidarIntrisic& a, LidarIntrisic& b) {
    a.Swap(&b);
  }
  inline void Swap(LidarIntrisic* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LidarIntrisic* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LidarIntrisic* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LidarIntrisic>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LidarIntrisic& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LidarIntrisic& from) {
    LidarIntrisic::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LidarIntrisic* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.LidarIntrisic";
  }
  protected:
  explicit LidarIntrisic(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
    kMinIntensityFieldNumber = 2,
    kMaxIntensityFieldNumber = 3,
    kRotFieldNumber = 4,
    kVertFieldNumber = 5,
    kDistFieldNumber = 6,
    kDistXFieldNumber = 7,
    kDistYFieldNumber = 8,
    kVertOffsetFieldNumber = 9,
    kHorizOffsetFieldNumber = 10,
    kFocalDistanceFieldNumber = 11,
    kFocalScopeFieldNumber = 12,
    kRingFieldNumber = 13,
    kCosRotFieldNumber = 14,
    kSinRotFieldNumber = 15,
    kCosVertFieldNumber = 16,
    kSinVertFieldNumber = 17,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // int32 min_intensity = 2;
  void clear_min_intensity();
  int32_t min_intensity() const;
  void set_min_intensity(int32_t value);
  private:
  int32_t _internal_min_intensity() const;
  void _internal_set_min_intensity(int32_t value);
  public:

  // int32 max_intensity = 3;
  void clear_max_intensity();
  int32_t max_intensity() const;
  void set_max_intensity(int32_t value);
  private:
  int32_t _internal_max_intensity() const;
  void _internal_set_max_intensity(int32_t value);
  public:

  // float rot = 4;
  void clear_rot();
  float rot() const;
  void set_rot(float value);
  private:
  float _internal_rot() const;
  void _internal_set_rot(float value);
  public:

  // float vert = 5;
  void clear_vert();
  float vert() const;
  void set_vert(float value);
  private:
  float _internal_vert() const;
  void _internal_set_vert(float value);
  public:

  // float dist = 6;
  void clear_dist();
  float dist() const;
  void set_dist(float value);
  private:
  float _internal_dist() const;
  void _internal_set_dist(float value);
  public:

  // float dist_x = 7;
  void clear_dist_x();
  float dist_x() const;
  void set_dist_x(float value);
  private:
  float _internal_dist_x() const;
  void _internal_set_dist_x(float value);
  public:

  // float dist_y = 8;
  void clear_dist_y();
  float dist_y() const;
  void set_dist_y(float value);
  private:
  float _internal_dist_y() const;
  void _internal_set_dist_y(float value);
  public:

  // float vert_offset = 9;
  void clear_vert_offset();
  float vert_offset() const;
  void set_vert_offset(float value);
  private:
  float _internal_vert_offset() const;
  void _internal_set_vert_offset(float value);
  public:

  // float horiz_offset = 10;
  void clear_horiz_offset();
  float horiz_offset() const;
  void set_horiz_offset(float value);
  private:
  float _internal_horiz_offset() const;
  void _internal_set_horiz_offset(float value);
  public:

  // float focal_distance = 11;
  void clear_focal_distance();
  float focal_distance() const;
  void set_focal_distance(float value);
  private:
  float _internal_focal_distance() const;
  void _internal_set_focal_distance(float value);
  public:

  // float focal_scope = 12;
  void clear_focal_scope();
  float focal_scope() const;
  void set_focal_scope(float value);
  private:
  float _internal_focal_scope() const;
  void _internal_set_focal_scope(float value);
  public:

  // int32 ring = 13;
  void clear_ring();
  int32_t ring() const;
  void set_ring(int32_t value);
  private:
  int32_t _internal_ring() const;
  void _internal_set_ring(int32_t value);
  public:

  // float cos_rot = 14;
  void clear_cos_rot();
  float cos_rot() const;
  void set_cos_rot(float value);
  private:
  float _internal_cos_rot() const;
  void _internal_set_cos_rot(float value);
  public:

  // float sin_rot = 15;
  void clear_sin_rot();
  float sin_rot() const;
  void set_sin_rot(float value);
  private:
  float _internal_sin_rot() const;
  void _internal_set_sin_rot(float value);
  public:

  // float cos_vert = 16;
  void clear_cos_vert();
  float cos_vert() const;
  void set_cos_vert(float value);
  private:
  float _internal_cos_vert() const;
  void _internal_set_cos_vert(float value);
  public:

  // float sin_vert = 17;
  void clear_sin_vert();
  float sin_vert() const;
  void set_sin_vert(float value);
  private:
  float _internal_sin_vert() const;
  void _internal_set_sin_vert(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.LidarIntrisic)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    bool enabled_;
    int32_t min_intensity_;
    int32_t max_intensity_;
    float rot_;
    float vert_;
    float dist_;
    float dist_x_;
    float dist_y_;
    float vert_offset_;
    float horiz_offset_;
    float focal_distance_;
    float focal_scope_;
    int32_t ring_;
    float cos_rot_;
    float sin_rot_;
    float cos_vert_;
    float sin_vert_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_2eproto;
};
// -------------------------------------------------------------------

class Lidar final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Lidar) */ {
 public:
  inline Lidar() : Lidar(nullptr) {}
  ~Lidar() override;
  explicit PROTOBUF_CONSTEXPR Lidar(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Lidar(const Lidar& from);
  Lidar(Lidar&& from) noexcept
    : Lidar() {
    *this = ::std::move(from);
  }

  inline Lidar& operator=(const Lidar& from) {
    CopyFrom(from);
    return *this;
  }
  inline Lidar& operator=(Lidar&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Lidar& default_instance() {
    return *internal_default_instance();
  }
  static inline const Lidar* internal_default_instance() {
    return reinterpret_cast<const Lidar*>(
               &_Lidar_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Lidar& a, Lidar& b) {
    a.Swap(&b);
  }
  inline void Swap(Lidar* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Lidar* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Lidar* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Lidar>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Lidar& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Lidar& from) {
    Lidar::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Lidar* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Lidar";
  }
  protected:
  explicit Lidar(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Lidar_Type Type;
  static constexpr Type UNKNOWN =
    Lidar_Type_UNKNOWN;
  static constexpr Type VLP_16 =
    Lidar_Type_VLP_16;
  static constexpr Type VLP_32 =
    Lidar_Type_VLP_32;
  static constexpr Type HESAI_64 =
    Lidar_Type_HESAI_64;
  static inline bool Type_IsValid(int value) {
    return Lidar_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    Lidar_Type_Type_MIN;
  static constexpr Type Type_MAX =
    Lidar_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    Lidar_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return Lidar_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return Lidar_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return Lidar_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kIntrinsicsFieldNumber = 3,
    kNameFieldNumber = 1,
    kTopicFieldNumber = 7,
    kTfMapLidarFieldNumber = 6,
    kTypeFieldNumber = 2,
    kMinDistanceFieldNumber = 4,
    kMaxDistanceFieldNumber = 5,
    kUsageFieldNumber = 8,
  };
  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  int intrinsics_size() const;
  private:
  int _internal_intrinsics_size() const;
  public:
  void clear_intrinsics();
  ::perception::config::LidarIntrisic* mutable_intrinsics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >*
      mutable_intrinsics();
  private:
  const ::perception::config::LidarIntrisic& _internal_intrinsics(int index) const;
  ::perception::config::LidarIntrisic* _internal_add_intrinsics();
  public:
  const ::perception::config::LidarIntrisic& intrinsics(int index) const;
  ::perception::config::LidarIntrisic* add_intrinsics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >&
      intrinsics() const;

  // bytes name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes topic = 7;
  void clear_topic();
  const std::string& topic() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_topic(ArgT0&& arg0, ArgT... args);
  std::string* mutable_topic();
  PROTOBUF_NODISCARD std::string* release_topic();
  void set_allocated_topic(std::string* topic);
  private:
  const std::string& _internal_topic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_topic(const std::string& value);
  std::string* _internal_mutable_topic();
  public:

  // .perception.config.Transformation3d tf_map_lidar = 6;
  bool has_tf_map_lidar() const;
  private:
  bool _internal_has_tf_map_lidar() const;
  public:
  void clear_tf_map_lidar();
  const ::perception::config::Transformation3d& tf_map_lidar() const;
  PROTOBUF_NODISCARD ::perception::config::Transformation3d* release_tf_map_lidar();
  ::perception::config::Transformation3d* mutable_tf_map_lidar();
  void set_allocated_tf_map_lidar(::perception::config::Transformation3d* tf_map_lidar);
  private:
  const ::perception::config::Transformation3d& _internal_tf_map_lidar() const;
  ::perception::config::Transformation3d* _internal_mutable_tf_map_lidar();
  public:
  void unsafe_arena_set_allocated_tf_map_lidar(
      ::perception::config::Transformation3d* tf_map_lidar);
  ::perception::config::Transformation3d* unsafe_arena_release_tf_map_lidar();

  // .perception.config.Lidar.Type type = 2;
  void clear_type();
  ::perception::config::Lidar_Type type() const;
  void set_type(::perception::config::Lidar_Type value);
  private:
  ::perception::config::Lidar_Type _internal_type() const;
  void _internal_set_type(::perception::config::Lidar_Type value);
  public:

  // float min_distance = 4;
  void clear_min_distance();
  float min_distance() const;
  void set_min_distance(float value);
  private:
  float _internal_min_distance() const;
  void _internal_set_min_distance(float value);
  public:

  // float max_distance = 5;
  void clear_max_distance();
  float max_distance() const;
  void set_max_distance(float value);
  private:
  float _internal_max_distance() const;
  void _internal_set_max_distance(float value);
  public:

  // int32 usage = 8;
  void clear_usage();
  int32_t usage() const;
  void set_usage(int32_t value);
  private:
  int32_t _internal_usage() const;
  void _internal_set_usage(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.Lidar)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic > intrinsics_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_;
    ::perception::config::Transformation3d* tf_map_lidar_;
    int type_;
    float min_distance_;
    float max_distance_;
    int32_t usage_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_2eproto;
};
// -------------------------------------------------------------------

class Radar final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Radar) */ {
 public:
  inline Radar() : Radar(nullptr) {}
  ~Radar() override;
  explicit PROTOBUF_CONSTEXPR Radar(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Radar(const Radar& from);
  Radar(Radar&& from) noexcept
    : Radar() {
    *this = ::std::move(from);
  }

  inline Radar& operator=(const Radar& from) {
    CopyFrom(from);
    return *this;
  }
  inline Radar& operator=(Radar&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Radar& default_instance() {
    return *internal_default_instance();
  }
  static inline const Radar* internal_default_instance() {
    return reinterpret_cast<const Radar*>(
               &_Radar_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Radar& a, Radar& b) {
    a.Swap(&b);
  }
  inline void Swap(Radar* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Radar* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Radar* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Radar>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Radar& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Radar& from) {
    Radar::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Radar* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Radar";
  }
  protected:
  explicit Radar(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  typedef Radar_Type Type;
  static constexpr Type UNKNOWN =
    Radar_Type_UNKNOWN;
  static constexpr Type ARBE =
    Radar_Type_ARBE;
  static inline bool Type_IsValid(int value) {
    return Radar_Type_IsValid(value);
  }
  static constexpr Type Type_MIN =
    Radar_Type_Type_MIN;
  static constexpr Type Type_MAX =
    Radar_Type_Type_MAX;
  static constexpr int Type_ARRAYSIZE =
    Radar_Type_Type_ARRAYSIZE;
  static inline const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor*
  Type_descriptor() {
    return Radar_Type_descriptor();
  }
  template<typename T>
  static inline const std::string& Type_Name(T enum_t_value) {
    static_assert(::std::is_same<T, Type>::value ||
      ::std::is_integral<T>::value,
      "Incorrect type passed to function Type_Name.");
    return Radar_Type_Name(enum_t_value);
  }
  static inline bool Type_Parse(::PROTOBUF_NAMESPACE_ID::ConstStringParam name,
      Type* value) {
    return Radar_Type_Parse(name, value);
  }

  // accessors -------------------------------------------------------

  enum : int {
    kIntrinsicsFieldNumber = 3,
    kNameFieldNumber = 1,
    kTopicFieldNumber = 7,
    kTfMapRadarFieldNumber = 6,
    kTypeFieldNumber = 2,
    kMinDistanceFieldNumber = 4,
    kMaxDistanceFieldNumber = 5,
    kUsageFieldNumber = 8,
  };
  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  int intrinsics_size() const;
  private:
  int _internal_intrinsics_size() const;
  public:
  void clear_intrinsics();
  ::perception::config::LidarIntrisic* mutable_intrinsics(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >*
      mutable_intrinsics();
  private:
  const ::perception::config::LidarIntrisic& _internal_intrinsics(int index) const;
  ::perception::config::LidarIntrisic* _internal_add_intrinsics();
  public:
  const ::perception::config::LidarIntrisic& intrinsics(int index) const;
  ::perception::config::LidarIntrisic* add_intrinsics();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >&
      intrinsics() const;

  // bytes name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes topic = 7;
  void clear_topic();
  const std::string& topic() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_topic(ArgT0&& arg0, ArgT... args);
  std::string* mutable_topic();
  PROTOBUF_NODISCARD std::string* release_topic();
  void set_allocated_topic(std::string* topic);
  private:
  const std::string& _internal_topic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_topic(const std::string& value);
  std::string* _internal_mutable_topic();
  public:

  // .perception.config.Transformation3d tf_map_radar = 6;
  bool has_tf_map_radar() const;
  private:
  bool _internal_has_tf_map_radar() const;
  public:
  void clear_tf_map_radar();
  const ::perception::config::Transformation3d& tf_map_radar() const;
  PROTOBUF_NODISCARD ::perception::config::Transformation3d* release_tf_map_radar();
  ::perception::config::Transformation3d* mutable_tf_map_radar();
  void set_allocated_tf_map_radar(::perception::config::Transformation3d* tf_map_radar);
  private:
  const ::perception::config::Transformation3d& _internal_tf_map_radar() const;
  ::perception::config::Transformation3d* _internal_mutable_tf_map_radar();
  public:
  void unsafe_arena_set_allocated_tf_map_radar(
      ::perception::config::Transformation3d* tf_map_radar);
  ::perception::config::Transformation3d* unsafe_arena_release_tf_map_radar();

  // .perception.config.Radar.Type type = 2;
  void clear_type();
  ::perception::config::Radar_Type type() const;
  void set_type(::perception::config::Radar_Type value);
  private:
  ::perception::config::Radar_Type _internal_type() const;
  void _internal_set_type(::perception::config::Radar_Type value);
  public:

  // float min_distance = 4;
  void clear_min_distance();
  float min_distance() const;
  void set_min_distance(float value);
  private:
  float _internal_min_distance() const;
  void _internal_set_min_distance(float value);
  public:

  // float max_distance = 5;
  void clear_max_distance();
  float max_distance() const;
  void set_max_distance(float value);
  private:
  float _internal_max_distance() const;
  void _internal_set_max_distance(float value);
  public:

  // int32 usage = 8;
  void clear_usage();
  int32_t usage() const;
  void set_usage(int32_t value);
  private:
  int32_t _internal_usage() const;
  void _internal_set_usage(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.Radar)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic > intrinsics_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_;
    ::perception::config::Transformation3d* tf_map_radar_;
    int type_;
    float min_distance_;
    float max_distance_;
    int32_t usage_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_2eproto;
};
// -------------------------------------------------------------------

class Camera final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Camera) */ {
 public:
  inline Camera() : Camera(nullptr) {}
  ~Camera() override;
  explicit PROTOBUF_CONSTEXPR Camera(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Camera(const Camera& from);
  Camera(Camera&& from) noexcept
    : Camera() {
    *this = ::std::move(from);
  }

  inline Camera& operator=(const Camera& from) {
    CopyFrom(from);
    return *this;
  }
  inline Camera& operator=(Camera&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Camera& default_instance() {
    return *internal_default_instance();
  }
  static inline const Camera* internal_default_instance() {
    return reinterpret_cast<const Camera*>(
               &_Camera_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(Camera& a, Camera& b) {
    a.Swap(&b);
  }
  inline void Swap(Camera* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Camera* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Camera* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Camera>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Camera& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Camera& from) {
    Camera::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Camera* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Camera";
  }
  protected:
  explicit Camera(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRadialDistortionFieldNumber = 9,
    kNameFieldNumber = 1,
    kTopicFieldNumber = 10,
    kTfMapCameraFieldNumber = 4,
    kImageWidthFieldNumber = 2,
    kImageHeightFieldNumber = 3,
    kFxFieldNumber = 5,
    kFyFieldNumber = 6,
    kCxFieldNumber = 7,
    kCyFieldNumber = 8,
    kTimeSyncedFieldNumber = 11,
    kUsageFieldNumber = 12,
  };
  // repeated double radial_distortion = 9;
  int radial_distortion_size() const;
  private:
  int _internal_radial_distortion_size() const;
  public:
  void clear_radial_distortion();
  private:
  double _internal_radial_distortion(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      _internal_radial_distortion() const;
  void _internal_add_radial_distortion(double value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      _internal_mutable_radial_distortion();
  public:
  double radial_distortion(int index) const;
  void set_radial_distortion(int index, double value);
  void add_radial_distortion(double value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
      radial_distortion() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
      mutable_radial_distortion();

  // bytes name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // bytes topic = 10;
  void clear_topic();
  const std::string& topic() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_topic(ArgT0&& arg0, ArgT... args);
  std::string* mutable_topic();
  PROTOBUF_NODISCARD std::string* release_topic();
  void set_allocated_topic(std::string* topic);
  private:
  const std::string& _internal_topic() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_topic(const std::string& value);
  std::string* _internal_mutable_topic();
  public:

  // .perception.config.Transformation3d tf_map_camera = 4;
  bool has_tf_map_camera() const;
  private:
  bool _internal_has_tf_map_camera() const;
  public:
  void clear_tf_map_camera();
  const ::perception::config::Transformation3d& tf_map_camera() const;
  PROTOBUF_NODISCARD ::perception::config::Transformation3d* release_tf_map_camera();
  ::perception::config::Transformation3d* mutable_tf_map_camera();
  void set_allocated_tf_map_camera(::perception::config::Transformation3d* tf_map_camera);
  private:
  const ::perception::config::Transformation3d& _internal_tf_map_camera() const;
  ::perception::config::Transformation3d* _internal_mutable_tf_map_camera();
  public:
  void unsafe_arena_set_allocated_tf_map_camera(
      ::perception::config::Transformation3d* tf_map_camera);
  ::perception::config::Transformation3d* unsafe_arena_release_tf_map_camera();

  // int32 image_width = 2;
  void clear_image_width();
  int32_t image_width() const;
  void set_image_width(int32_t value);
  private:
  int32_t _internal_image_width() const;
  void _internal_set_image_width(int32_t value);
  public:

  // int32 image_height = 3;
  void clear_image_height();
  int32_t image_height() const;
  void set_image_height(int32_t value);
  private:
  int32_t _internal_image_height() const;
  void _internal_set_image_height(int32_t value);
  public:

  // double fx = 5;
  void clear_fx();
  double fx() const;
  void set_fx(double value);
  private:
  double _internal_fx() const;
  void _internal_set_fx(double value);
  public:

  // double fy = 6;
  void clear_fy();
  double fy() const;
  void set_fy(double value);
  private:
  double _internal_fy() const;
  void _internal_set_fy(double value);
  public:

  // double cx = 7;
  void clear_cx();
  double cx() const;
  void set_cx(double value);
  private:
  double _internal_cx() const;
  void _internal_set_cx(double value);
  public:

  // double cy = 8;
  void clear_cy();
  double cy() const;
  void set_cy(double value);
  private:
  double _internal_cy() const;
  void _internal_set_cy(double value);
  public:

  // bool time_synced = 11;
  void clear_time_synced();
  bool time_synced() const;
  void set_time_synced(bool value);
  private:
  bool _internal_time_synced() const;
  void _internal_set_time_synced(bool value);
  public:

  // int32 usage = 12;
  void clear_usage();
  int32_t usage() const;
  void set_usage(int32_t value);
  private:
  int32_t _internal_usage() const;
  void _internal_set_usage(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.Camera)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< double > radial_distortion_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr topic_;
    ::perception::config::Transformation3d* tf_map_camera_;
    int32_t image_width_;
    int32_t image_height_;
    double fx_;
    double fy_;
    double cx_;
    double cy_;
    bool time_synced_;
    int32_t usage_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_2eproto;
};
// -------------------------------------------------------------------

class ConfigGroup final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.ConfigGroup) */ {
 public:
  inline ConfigGroup() : ConfigGroup(nullptr) {}
  ~ConfigGroup() override;
  explicit PROTOBUF_CONSTEXPR ConfigGroup(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigGroup(const ConfigGroup& from);
  ConfigGroup(ConfigGroup&& from) noexcept
    : ConfigGroup() {
    *this = ::std::move(from);
  }

  inline ConfigGroup& operator=(const ConfigGroup& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigGroup& operator=(ConfigGroup&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigGroup& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigGroup* internal_default_instance() {
    return reinterpret_cast<const ConfigGroup*>(
               &_ConfigGroup_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ConfigGroup& a, ConfigGroup& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigGroup* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigGroup* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigGroup* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigGroup>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigGroup& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const ConfigGroup& from) {
    ConfigGroup::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigGroup* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.ConfigGroup";
  }
  protected:
  explicit ConfigGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRadarsFieldNumber = 4,
    kCamerasFieldNumber = 5,
    kGroupNameFieldNumber = 1,
    kTfMapGroupFieldNumber = 3,
    kGroupTypeFieldNumber = 2,
  };
  // repeated .perception.config.Radar radars = 4;
  int radars_size() const;
  private:
  int _internal_radars_size() const;
  public:
  void clear_radars();
  ::perception::config::Radar* mutable_radars(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Radar >*
      mutable_radars();
  private:
  const ::perception::config::Radar& _internal_radars(int index) const;
  ::perception::config::Radar* _internal_add_radars();
  public:
  const ::perception::config::Radar& radars(int index) const;
  ::perception::config::Radar* add_radars();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Radar >&
      radars() const;

  // repeated .perception.config.Camera cameras = 5;
  int cameras_size() const;
  private:
  int _internal_cameras_size() const;
  public:
  void clear_cameras();
  ::perception::config::Camera* mutable_cameras(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Camera >*
      mutable_cameras();
  private:
  const ::perception::config::Camera& _internal_cameras(int index) const;
  ::perception::config::Camera* _internal_add_cameras();
  public:
  const ::perception::config::Camera& cameras(int index) const;
  ::perception::config::Camera* add_cameras();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Camera >&
      cameras() const;

  // bytes group_name = 1;
  void clear_group_name();
  const std::string& group_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_name();
  PROTOBUF_NODISCARD std::string* release_group_name();
  void set_allocated_group_name(std::string* group_name);
  private:
  const std::string& _internal_group_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_name(const std::string& value);
  std::string* _internal_mutable_group_name();
  public:

  // .perception.config.Transformation3d tf_map_group = 3;
  bool has_tf_map_group() const;
  private:
  bool _internal_has_tf_map_group() const;
  public:
  void clear_tf_map_group();
  const ::perception::config::Transformation3d& tf_map_group() const;
  PROTOBUF_NODISCARD ::perception::config::Transformation3d* release_tf_map_group();
  ::perception::config::Transformation3d* mutable_tf_map_group();
  void set_allocated_tf_map_group(::perception::config::Transformation3d* tf_map_group);
  private:
  const ::perception::config::Transformation3d& _internal_tf_map_group() const;
  ::perception::config::Transformation3d* _internal_mutable_tf_map_group();
  public:
  void unsafe_arena_set_allocated_tf_map_group(
      ::perception::config::Transformation3d* tf_map_group);
  ::perception::config::Transformation3d* unsafe_arena_release_tf_map_group();

  // int32 group_type = 2;
  void clear_group_type();
  int32_t group_type() const;
  void set_group_type(int32_t value);
  private:
  int32_t _internal_group_type() const;
  void _internal_set_group_type(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.ConfigGroup)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Radar > radars_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Camera > cameras_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_name_;
    ::perception::config::Transformation3d* tf_map_group_;
    int32_t group_type_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LidarIntrisic

// bool enabled = 1;
inline void LidarIntrisic::clear_enabled() {
  _impl_.enabled_ = false;
}
inline bool LidarIntrisic::_internal_enabled() const {
  return _impl_.enabled_;
}
inline bool LidarIntrisic::enabled() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.enabled)
  return _internal_enabled();
}
inline void LidarIntrisic::_internal_set_enabled(bool value) {
  
  _impl_.enabled_ = value;
}
inline void LidarIntrisic::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.enabled)
}

// int32 min_intensity = 2;
inline void LidarIntrisic::clear_min_intensity() {
  _impl_.min_intensity_ = 0;
}
inline int32_t LidarIntrisic::_internal_min_intensity() const {
  return _impl_.min_intensity_;
}
inline int32_t LidarIntrisic::min_intensity() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.min_intensity)
  return _internal_min_intensity();
}
inline void LidarIntrisic::_internal_set_min_intensity(int32_t value) {
  
  _impl_.min_intensity_ = value;
}
inline void LidarIntrisic::set_min_intensity(int32_t value) {
  _internal_set_min_intensity(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.min_intensity)
}

// int32 max_intensity = 3;
inline void LidarIntrisic::clear_max_intensity() {
  _impl_.max_intensity_ = 0;
}
inline int32_t LidarIntrisic::_internal_max_intensity() const {
  return _impl_.max_intensity_;
}
inline int32_t LidarIntrisic::max_intensity() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.max_intensity)
  return _internal_max_intensity();
}
inline void LidarIntrisic::_internal_set_max_intensity(int32_t value) {
  
  _impl_.max_intensity_ = value;
}
inline void LidarIntrisic::set_max_intensity(int32_t value) {
  _internal_set_max_intensity(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.max_intensity)
}

// float rot = 4;
inline void LidarIntrisic::clear_rot() {
  _impl_.rot_ = 0;
}
inline float LidarIntrisic::_internal_rot() const {
  return _impl_.rot_;
}
inline float LidarIntrisic::rot() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.rot)
  return _internal_rot();
}
inline void LidarIntrisic::_internal_set_rot(float value) {
  
  _impl_.rot_ = value;
}
inline void LidarIntrisic::set_rot(float value) {
  _internal_set_rot(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.rot)
}

// float vert = 5;
inline void LidarIntrisic::clear_vert() {
  _impl_.vert_ = 0;
}
inline float LidarIntrisic::_internal_vert() const {
  return _impl_.vert_;
}
inline float LidarIntrisic::vert() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.vert)
  return _internal_vert();
}
inline void LidarIntrisic::_internal_set_vert(float value) {
  
  _impl_.vert_ = value;
}
inline void LidarIntrisic::set_vert(float value) {
  _internal_set_vert(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.vert)
}

// float dist = 6;
inline void LidarIntrisic::clear_dist() {
  _impl_.dist_ = 0;
}
inline float LidarIntrisic::_internal_dist() const {
  return _impl_.dist_;
}
inline float LidarIntrisic::dist() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.dist)
  return _internal_dist();
}
inline void LidarIntrisic::_internal_set_dist(float value) {
  
  _impl_.dist_ = value;
}
inline void LidarIntrisic::set_dist(float value) {
  _internal_set_dist(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.dist)
}

// float dist_x = 7;
inline void LidarIntrisic::clear_dist_x() {
  _impl_.dist_x_ = 0;
}
inline float LidarIntrisic::_internal_dist_x() const {
  return _impl_.dist_x_;
}
inline float LidarIntrisic::dist_x() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.dist_x)
  return _internal_dist_x();
}
inline void LidarIntrisic::_internal_set_dist_x(float value) {
  
  _impl_.dist_x_ = value;
}
inline void LidarIntrisic::set_dist_x(float value) {
  _internal_set_dist_x(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.dist_x)
}

// float dist_y = 8;
inline void LidarIntrisic::clear_dist_y() {
  _impl_.dist_y_ = 0;
}
inline float LidarIntrisic::_internal_dist_y() const {
  return _impl_.dist_y_;
}
inline float LidarIntrisic::dist_y() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.dist_y)
  return _internal_dist_y();
}
inline void LidarIntrisic::_internal_set_dist_y(float value) {
  
  _impl_.dist_y_ = value;
}
inline void LidarIntrisic::set_dist_y(float value) {
  _internal_set_dist_y(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.dist_y)
}

// float vert_offset = 9;
inline void LidarIntrisic::clear_vert_offset() {
  _impl_.vert_offset_ = 0;
}
inline float LidarIntrisic::_internal_vert_offset() const {
  return _impl_.vert_offset_;
}
inline float LidarIntrisic::vert_offset() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.vert_offset)
  return _internal_vert_offset();
}
inline void LidarIntrisic::_internal_set_vert_offset(float value) {
  
  _impl_.vert_offset_ = value;
}
inline void LidarIntrisic::set_vert_offset(float value) {
  _internal_set_vert_offset(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.vert_offset)
}

// float horiz_offset = 10;
inline void LidarIntrisic::clear_horiz_offset() {
  _impl_.horiz_offset_ = 0;
}
inline float LidarIntrisic::_internal_horiz_offset() const {
  return _impl_.horiz_offset_;
}
inline float LidarIntrisic::horiz_offset() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.horiz_offset)
  return _internal_horiz_offset();
}
inline void LidarIntrisic::_internal_set_horiz_offset(float value) {
  
  _impl_.horiz_offset_ = value;
}
inline void LidarIntrisic::set_horiz_offset(float value) {
  _internal_set_horiz_offset(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.horiz_offset)
}

// float focal_distance = 11;
inline void LidarIntrisic::clear_focal_distance() {
  _impl_.focal_distance_ = 0;
}
inline float LidarIntrisic::_internal_focal_distance() const {
  return _impl_.focal_distance_;
}
inline float LidarIntrisic::focal_distance() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.focal_distance)
  return _internal_focal_distance();
}
inline void LidarIntrisic::_internal_set_focal_distance(float value) {
  
  _impl_.focal_distance_ = value;
}
inline void LidarIntrisic::set_focal_distance(float value) {
  _internal_set_focal_distance(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.focal_distance)
}

// float focal_scope = 12;
inline void LidarIntrisic::clear_focal_scope() {
  _impl_.focal_scope_ = 0;
}
inline float LidarIntrisic::_internal_focal_scope() const {
  return _impl_.focal_scope_;
}
inline float LidarIntrisic::focal_scope() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.focal_scope)
  return _internal_focal_scope();
}
inline void LidarIntrisic::_internal_set_focal_scope(float value) {
  
  _impl_.focal_scope_ = value;
}
inline void LidarIntrisic::set_focal_scope(float value) {
  _internal_set_focal_scope(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.focal_scope)
}

// int32 ring = 13;
inline void LidarIntrisic::clear_ring() {
  _impl_.ring_ = 0;
}
inline int32_t LidarIntrisic::_internal_ring() const {
  return _impl_.ring_;
}
inline int32_t LidarIntrisic::ring() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.ring)
  return _internal_ring();
}
inline void LidarIntrisic::_internal_set_ring(int32_t value) {
  
  _impl_.ring_ = value;
}
inline void LidarIntrisic::set_ring(int32_t value) {
  _internal_set_ring(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.ring)
}

// float cos_rot = 14;
inline void LidarIntrisic::clear_cos_rot() {
  _impl_.cos_rot_ = 0;
}
inline float LidarIntrisic::_internal_cos_rot() const {
  return _impl_.cos_rot_;
}
inline float LidarIntrisic::cos_rot() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.cos_rot)
  return _internal_cos_rot();
}
inline void LidarIntrisic::_internal_set_cos_rot(float value) {
  
  _impl_.cos_rot_ = value;
}
inline void LidarIntrisic::set_cos_rot(float value) {
  _internal_set_cos_rot(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.cos_rot)
}

// float sin_rot = 15;
inline void LidarIntrisic::clear_sin_rot() {
  _impl_.sin_rot_ = 0;
}
inline float LidarIntrisic::_internal_sin_rot() const {
  return _impl_.sin_rot_;
}
inline float LidarIntrisic::sin_rot() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.sin_rot)
  return _internal_sin_rot();
}
inline void LidarIntrisic::_internal_set_sin_rot(float value) {
  
  _impl_.sin_rot_ = value;
}
inline void LidarIntrisic::set_sin_rot(float value) {
  _internal_set_sin_rot(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.sin_rot)
}

// float cos_vert = 16;
inline void LidarIntrisic::clear_cos_vert() {
  _impl_.cos_vert_ = 0;
}
inline float LidarIntrisic::_internal_cos_vert() const {
  return _impl_.cos_vert_;
}
inline float LidarIntrisic::cos_vert() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.cos_vert)
  return _internal_cos_vert();
}
inline void LidarIntrisic::_internal_set_cos_vert(float value) {
  
  _impl_.cos_vert_ = value;
}
inline void LidarIntrisic::set_cos_vert(float value) {
  _internal_set_cos_vert(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.cos_vert)
}

// float sin_vert = 17;
inline void LidarIntrisic::clear_sin_vert() {
  _impl_.sin_vert_ = 0;
}
inline float LidarIntrisic::_internal_sin_vert() const {
  return _impl_.sin_vert_;
}
inline float LidarIntrisic::sin_vert() const {
  // @@protoc_insertion_point(field_get:perception.config.LidarIntrisic.sin_vert)
  return _internal_sin_vert();
}
inline void LidarIntrisic::_internal_set_sin_vert(float value) {
  
  _impl_.sin_vert_ = value;
}
inline void LidarIntrisic::set_sin_vert(float value) {
  _internal_set_sin_vert(value);
  // @@protoc_insertion_point(field_set:perception.config.LidarIntrisic.sin_vert)
}

// -------------------------------------------------------------------

// Lidar

// bytes name = 1;
inline void Lidar::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Lidar::name() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Lidar::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Lidar.name)
}
inline std::string* Lidar::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:perception.config.Lidar.name)
  return _s;
}
inline const std::string& Lidar::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Lidar::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Lidar::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Lidar::release_name() {
  // @@protoc_insertion_point(field_release:perception.config.Lidar.name)
  return _impl_.name_.Release();
}
inline void Lidar::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Lidar.name)
}

// .perception.config.Lidar.Type type = 2;
inline void Lidar::clear_type() {
  _impl_.type_ = 0;
}
inline ::perception::config::Lidar_Type Lidar::_internal_type() const {
  return static_cast< ::perception::config::Lidar_Type >(_impl_.type_);
}
inline ::perception::config::Lidar_Type Lidar::type() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.type)
  return _internal_type();
}
inline void Lidar::_internal_set_type(::perception::config::Lidar_Type value) {
  
  _impl_.type_ = value;
}
inline void Lidar::set_type(::perception::config::Lidar_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.config.Lidar.type)
}

// repeated .perception.config.LidarIntrisic intrinsics = 3;
inline int Lidar::_internal_intrinsics_size() const {
  return _impl_.intrinsics_.size();
}
inline int Lidar::intrinsics_size() const {
  return _internal_intrinsics_size();
}
inline void Lidar::clear_intrinsics() {
  _impl_.intrinsics_.Clear();
}
inline ::perception::config::LidarIntrisic* Lidar::mutable_intrinsics(int index) {
  // @@protoc_insertion_point(field_mutable:perception.config.Lidar.intrinsics)
  return _impl_.intrinsics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >*
Lidar::mutable_intrinsics() {
  // @@protoc_insertion_point(field_mutable_list:perception.config.Lidar.intrinsics)
  return &_impl_.intrinsics_;
}
inline const ::perception::config::LidarIntrisic& Lidar::_internal_intrinsics(int index) const {
  return _impl_.intrinsics_.Get(index);
}
inline const ::perception::config::LidarIntrisic& Lidar::intrinsics(int index) const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.intrinsics)
  return _internal_intrinsics(index);
}
inline ::perception::config::LidarIntrisic* Lidar::_internal_add_intrinsics() {
  return _impl_.intrinsics_.Add();
}
inline ::perception::config::LidarIntrisic* Lidar::add_intrinsics() {
  ::perception::config::LidarIntrisic* _add = _internal_add_intrinsics();
  // @@protoc_insertion_point(field_add:perception.config.Lidar.intrinsics)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >&
Lidar::intrinsics() const {
  // @@protoc_insertion_point(field_list:perception.config.Lidar.intrinsics)
  return _impl_.intrinsics_;
}

// float min_distance = 4;
inline void Lidar::clear_min_distance() {
  _impl_.min_distance_ = 0;
}
inline float Lidar::_internal_min_distance() const {
  return _impl_.min_distance_;
}
inline float Lidar::min_distance() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.min_distance)
  return _internal_min_distance();
}
inline void Lidar::_internal_set_min_distance(float value) {
  
  _impl_.min_distance_ = value;
}
inline void Lidar::set_min_distance(float value) {
  _internal_set_min_distance(value);
  // @@protoc_insertion_point(field_set:perception.config.Lidar.min_distance)
}

// float max_distance = 5;
inline void Lidar::clear_max_distance() {
  _impl_.max_distance_ = 0;
}
inline float Lidar::_internal_max_distance() const {
  return _impl_.max_distance_;
}
inline float Lidar::max_distance() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.max_distance)
  return _internal_max_distance();
}
inline void Lidar::_internal_set_max_distance(float value) {
  
  _impl_.max_distance_ = value;
}
inline void Lidar::set_max_distance(float value) {
  _internal_set_max_distance(value);
  // @@protoc_insertion_point(field_set:perception.config.Lidar.max_distance)
}

// .perception.config.Transformation3d tf_map_lidar = 6;
inline bool Lidar::_internal_has_tf_map_lidar() const {
  return this != internal_default_instance() && _impl_.tf_map_lidar_ != nullptr;
}
inline bool Lidar::has_tf_map_lidar() const {
  return _internal_has_tf_map_lidar();
}
inline const ::perception::config::Transformation3d& Lidar::_internal_tf_map_lidar() const {
  const ::perception::config::Transformation3d* p = _impl_.tf_map_lidar_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Transformation3d&>(
      ::perception::config::_Transformation3d_default_instance_);
}
inline const ::perception::config::Transformation3d& Lidar::tf_map_lidar() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.tf_map_lidar)
  return _internal_tf_map_lidar();
}
inline void Lidar::unsafe_arena_set_allocated_tf_map_lidar(
    ::perception::config::Transformation3d* tf_map_lidar) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_lidar_);
  }
  _impl_.tf_map_lidar_ = tf_map_lidar;
  if (tf_map_lidar) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.Lidar.tf_map_lidar)
}
inline ::perception::config::Transformation3d* Lidar::release_tf_map_lidar() {
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_lidar_;
  _impl_.tf_map_lidar_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Transformation3d* Lidar::unsafe_arena_release_tf_map_lidar() {
  // @@protoc_insertion_point(field_release:perception.config.Lidar.tf_map_lidar)
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_lidar_;
  _impl_.tf_map_lidar_ = nullptr;
  return temp;
}
inline ::perception::config::Transformation3d* Lidar::_internal_mutable_tf_map_lidar() {
  
  if (_impl_.tf_map_lidar_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Transformation3d>(GetArenaForAllocation());
    _impl_.tf_map_lidar_ = p;
  }
  return _impl_.tf_map_lidar_;
}
inline ::perception::config::Transformation3d* Lidar::mutable_tf_map_lidar() {
  ::perception::config::Transformation3d* _msg = _internal_mutable_tf_map_lidar();
  // @@protoc_insertion_point(field_mutable:perception.config.Lidar.tf_map_lidar)
  return _msg;
}
inline void Lidar::set_allocated_tf_map_lidar(::perception::config::Transformation3d* tf_map_lidar) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_lidar_);
  }
  if (tf_map_lidar) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tf_map_lidar));
    if (message_arena != submessage_arena) {
      tf_map_lidar = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tf_map_lidar, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tf_map_lidar_ = tf_map_lidar;
  // @@protoc_insertion_point(field_set_allocated:perception.config.Lidar.tf_map_lidar)
}

// bytes topic = 7;
inline void Lidar::clear_topic() {
  _impl_.topic_.ClearToEmpty();
}
inline const std::string& Lidar::topic() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.topic)
  return _internal_topic();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Lidar::set_topic(ArgT0&& arg0, ArgT... args) {
 
 _impl_.topic_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Lidar.topic)
}
inline std::string* Lidar::mutable_topic() {
  std::string* _s = _internal_mutable_topic();
  // @@protoc_insertion_point(field_mutable:perception.config.Lidar.topic)
  return _s;
}
inline const std::string& Lidar::_internal_topic() const {
  return _impl_.topic_.Get();
}
inline void Lidar::_internal_set_topic(const std::string& value) {
  
  _impl_.topic_.Set(value, GetArenaForAllocation());
}
inline std::string* Lidar::_internal_mutable_topic() {
  
  return _impl_.topic_.Mutable(GetArenaForAllocation());
}
inline std::string* Lidar::release_topic() {
  // @@protoc_insertion_point(field_release:perception.config.Lidar.topic)
  return _impl_.topic_.Release();
}
inline void Lidar::set_allocated_topic(std::string* topic) {
  if (topic != nullptr) {
    
  } else {
    
  }
  _impl_.topic_.SetAllocated(topic, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.topic_.IsDefault()) {
    _impl_.topic_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Lidar.topic)
}

// int32 usage = 8;
inline void Lidar::clear_usage() {
  _impl_.usage_ = 0;
}
inline int32_t Lidar::_internal_usage() const {
  return _impl_.usage_;
}
inline int32_t Lidar::usage() const {
  // @@protoc_insertion_point(field_get:perception.config.Lidar.usage)
  return _internal_usage();
}
inline void Lidar::_internal_set_usage(int32_t value) {
  
  _impl_.usage_ = value;
}
inline void Lidar::set_usage(int32_t value) {
  _internal_set_usage(value);
  // @@protoc_insertion_point(field_set:perception.config.Lidar.usage)
}

// -------------------------------------------------------------------

// Radar

// bytes name = 1;
inline void Radar::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Radar::name() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Radar::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Radar.name)
}
inline std::string* Radar::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:perception.config.Radar.name)
  return _s;
}
inline const std::string& Radar::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Radar::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Radar::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Radar::release_name() {
  // @@protoc_insertion_point(field_release:perception.config.Radar.name)
  return _impl_.name_.Release();
}
inline void Radar::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Radar.name)
}

// .perception.config.Radar.Type type = 2;
inline void Radar::clear_type() {
  _impl_.type_ = 0;
}
inline ::perception::config::Radar_Type Radar::_internal_type() const {
  return static_cast< ::perception::config::Radar_Type >(_impl_.type_);
}
inline ::perception::config::Radar_Type Radar::type() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.type)
  return _internal_type();
}
inline void Radar::_internal_set_type(::perception::config::Radar_Type value) {
  
  _impl_.type_ = value;
}
inline void Radar::set_type(::perception::config::Radar_Type value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.config.Radar.type)
}

// repeated .perception.config.LidarIntrisic intrinsics = 3;
inline int Radar::_internal_intrinsics_size() const {
  return _impl_.intrinsics_.size();
}
inline int Radar::intrinsics_size() const {
  return _internal_intrinsics_size();
}
inline void Radar::clear_intrinsics() {
  _impl_.intrinsics_.Clear();
}
inline ::perception::config::LidarIntrisic* Radar::mutable_intrinsics(int index) {
  // @@protoc_insertion_point(field_mutable:perception.config.Radar.intrinsics)
  return _impl_.intrinsics_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >*
Radar::mutable_intrinsics() {
  // @@protoc_insertion_point(field_mutable_list:perception.config.Radar.intrinsics)
  return &_impl_.intrinsics_;
}
inline const ::perception::config::LidarIntrisic& Radar::_internal_intrinsics(int index) const {
  return _impl_.intrinsics_.Get(index);
}
inline const ::perception::config::LidarIntrisic& Radar::intrinsics(int index) const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.intrinsics)
  return _internal_intrinsics(index);
}
inline ::perception::config::LidarIntrisic* Radar::_internal_add_intrinsics() {
  return _impl_.intrinsics_.Add();
}
inline ::perception::config::LidarIntrisic* Radar::add_intrinsics() {
  ::perception::config::LidarIntrisic* _add = _internal_add_intrinsics();
  // @@protoc_insertion_point(field_add:perception.config.Radar.intrinsics)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::LidarIntrisic >&
Radar::intrinsics() const {
  // @@protoc_insertion_point(field_list:perception.config.Radar.intrinsics)
  return _impl_.intrinsics_;
}

// float min_distance = 4;
inline void Radar::clear_min_distance() {
  _impl_.min_distance_ = 0;
}
inline float Radar::_internal_min_distance() const {
  return _impl_.min_distance_;
}
inline float Radar::min_distance() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.min_distance)
  return _internal_min_distance();
}
inline void Radar::_internal_set_min_distance(float value) {
  
  _impl_.min_distance_ = value;
}
inline void Radar::set_min_distance(float value) {
  _internal_set_min_distance(value);
  // @@protoc_insertion_point(field_set:perception.config.Radar.min_distance)
}

// float max_distance = 5;
inline void Radar::clear_max_distance() {
  _impl_.max_distance_ = 0;
}
inline float Radar::_internal_max_distance() const {
  return _impl_.max_distance_;
}
inline float Radar::max_distance() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.max_distance)
  return _internal_max_distance();
}
inline void Radar::_internal_set_max_distance(float value) {
  
  _impl_.max_distance_ = value;
}
inline void Radar::set_max_distance(float value) {
  _internal_set_max_distance(value);
  // @@protoc_insertion_point(field_set:perception.config.Radar.max_distance)
}

// .perception.config.Transformation3d tf_map_radar = 6;
inline bool Radar::_internal_has_tf_map_radar() const {
  return this != internal_default_instance() && _impl_.tf_map_radar_ != nullptr;
}
inline bool Radar::has_tf_map_radar() const {
  return _internal_has_tf_map_radar();
}
inline const ::perception::config::Transformation3d& Radar::_internal_tf_map_radar() const {
  const ::perception::config::Transformation3d* p = _impl_.tf_map_radar_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Transformation3d&>(
      ::perception::config::_Transformation3d_default_instance_);
}
inline const ::perception::config::Transformation3d& Radar::tf_map_radar() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.tf_map_radar)
  return _internal_tf_map_radar();
}
inline void Radar::unsafe_arena_set_allocated_tf_map_radar(
    ::perception::config::Transformation3d* tf_map_radar) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_radar_);
  }
  _impl_.tf_map_radar_ = tf_map_radar;
  if (tf_map_radar) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.Radar.tf_map_radar)
}
inline ::perception::config::Transformation3d* Radar::release_tf_map_radar() {
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_radar_;
  _impl_.tf_map_radar_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Transformation3d* Radar::unsafe_arena_release_tf_map_radar() {
  // @@protoc_insertion_point(field_release:perception.config.Radar.tf_map_radar)
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_radar_;
  _impl_.tf_map_radar_ = nullptr;
  return temp;
}
inline ::perception::config::Transformation3d* Radar::_internal_mutable_tf_map_radar() {
  
  if (_impl_.tf_map_radar_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Transformation3d>(GetArenaForAllocation());
    _impl_.tf_map_radar_ = p;
  }
  return _impl_.tf_map_radar_;
}
inline ::perception::config::Transformation3d* Radar::mutable_tf_map_radar() {
  ::perception::config::Transformation3d* _msg = _internal_mutable_tf_map_radar();
  // @@protoc_insertion_point(field_mutable:perception.config.Radar.tf_map_radar)
  return _msg;
}
inline void Radar::set_allocated_tf_map_radar(::perception::config::Transformation3d* tf_map_radar) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_radar_);
  }
  if (tf_map_radar) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tf_map_radar));
    if (message_arena != submessage_arena) {
      tf_map_radar = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tf_map_radar, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tf_map_radar_ = tf_map_radar;
  // @@protoc_insertion_point(field_set_allocated:perception.config.Radar.tf_map_radar)
}

// bytes topic = 7;
inline void Radar::clear_topic() {
  _impl_.topic_.ClearToEmpty();
}
inline const std::string& Radar::topic() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.topic)
  return _internal_topic();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Radar::set_topic(ArgT0&& arg0, ArgT... args) {
 
 _impl_.topic_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Radar.topic)
}
inline std::string* Radar::mutable_topic() {
  std::string* _s = _internal_mutable_topic();
  // @@protoc_insertion_point(field_mutable:perception.config.Radar.topic)
  return _s;
}
inline const std::string& Radar::_internal_topic() const {
  return _impl_.topic_.Get();
}
inline void Radar::_internal_set_topic(const std::string& value) {
  
  _impl_.topic_.Set(value, GetArenaForAllocation());
}
inline std::string* Radar::_internal_mutable_topic() {
  
  return _impl_.topic_.Mutable(GetArenaForAllocation());
}
inline std::string* Radar::release_topic() {
  // @@protoc_insertion_point(field_release:perception.config.Radar.topic)
  return _impl_.topic_.Release();
}
inline void Radar::set_allocated_topic(std::string* topic) {
  if (topic != nullptr) {
    
  } else {
    
  }
  _impl_.topic_.SetAllocated(topic, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.topic_.IsDefault()) {
    _impl_.topic_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Radar.topic)
}

// int32 usage = 8;
inline void Radar::clear_usage() {
  _impl_.usage_ = 0;
}
inline int32_t Radar::_internal_usage() const {
  return _impl_.usage_;
}
inline int32_t Radar::usage() const {
  // @@protoc_insertion_point(field_get:perception.config.Radar.usage)
  return _internal_usage();
}
inline void Radar::_internal_set_usage(int32_t value) {
  
  _impl_.usage_ = value;
}
inline void Radar::set_usage(int32_t value) {
  _internal_set_usage(value);
  // @@protoc_insertion_point(field_set:perception.config.Radar.usage)
}

// -------------------------------------------------------------------

// Camera

// bytes name = 1;
inline void Camera::clear_name() {
  _impl_.name_.ClearToEmpty();
}
inline const std::string& Camera::name() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Camera::set_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Camera.name)
}
inline std::string* Camera::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:perception.config.Camera.name)
  return _s;
}
inline const std::string& Camera::_internal_name() const {
  return _impl_.name_.Get();
}
inline void Camera::_internal_set_name(const std::string& value) {
  
  _impl_.name_.Set(value, GetArenaForAllocation());
}
inline std::string* Camera::_internal_mutable_name() {
  
  return _impl_.name_.Mutable(GetArenaForAllocation());
}
inline std::string* Camera::release_name() {
  // @@protoc_insertion_point(field_release:perception.config.Camera.name)
  return _impl_.name_.Release();
}
inline void Camera::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  _impl_.name_.SetAllocated(name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.name_.IsDefault()) {
    _impl_.name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Camera.name)
}

// int32 image_width = 2;
inline void Camera::clear_image_width() {
  _impl_.image_width_ = 0;
}
inline int32_t Camera::_internal_image_width() const {
  return _impl_.image_width_;
}
inline int32_t Camera::image_width() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.image_width)
  return _internal_image_width();
}
inline void Camera::_internal_set_image_width(int32_t value) {
  
  _impl_.image_width_ = value;
}
inline void Camera::set_image_width(int32_t value) {
  _internal_set_image_width(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.image_width)
}

// int32 image_height = 3;
inline void Camera::clear_image_height() {
  _impl_.image_height_ = 0;
}
inline int32_t Camera::_internal_image_height() const {
  return _impl_.image_height_;
}
inline int32_t Camera::image_height() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.image_height)
  return _internal_image_height();
}
inline void Camera::_internal_set_image_height(int32_t value) {
  
  _impl_.image_height_ = value;
}
inline void Camera::set_image_height(int32_t value) {
  _internal_set_image_height(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.image_height)
}

// .perception.config.Transformation3d tf_map_camera = 4;
inline bool Camera::_internal_has_tf_map_camera() const {
  return this != internal_default_instance() && _impl_.tf_map_camera_ != nullptr;
}
inline bool Camera::has_tf_map_camera() const {
  return _internal_has_tf_map_camera();
}
inline const ::perception::config::Transformation3d& Camera::_internal_tf_map_camera() const {
  const ::perception::config::Transformation3d* p = _impl_.tf_map_camera_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Transformation3d&>(
      ::perception::config::_Transformation3d_default_instance_);
}
inline const ::perception::config::Transformation3d& Camera::tf_map_camera() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.tf_map_camera)
  return _internal_tf_map_camera();
}
inline void Camera::unsafe_arena_set_allocated_tf_map_camera(
    ::perception::config::Transformation3d* tf_map_camera) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_camera_);
  }
  _impl_.tf_map_camera_ = tf_map_camera;
  if (tf_map_camera) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.Camera.tf_map_camera)
}
inline ::perception::config::Transformation3d* Camera::release_tf_map_camera() {
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_camera_;
  _impl_.tf_map_camera_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Transformation3d* Camera::unsafe_arena_release_tf_map_camera() {
  // @@protoc_insertion_point(field_release:perception.config.Camera.tf_map_camera)
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_camera_;
  _impl_.tf_map_camera_ = nullptr;
  return temp;
}
inline ::perception::config::Transformation3d* Camera::_internal_mutable_tf_map_camera() {
  
  if (_impl_.tf_map_camera_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Transformation3d>(GetArenaForAllocation());
    _impl_.tf_map_camera_ = p;
  }
  return _impl_.tf_map_camera_;
}
inline ::perception::config::Transformation3d* Camera::mutable_tf_map_camera() {
  ::perception::config::Transformation3d* _msg = _internal_mutable_tf_map_camera();
  // @@protoc_insertion_point(field_mutable:perception.config.Camera.tf_map_camera)
  return _msg;
}
inline void Camera::set_allocated_tf_map_camera(::perception::config::Transformation3d* tf_map_camera) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_camera_);
  }
  if (tf_map_camera) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tf_map_camera));
    if (message_arena != submessage_arena) {
      tf_map_camera = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tf_map_camera, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tf_map_camera_ = tf_map_camera;
  // @@protoc_insertion_point(field_set_allocated:perception.config.Camera.tf_map_camera)
}

// double fx = 5;
inline void Camera::clear_fx() {
  _impl_.fx_ = 0;
}
inline double Camera::_internal_fx() const {
  return _impl_.fx_;
}
inline double Camera::fx() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.fx)
  return _internal_fx();
}
inline void Camera::_internal_set_fx(double value) {
  
  _impl_.fx_ = value;
}
inline void Camera::set_fx(double value) {
  _internal_set_fx(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.fx)
}

// double fy = 6;
inline void Camera::clear_fy() {
  _impl_.fy_ = 0;
}
inline double Camera::_internal_fy() const {
  return _impl_.fy_;
}
inline double Camera::fy() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.fy)
  return _internal_fy();
}
inline void Camera::_internal_set_fy(double value) {
  
  _impl_.fy_ = value;
}
inline void Camera::set_fy(double value) {
  _internal_set_fy(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.fy)
}

// double cx = 7;
inline void Camera::clear_cx() {
  _impl_.cx_ = 0;
}
inline double Camera::_internal_cx() const {
  return _impl_.cx_;
}
inline double Camera::cx() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.cx)
  return _internal_cx();
}
inline void Camera::_internal_set_cx(double value) {
  
  _impl_.cx_ = value;
}
inline void Camera::set_cx(double value) {
  _internal_set_cx(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.cx)
}

// double cy = 8;
inline void Camera::clear_cy() {
  _impl_.cy_ = 0;
}
inline double Camera::_internal_cy() const {
  return _impl_.cy_;
}
inline double Camera::cy() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.cy)
  return _internal_cy();
}
inline void Camera::_internal_set_cy(double value) {
  
  _impl_.cy_ = value;
}
inline void Camera::set_cy(double value) {
  _internal_set_cy(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.cy)
}

// repeated double radial_distortion = 9;
inline int Camera::_internal_radial_distortion_size() const {
  return _impl_.radial_distortion_.size();
}
inline int Camera::radial_distortion_size() const {
  return _internal_radial_distortion_size();
}
inline void Camera::clear_radial_distortion() {
  _impl_.radial_distortion_.Clear();
}
inline double Camera::_internal_radial_distortion(int index) const {
  return _impl_.radial_distortion_.Get(index);
}
inline double Camera::radial_distortion(int index) const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.radial_distortion)
  return _internal_radial_distortion(index);
}
inline void Camera::set_radial_distortion(int index, double value) {
  _impl_.radial_distortion_.Set(index, value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.radial_distortion)
}
inline void Camera::_internal_add_radial_distortion(double value) {
  _impl_.radial_distortion_.Add(value);
}
inline void Camera::add_radial_distortion(double value) {
  _internal_add_radial_distortion(value);
  // @@protoc_insertion_point(field_add:perception.config.Camera.radial_distortion)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
Camera::_internal_radial_distortion() const {
  return _impl_.radial_distortion_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >&
Camera::radial_distortion() const {
  // @@protoc_insertion_point(field_list:perception.config.Camera.radial_distortion)
  return _internal_radial_distortion();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
Camera::_internal_mutable_radial_distortion() {
  return &_impl_.radial_distortion_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< double >*
Camera::mutable_radial_distortion() {
  // @@protoc_insertion_point(field_mutable_list:perception.config.Camera.radial_distortion)
  return _internal_mutable_radial_distortion();
}

// bytes topic = 10;
inline void Camera::clear_topic() {
  _impl_.topic_.ClearToEmpty();
}
inline const std::string& Camera::topic() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.topic)
  return _internal_topic();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Camera::set_topic(ArgT0&& arg0, ArgT... args) {
 
 _impl_.topic_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.Camera.topic)
}
inline std::string* Camera::mutable_topic() {
  std::string* _s = _internal_mutable_topic();
  // @@protoc_insertion_point(field_mutable:perception.config.Camera.topic)
  return _s;
}
inline const std::string& Camera::_internal_topic() const {
  return _impl_.topic_.Get();
}
inline void Camera::_internal_set_topic(const std::string& value) {
  
  _impl_.topic_.Set(value, GetArenaForAllocation());
}
inline std::string* Camera::_internal_mutable_topic() {
  
  return _impl_.topic_.Mutable(GetArenaForAllocation());
}
inline std::string* Camera::release_topic() {
  // @@protoc_insertion_point(field_release:perception.config.Camera.topic)
  return _impl_.topic_.Release();
}
inline void Camera::set_allocated_topic(std::string* topic) {
  if (topic != nullptr) {
    
  } else {
    
  }
  _impl_.topic_.SetAllocated(topic, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.topic_.IsDefault()) {
    _impl_.topic_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.Camera.topic)
}

// bool time_synced = 11;
inline void Camera::clear_time_synced() {
  _impl_.time_synced_ = false;
}
inline bool Camera::_internal_time_synced() const {
  return _impl_.time_synced_;
}
inline bool Camera::time_synced() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.time_synced)
  return _internal_time_synced();
}
inline void Camera::_internal_set_time_synced(bool value) {
  
  _impl_.time_synced_ = value;
}
inline void Camera::set_time_synced(bool value) {
  _internal_set_time_synced(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.time_synced)
}

// int32 usage = 12;
inline void Camera::clear_usage() {
  _impl_.usage_ = 0;
}
inline int32_t Camera::_internal_usage() const {
  return _impl_.usage_;
}
inline int32_t Camera::usage() const {
  // @@protoc_insertion_point(field_get:perception.config.Camera.usage)
  return _internal_usage();
}
inline void Camera::_internal_set_usage(int32_t value) {
  
  _impl_.usage_ = value;
}
inline void Camera::set_usage(int32_t value) {
  _internal_set_usage(value);
  // @@protoc_insertion_point(field_set:perception.config.Camera.usage)
}

// -------------------------------------------------------------------

// ConfigGroup

// bytes group_name = 1;
inline void ConfigGroup::clear_group_name() {
  _impl_.group_name_.ClearToEmpty();
}
inline const std::string& ConfigGroup::group_name() const {
  // @@protoc_insertion_point(field_get:perception.config.ConfigGroup.group_name)
  return _internal_group_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigGroup::set_group_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.config.ConfigGroup.group_name)
}
inline std::string* ConfigGroup::mutable_group_name() {
  std::string* _s = _internal_mutable_group_name();
  // @@protoc_insertion_point(field_mutable:perception.config.ConfigGroup.group_name)
  return _s;
}
inline const std::string& ConfigGroup::_internal_group_name() const {
  return _impl_.group_name_.Get();
}
inline void ConfigGroup::_internal_set_group_name(const std::string& value) {
  
  _impl_.group_name_.Set(value, GetArenaForAllocation());
}
inline std::string* ConfigGroup::_internal_mutable_group_name() {
  
  return _impl_.group_name_.Mutable(GetArenaForAllocation());
}
inline std::string* ConfigGroup::release_group_name() {
  // @@protoc_insertion_point(field_release:perception.config.ConfigGroup.group_name)
  return _impl_.group_name_.Release();
}
inline void ConfigGroup::set_allocated_group_name(std::string* group_name) {
  if (group_name != nullptr) {
    
  } else {
    
  }
  _impl_.group_name_.SetAllocated(group_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_name_.IsDefault()) {
    _impl_.group_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.config.ConfigGroup.group_name)
}

// int32 group_type = 2;
inline void ConfigGroup::clear_group_type() {
  _impl_.group_type_ = 0;
}
inline int32_t ConfigGroup::_internal_group_type() const {
  return _impl_.group_type_;
}
inline int32_t ConfigGroup::group_type() const {
  // @@protoc_insertion_point(field_get:perception.config.ConfigGroup.group_type)
  return _internal_group_type();
}
inline void ConfigGroup::_internal_set_group_type(int32_t value) {
  
  _impl_.group_type_ = value;
}
inline void ConfigGroup::set_group_type(int32_t value) {
  _internal_set_group_type(value);
  // @@protoc_insertion_point(field_set:perception.config.ConfigGroup.group_type)
}

// .perception.config.Transformation3d tf_map_group = 3;
inline bool ConfigGroup::_internal_has_tf_map_group() const {
  return this != internal_default_instance() && _impl_.tf_map_group_ != nullptr;
}
inline bool ConfigGroup::has_tf_map_group() const {
  return _internal_has_tf_map_group();
}
inline const ::perception::config::Transformation3d& ConfigGroup::_internal_tf_map_group() const {
  const ::perception::config::Transformation3d* p = _impl_.tf_map_group_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Transformation3d&>(
      ::perception::config::_Transformation3d_default_instance_);
}
inline const ::perception::config::Transformation3d& ConfigGroup::tf_map_group() const {
  // @@protoc_insertion_point(field_get:perception.config.ConfigGroup.tf_map_group)
  return _internal_tf_map_group();
}
inline void ConfigGroup::unsafe_arena_set_allocated_tf_map_group(
    ::perception::config::Transformation3d* tf_map_group) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_group_);
  }
  _impl_.tf_map_group_ = tf_map_group;
  if (tf_map_group) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.ConfigGroup.tf_map_group)
}
inline ::perception::config::Transformation3d* ConfigGroup::release_tf_map_group() {
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_group_;
  _impl_.tf_map_group_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Transformation3d* ConfigGroup::unsafe_arena_release_tf_map_group() {
  // @@protoc_insertion_point(field_release:perception.config.ConfigGroup.tf_map_group)
  
  ::perception::config::Transformation3d* temp = _impl_.tf_map_group_;
  _impl_.tf_map_group_ = nullptr;
  return temp;
}
inline ::perception::config::Transformation3d* ConfigGroup::_internal_mutable_tf_map_group() {
  
  if (_impl_.tf_map_group_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Transformation3d>(GetArenaForAllocation());
    _impl_.tf_map_group_ = p;
  }
  return _impl_.tf_map_group_;
}
inline ::perception::config::Transformation3d* ConfigGroup::mutable_tf_map_group() {
  ::perception::config::Transformation3d* _msg = _internal_mutable_tf_map_group();
  // @@protoc_insertion_point(field_mutable:perception.config.ConfigGroup.tf_map_group)
  return _msg;
}
inline void ConfigGroup::set_allocated_tf_map_group(::perception::config::Transformation3d* tf_map_group) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.tf_map_group_);
  }
  if (tf_map_group) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tf_map_group));
    if (message_arena != submessage_arena) {
      tf_map_group = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tf_map_group, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.tf_map_group_ = tf_map_group;
  // @@protoc_insertion_point(field_set_allocated:perception.config.ConfigGroup.tf_map_group)
}

// repeated .perception.config.Radar radars = 4;
inline int ConfigGroup::_internal_radars_size() const {
  return _impl_.radars_.size();
}
inline int ConfigGroup::radars_size() const {
  return _internal_radars_size();
}
inline void ConfigGroup::clear_radars() {
  _impl_.radars_.Clear();
}
inline ::perception::config::Radar* ConfigGroup::mutable_radars(int index) {
  // @@protoc_insertion_point(field_mutable:perception.config.ConfigGroup.radars)
  return _impl_.radars_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Radar >*
ConfigGroup::mutable_radars() {
  // @@protoc_insertion_point(field_mutable_list:perception.config.ConfigGroup.radars)
  return &_impl_.radars_;
}
inline const ::perception::config::Radar& ConfigGroup::_internal_radars(int index) const {
  return _impl_.radars_.Get(index);
}
inline const ::perception::config::Radar& ConfigGroup::radars(int index) const {
  // @@protoc_insertion_point(field_get:perception.config.ConfigGroup.radars)
  return _internal_radars(index);
}
inline ::perception::config::Radar* ConfigGroup::_internal_add_radars() {
  return _impl_.radars_.Add();
}
inline ::perception::config::Radar* ConfigGroup::add_radars() {
  ::perception::config::Radar* _add = _internal_add_radars();
  // @@protoc_insertion_point(field_add:perception.config.ConfigGroup.radars)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Radar >&
ConfigGroup::radars() const {
  // @@protoc_insertion_point(field_list:perception.config.ConfigGroup.radars)
  return _impl_.radars_;
}

// repeated .perception.config.Camera cameras = 5;
inline int ConfigGroup::_internal_cameras_size() const {
  return _impl_.cameras_.size();
}
inline int ConfigGroup::cameras_size() const {
  return _internal_cameras_size();
}
inline void ConfigGroup::clear_cameras() {
  _impl_.cameras_.Clear();
}
inline ::perception::config::Camera* ConfigGroup::mutable_cameras(int index) {
  // @@protoc_insertion_point(field_mutable:perception.config.ConfigGroup.cameras)
  return _impl_.cameras_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Camera >*
ConfigGroup::mutable_cameras() {
  // @@protoc_insertion_point(field_mutable_list:perception.config.ConfigGroup.cameras)
  return &_impl_.cameras_;
}
inline const ::perception::config::Camera& ConfigGroup::_internal_cameras(int index) const {
  return _impl_.cameras_.Get(index);
}
inline const ::perception::config::Camera& ConfigGroup::cameras(int index) const {
  // @@protoc_insertion_point(field_get:perception.config.ConfigGroup.cameras)
  return _internal_cameras(index);
}
inline ::perception::config::Camera* ConfigGroup::_internal_add_cameras() {
  return _impl_.cameras_.Add();
}
inline ::perception::config::Camera* ConfigGroup::add_cameras() {
  ::perception::config::Camera* _add = _internal_add_cameras();
  // @@protoc_insertion_point(field_add:perception.config.ConfigGroup.cameras)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::config::Camera >&
ConfigGroup::cameras() const {
  // @@protoc_insertion_point(field_list:perception.config.ConfigGroup.cameras)
  return _impl_.cameras_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace perception

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::perception::config::Lidar_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::perception::config::Lidar_Type>() {
  return ::perception::config::Lidar_Type_descriptor();
}
template <> struct is_proto_enum< ::perception::config::Radar_Type> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::perception::config::Radar_Type>() {
  return ::perception::config::Radar_Type_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_2eproto
