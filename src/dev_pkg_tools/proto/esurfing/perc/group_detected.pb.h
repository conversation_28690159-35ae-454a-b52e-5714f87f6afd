// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/perc/group_detected.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_5fdetected_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_5fdetected_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021010 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "esurfing/math/geo.pb.h"
#include "esurfing/math/color.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_esurfing_2fperc_2fgroup_5fdetected_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_esurfing_2fperc_2fgroup_5fdetected_2eproto;
namespace perception {
namespace detect {
class DetectedObject;
struct DetectedObjectDefaultTypeInternal;
extern DetectedObjectDefaultTypeInternal _DetectedObject_default_instance_;
class DetectedObjects;
struct DetectedObjectsDefaultTypeInternal;
extern DetectedObjectsDefaultTypeInternal _DetectedObjects_default_instance_;
class LicenePlate;
struct LicenePlateDefaultTypeInternal;
extern LicenePlateDefaultTypeInternal _LicenePlate_default_instance_;
class TrackConfidence;
struct TrackConfidenceDefaultTypeInternal;
extern TrackConfidenceDefaultTypeInternal _TrackConfidence_default_instance_;
class Velocity;
struct VelocityDefaultTypeInternal;
extern VelocityDefaultTypeInternal _Velocity_default_instance_;
class WayPoint;
struct WayPointDefaultTypeInternal;
extern WayPointDefaultTypeInternal _WayPoint_default_instance_;
class WayPoints;
struct WayPointsDefaultTypeInternal;
extern WayPointsDefaultTypeInternal _WayPoints_default_instance_;
}  // namespace detect
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> ::perception::detect::DetectedObject* Arena::CreateMaybeMessage<::perception::detect::DetectedObject>(Arena*);
template<> ::perception::detect::DetectedObjects* Arena::CreateMaybeMessage<::perception::detect::DetectedObjects>(Arena*);
template<> ::perception::detect::LicenePlate* Arena::CreateMaybeMessage<::perception::detect::LicenePlate>(Arena*);
template<> ::perception::detect::TrackConfidence* Arena::CreateMaybeMessage<::perception::detect::TrackConfidence>(Arena*);
template<> ::perception::detect::Velocity* Arena::CreateMaybeMessage<::perception::detect::Velocity>(Arena*);
template<> ::perception::detect::WayPoint* Arena::CreateMaybeMessage<::perception::detect::WayPoint>(Arena*);
template<> ::perception::detect::WayPoints* Arena::CreateMaybeMessage<::perception::detect::WayPoints>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace perception {
namespace detect {

enum ObjectType : int {
  UNKNOWN = 0,
  CAR = 1,
  PEDESTRIAN = 2,
  CYCLIST = 3,
  TRUCK = 4,
  VAN = 5,
  BUS = 6,
  STATIC = 7,
  STATIC_EDGE = 8,
  CONE = 9,
  TROLLEY = 10,
  ROBOT = 11,
  GATE = 12,
  AIRPLANE = 13,
  DRONE = 14,
  HELICOPTER = 15,
  EVTOL = 16,
  BIRD = 17,
  ObjectType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ObjectType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ObjectType_IsValid(int value);
constexpr ObjectType ObjectType_MIN = UNKNOWN;
constexpr ObjectType ObjectType_MAX = BIRD;
constexpr int ObjectType_ARRAYSIZE = ObjectType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ObjectType_descriptor();
template<typename T>
inline const std::string& ObjectType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ObjectType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ObjectType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ObjectType_descriptor(), enum_t_value);
}
inline bool ObjectType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ObjectType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ObjectType>(
    ObjectType_descriptor(), name, value);
}
// ===================================================================

class DetectedObjects final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.DetectedObjects) */ {
 public:
  inline DetectedObjects() : DetectedObjects(nullptr) {}
  ~DetectedObjects() override;
  explicit PROTOBUF_CONSTEXPR DetectedObjects(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DetectedObjects(const DetectedObjects& from);
  DetectedObjects(DetectedObjects&& from) noexcept
    : DetectedObjects() {
    *this = ::std::move(from);
  }

  inline DetectedObjects& operator=(const DetectedObjects& from) {
    CopyFrom(from);
    return *this;
  }
  inline DetectedObjects& operator=(DetectedObjects&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DetectedObjects& default_instance() {
    return *internal_default_instance();
  }
  static inline const DetectedObjects* internal_default_instance() {
    return reinterpret_cast<const DetectedObjects*>(
               &_DetectedObjects_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DetectedObjects& a, DetectedObjects& b) {
    a.Swap(&b);
  }
  inline void Swap(DetectedObjects* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DetectedObjects* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DetectedObjects* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DetectedObjects>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DetectedObjects& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DetectedObjects& from) {
    DetectedObjects::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DetectedObjects* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.DetectedObjects";
  }
  protected:
  explicit DetectedObjects(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kObjectsFieldNumber = 5,
    kGroupNameFieldNumber = 3,
    kGroupInfoFieldNumber = 4,
    kTimeMeasFieldNumber = 1,
    kTimePubFieldNumber = 2,
  };
  // repeated .perception.detect.DetectedObject objects = 5;
  int objects_size() const;
  private:
  int _internal_objects_size() const;
  public:
  void clear_objects();
  ::perception::detect::DetectedObject* mutable_objects(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::DetectedObject >*
      mutable_objects();
  private:
  const ::perception::detect::DetectedObject& _internal_objects(int index) const;
  ::perception::detect::DetectedObject* _internal_add_objects();
  public:
  const ::perception::detect::DetectedObject& objects(int index) const;
  ::perception::detect::DetectedObject* add_objects();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::DetectedObject >&
      objects() const;

  // bytes group_name = 3;
  void clear_group_name();
  const std::string& group_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_name();
  PROTOBUF_NODISCARD std::string* release_group_name();
  void set_allocated_group_name(std::string* group_name);
  private:
  const std::string& _internal_group_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_name(const std::string& value);
  std::string* _internal_mutable_group_name();
  public:

  // bytes group_info = 4;
  void clear_group_info();
  const std::string& group_info() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_info(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_info();
  PROTOBUF_NODISCARD std::string* release_group_info();
  void set_allocated_group_info(std::string* group_info);
  private:
  const std::string& _internal_group_info() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_info(const std::string& value);
  std::string* _internal_mutable_group_info();
  public:

  // sfixed64 time_meas = 1;
  void clear_time_meas();
  int64_t time_meas() const;
  void set_time_meas(int64_t value);
  private:
  int64_t _internal_time_meas() const;
  void _internal_set_time_meas(int64_t value);
  public:

  // sfixed64 time_pub = 2;
  void clear_time_pub();
  int64_t time_pub() const;
  void set_time_pub(int64_t value);
  private:
  int64_t _internal_time_pub() const;
  void _internal_set_time_pub(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.DetectedObjects)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::DetectedObject > objects_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_info_;
    int64_t time_meas_;
    int64_t time_pub_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class LicenePlate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.LicenePlate) */ {
 public:
  inline LicenePlate() : LicenePlate(nullptr) {}
  ~LicenePlate() override;
  explicit PROTOBUF_CONSTEXPR LicenePlate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LicenePlate(const LicenePlate& from);
  LicenePlate(LicenePlate&& from) noexcept
    : LicenePlate() {
    *this = ::std::move(from);
  }

  inline LicenePlate& operator=(const LicenePlate& from) {
    CopyFrom(from);
    return *this;
  }
  inline LicenePlate& operator=(LicenePlate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LicenePlate& default_instance() {
    return *internal_default_instance();
  }
  static inline const LicenePlate* internal_default_instance() {
    return reinterpret_cast<const LicenePlate*>(
               &_LicenePlate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LicenePlate& a, LicenePlate& b) {
    a.Swap(&b);
  }
  inline void Swap(LicenePlate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LicenePlate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LicenePlate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LicenePlate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LicenePlate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const LicenePlate& from) {
    LicenePlate::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LicenePlate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.LicenePlate";
  }
  protected:
  explicit LicenePlate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumberFieldNumber = 3,
    kColorFieldNumber = 1,
    kColorConfidenceFieldNumber = 2,
    kNumberConfidenceFieldNumber = 4,
  };
  // string number = 3;
  void clear_number();
  const std::string& number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_number();
  PROTOBUF_NODISCARD std::string* release_number();
  void set_allocated_number(std::string* number);
  private:
  const std::string& _internal_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_number(const std::string& value);
  std::string* _internal_mutable_number();
  public:

  // int32 color = 1;
  void clear_color();
  int32_t color() const;
  void set_color(int32_t value);
  private:
  int32_t _internal_color() const;
  void _internal_set_color(int32_t value);
  public:

  // float color_confidence = 2;
  void clear_color_confidence();
  float color_confidence() const;
  void set_color_confidence(float value);
  private:
  float _internal_color_confidence() const;
  void _internal_set_color_confidence(float value);
  public:

  // float number_confidence = 4;
  void clear_number_confidence();
  float number_confidence() const;
  void set_number_confidence(float value);
  private:
  float _internal_number_confidence() const;
  void _internal_set_number_confidence(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.LicenePlate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr number_;
    int32_t color_;
    float color_confidence_;
    float number_confidence_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class TrackConfidence final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.TrackConfidence) */ {
 public:
  inline TrackConfidence() : TrackConfidence(nullptr) {}
  ~TrackConfidence() override;
  explicit PROTOBUF_CONSTEXPR TrackConfidence(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TrackConfidence(const TrackConfidence& from);
  TrackConfidence(TrackConfidence&& from) noexcept
    : TrackConfidence() {
    *this = ::std::move(from);
  }

  inline TrackConfidence& operator=(const TrackConfidence& from) {
    CopyFrom(from);
    return *this;
  }
  inline TrackConfidence& operator=(TrackConfidence&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TrackConfidence& default_instance() {
    return *internal_default_instance();
  }
  static inline const TrackConfidence* internal_default_instance() {
    return reinterpret_cast<const TrackConfidence*>(
               &_TrackConfidence_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(TrackConfidence& a, TrackConfidence& b) {
    a.Swap(&b);
  }
  inline void Swap(TrackConfidence* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TrackConfidence* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TrackConfidence* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TrackConfidence>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TrackConfidence& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const TrackConfidence& from) {
    TrackConfidence::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TrackConfidence* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.TrackConfidence";
  }
  protected:
  explicit TrackConfidence(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kColorFieldNumber = 1,
    kTypeFieldNumber = 2,
    kPlateNumberFieldNumber = 3,
    kPlateColorFieldNumber = 4,
  };
  // uint32 color = 1;
  void clear_color();
  uint32_t color() const;
  void set_color(uint32_t value);
  private:
  uint32_t _internal_color() const;
  void _internal_set_color(uint32_t value);
  public:

  // uint32 type = 2;
  void clear_type();
  uint32_t type() const;
  void set_type(uint32_t value);
  private:
  uint32_t _internal_type() const;
  void _internal_set_type(uint32_t value);
  public:

  // uint32 plate_number = 3;
  void clear_plate_number();
  uint32_t plate_number() const;
  void set_plate_number(uint32_t value);
  private:
  uint32_t _internal_plate_number() const;
  void _internal_set_plate_number(uint32_t value);
  public:

  // uint32 plate_color = 4;
  void clear_plate_color();
  uint32_t plate_color() const;
  void set_plate_color(uint32_t value);
  private:
  uint32_t _internal_plate_color() const;
  void _internal_set_plate_color(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.TrackConfidence)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    uint32_t color_;
    uint32_t type_;
    uint32_t plate_number_;
    uint32_t plate_color_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class DetectedObject final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.DetectedObject) */ {
 public:
  inline DetectedObject() : DetectedObject(nullptr) {}
  ~DetectedObject() override;
  explicit PROTOBUF_CONSTEXPR DetectedObject(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DetectedObject(const DetectedObject& from);
  DetectedObject(DetectedObject&& from) noexcept
    : DetectedObject() {
    *this = ::std::move(from);
  }

  inline DetectedObject& operator=(const DetectedObject& from) {
    CopyFrom(from);
    return *this;
  }
  inline DetectedObject& operator=(DetectedObject&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DetectedObject& default_instance() {
    return *internal_default_instance();
  }
  static inline const DetectedObject* internal_default_instance() {
    return reinterpret_cast<const DetectedObject*>(
               &_DetectedObject_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DetectedObject& a, DetectedObject& b) {
    a.Swap(&b);
  }
  inline void Swap(DetectedObject* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DetectedObject* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DetectedObject* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DetectedObject>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DetectedObject& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const DetectedObject& from) {
    DetectedObject::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DetectedObject* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.DetectedObject";
  }
  protected:
  explicit DetectedObject(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFeatureFieldNumber = 12,
    kTrajectoriesFieldNumber = 13,
    kStrArrayFieldNumber = 14,
    kIntArrayFieldNumber = 15,
    kOverlapNameFieldNumber = 1,
    kGroupIdFieldNumber = 19,
    kCameraNameFieldNumber = 22,
    kPositionFieldNumber = 5,
    kShapeFieldNumber = 6,
    kHullFieldNumber = 7,
    kVelocityFieldNumber = 9,
    kColorFieldNumber = 11,
    kPlateFieldNumber = 17,
    kTrackConfFieldNumber = 21,
    kPositionImageFieldNumber = 23,
    kShapeImageFieldNumber = 24,
    kUuidFieldNumber = 2,
    kTypeFieldNumber = 3,
    kConfidenceFieldNumber = 4,
    kOrientationFieldNumber = 8,
    kIsStaticFieldNumber = 10,
    kParkingTimeFieldNumber = 16,
    kObjColorFieldNumber = 18,
    kObjColorConfFieldNumber = 20,
  };
  // repeated float feature = 12;
  int feature_size() const;
  private:
  int _internal_feature_size() const;
  public:
  void clear_feature();
  private:
  float _internal_feature(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_feature() const;
  void _internal_add_feature(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_feature();
  public:
  float feature(int index) const;
  void set_feature(int index, float value);
  void add_feature(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      feature() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_feature();

  // repeated .perception.detect.WayPoints trajectories = 13;
  int trajectories_size() const;
  private:
  int _internal_trajectories_size() const;
  public:
  void clear_trajectories();
  ::perception::detect::WayPoints* mutable_trajectories(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoints >*
      mutable_trajectories();
  private:
  const ::perception::detect::WayPoints& _internal_trajectories(int index) const;
  ::perception::detect::WayPoints* _internal_add_trajectories();
  public:
  const ::perception::detect::WayPoints& trajectories(int index) const;
  ::perception::detect::WayPoints* add_trajectories();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoints >&
      trajectories() const;

  // repeated bytes str_array = 14;
  int str_array_size() const;
  private:
  int _internal_str_array_size() const;
  public:
  void clear_str_array();
  const std::string& str_array(int index) const;
  std::string* mutable_str_array(int index);
  void set_str_array(int index, const std::string& value);
  void set_str_array(int index, std::string&& value);
  void set_str_array(int index, const char* value);
  void set_str_array(int index, const void* value, size_t size);
  std::string* add_str_array();
  void add_str_array(const std::string& value);
  void add_str_array(std::string&& value);
  void add_str_array(const char* value);
  void add_str_array(const void* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& str_array() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_str_array();
  private:
  const std::string& _internal_str_array(int index) const;
  std::string* _internal_add_str_array();
  public:

  // repeated sint32 int_array = 15;
  int int_array_size() const;
  private:
  int _internal_int_array_size() const;
  public:
  void clear_int_array();
  private:
  int32_t _internal_int_array(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_int_array() const;
  void _internal_add_int_array(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_int_array();
  public:
  int32_t int_array(int index) const;
  void set_int_array(int index, int32_t value);
  void add_int_array(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      int_array() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_int_array();

  // bytes overlap_name = 1;
  void clear_overlap_name();
  const std::string& overlap_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_overlap_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_overlap_name();
  PROTOBUF_NODISCARD std::string* release_overlap_name();
  void set_allocated_overlap_name(std::string* overlap_name);
  private:
  const std::string& _internal_overlap_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_overlap_name(const std::string& value);
  std::string* _internal_mutable_overlap_name();
  public:

  // string group_id = 19;
  void clear_group_id();
  const std::string& group_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_id();
  PROTOBUF_NODISCARD std::string* release_group_id();
  void set_allocated_group_id(std::string* group_id);
  private:
  const std::string& _internal_group_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_id(const std::string& value);
  std::string* _internal_mutable_group_id();
  public:

  // string camera_name = 22;
  void clear_camera_name();
  const std::string& camera_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera_name();
  PROTOBUF_NODISCARD std::string* release_camera_name();
  void set_allocated_camera_name(std::string* camera_name);
  private:
  const std::string& _internal_camera_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera_name(const std::string& value);
  std::string* _internal_mutable_camera_name();
  public:

  // .perception.detect.Vector3f position = 5;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::perception::detect::Vector3f& position() const;
  PROTOBUF_NODISCARD ::perception::detect::Vector3f* release_position();
  ::perception::detect::Vector3f* mutable_position();
  void set_allocated_position(::perception::detect::Vector3f* position);
  private:
  const ::perception::detect::Vector3f& _internal_position() const;
  ::perception::detect::Vector3f* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::perception::detect::Vector3f* position);
  ::perception::detect::Vector3f* unsafe_arena_release_position();

  // .perception.detect.Vector3f shape = 6;
  bool has_shape() const;
  private:
  bool _internal_has_shape() const;
  public:
  void clear_shape();
  const ::perception::detect::Vector3f& shape() const;
  PROTOBUF_NODISCARD ::perception::detect::Vector3f* release_shape();
  ::perception::detect::Vector3f* mutable_shape();
  void set_allocated_shape(::perception::detect::Vector3f* shape);
  private:
  const ::perception::detect::Vector3f& _internal_shape() const;
  ::perception::detect::Vector3f* _internal_mutable_shape();
  public:
  void unsafe_arena_set_allocated_shape(
      ::perception::detect::Vector3f* shape);
  ::perception::detect::Vector3f* unsafe_arena_release_shape();

  // .perception.detect.Polygon hull = 7;
  bool has_hull() const;
  private:
  bool _internal_has_hull() const;
  public:
  void clear_hull();
  const ::perception::detect::Polygon& hull() const;
  PROTOBUF_NODISCARD ::perception::detect::Polygon* release_hull();
  ::perception::detect::Polygon* mutable_hull();
  void set_allocated_hull(::perception::detect::Polygon* hull);
  private:
  const ::perception::detect::Polygon& _internal_hull() const;
  ::perception::detect::Polygon* _internal_mutable_hull();
  public:
  void unsafe_arena_set_allocated_hull(
      ::perception::detect::Polygon* hull);
  ::perception::detect::Polygon* unsafe_arena_release_hull();

  // .perception.detect.Velocity velocity = 9;
  bool has_velocity() const;
  private:
  bool _internal_has_velocity() const;
  public:
  void clear_velocity();
  const ::perception::detect::Velocity& velocity() const;
  PROTOBUF_NODISCARD ::perception::detect::Velocity* release_velocity();
  ::perception::detect::Velocity* mutable_velocity();
  void set_allocated_velocity(::perception::detect::Velocity* velocity);
  private:
  const ::perception::detect::Velocity& _internal_velocity() const;
  ::perception::detect::Velocity* _internal_mutable_velocity();
  public:
  void unsafe_arena_set_allocated_velocity(
      ::perception::detect::Velocity* velocity);
  ::perception::detect::Velocity* unsafe_arena_release_velocity();

  // .perception.detect.Color color = 11;
  bool has_color() const;
  private:
  bool _internal_has_color() const;
  public:
  void clear_color();
  const ::perception::detect::Color& color() const;
  PROTOBUF_NODISCARD ::perception::detect::Color* release_color();
  ::perception::detect::Color* mutable_color();
  void set_allocated_color(::perception::detect::Color* color);
  private:
  const ::perception::detect::Color& _internal_color() const;
  ::perception::detect::Color* _internal_mutable_color();
  public:
  void unsafe_arena_set_allocated_color(
      ::perception::detect::Color* color);
  ::perception::detect::Color* unsafe_arena_release_color();

  // .perception.detect.LicenePlate plate = 17;
  bool has_plate() const;
  private:
  bool _internal_has_plate() const;
  public:
  void clear_plate();
  const ::perception::detect::LicenePlate& plate() const;
  PROTOBUF_NODISCARD ::perception::detect::LicenePlate* release_plate();
  ::perception::detect::LicenePlate* mutable_plate();
  void set_allocated_plate(::perception::detect::LicenePlate* plate);
  private:
  const ::perception::detect::LicenePlate& _internal_plate() const;
  ::perception::detect::LicenePlate* _internal_mutable_plate();
  public:
  void unsafe_arena_set_allocated_plate(
      ::perception::detect::LicenePlate* plate);
  ::perception::detect::LicenePlate* unsafe_arena_release_plate();

  // .perception.detect.TrackConfidence track_conf = 21;
  bool has_track_conf() const;
  private:
  bool _internal_has_track_conf() const;
  public:
  void clear_track_conf();
  const ::perception::detect::TrackConfidence& track_conf() const;
  PROTOBUF_NODISCARD ::perception::detect::TrackConfidence* release_track_conf();
  ::perception::detect::TrackConfidence* mutable_track_conf();
  void set_allocated_track_conf(::perception::detect::TrackConfidence* track_conf);
  private:
  const ::perception::detect::TrackConfidence& _internal_track_conf() const;
  ::perception::detect::TrackConfidence* _internal_mutable_track_conf();
  public:
  void unsafe_arena_set_allocated_track_conf(
      ::perception::detect::TrackConfidence* track_conf);
  ::perception::detect::TrackConfidence* unsafe_arena_release_track_conf();

  // .perception.detect.Vector3f position_image = 23;
  bool has_position_image() const;
  private:
  bool _internal_has_position_image() const;
  public:
  void clear_position_image();
  const ::perception::detect::Vector3f& position_image() const;
  PROTOBUF_NODISCARD ::perception::detect::Vector3f* release_position_image();
  ::perception::detect::Vector3f* mutable_position_image();
  void set_allocated_position_image(::perception::detect::Vector3f* position_image);
  private:
  const ::perception::detect::Vector3f& _internal_position_image() const;
  ::perception::detect::Vector3f* _internal_mutable_position_image();
  public:
  void unsafe_arena_set_allocated_position_image(
      ::perception::detect::Vector3f* position_image);
  ::perception::detect::Vector3f* unsafe_arena_release_position_image();

  // .perception.detect.Vector2f shape_image = 24;
  bool has_shape_image() const;
  private:
  bool _internal_has_shape_image() const;
  public:
  void clear_shape_image();
  const ::perception::detect::Vector2f& shape_image() const;
  PROTOBUF_NODISCARD ::perception::detect::Vector2f* release_shape_image();
  ::perception::detect::Vector2f* mutable_shape_image();
  void set_allocated_shape_image(::perception::detect::Vector2f* shape_image);
  private:
  const ::perception::detect::Vector2f& _internal_shape_image() const;
  ::perception::detect::Vector2f* _internal_mutable_shape_image();
  public:
  void unsafe_arena_set_allocated_shape_image(
      ::perception::detect::Vector2f* shape_image);
  ::perception::detect::Vector2f* unsafe_arena_release_shape_image();

  // fixed64 uuid = 2;
  void clear_uuid();
  uint64_t uuid() const;
  void set_uuid(uint64_t value);
  private:
  uint64_t _internal_uuid() const;
  void _internal_set_uuid(uint64_t value);
  public:

  // sint32 type = 3;
  void clear_type();
  int32_t type() const;
  void set_type(int32_t value);
  private:
  int32_t _internal_type() const;
  void _internal_set_type(int32_t value);
  public:

  // float confidence = 4;
  void clear_confidence();
  float confidence() const;
  void set_confidence(float value);
  private:
  float _internal_confidence() const;
  void _internal_set_confidence(float value);
  public:

  // float orientation = 8;
  void clear_orientation();
  float orientation() const;
  void set_orientation(float value);
  private:
  float _internal_orientation() const;
  void _internal_set_orientation(float value);
  public:

  // bool is_static = 10;
  void clear_is_static();
  bool is_static() const;
  void set_is_static(bool value);
  private:
  bool _internal_is_static() const;
  void _internal_set_is_static(bool value);
  public:

  // int64 parking_time = 16;
  void clear_parking_time();
  int64_t parking_time() const;
  void set_parking_time(int64_t value);
  private:
  int64_t _internal_parking_time() const;
  void _internal_set_parking_time(int64_t value);
  public:

  // int32 obj_color = 18;
  void clear_obj_color();
  int32_t obj_color() const;
  void set_obj_color(int32_t value);
  private:
  int32_t _internal_obj_color() const;
  void _internal_set_obj_color(int32_t value);
  public:

  // float obj_color_conf = 20;
  void clear_obj_color_conf();
  float obj_color_conf() const;
  void set_obj_color_conf(float value);
  private:
  float _internal_obj_color_conf() const;
  void _internal_set_obj_color_conf(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.DetectedObject)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > feature_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoints > trajectories_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> str_array_;
    ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > int_array_;
    mutable std::atomic<int> _int_array_cached_byte_size_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr overlap_name_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_id_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_name_;
    ::perception::detect::Vector3f* position_;
    ::perception::detect::Vector3f* shape_;
    ::perception::detect::Polygon* hull_;
    ::perception::detect::Velocity* velocity_;
    ::perception::detect::Color* color_;
    ::perception::detect::LicenePlate* plate_;
    ::perception::detect::TrackConfidence* track_conf_;
    ::perception::detect::Vector3f* position_image_;
    ::perception::detect::Vector2f* shape_image_;
    uint64_t uuid_;
    int32_t type_;
    float confidence_;
    float orientation_;
    bool is_static_;
    int64_t parking_time_;
    int32_t obj_color_;
    float obj_color_conf_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class Velocity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.Velocity) */ {
 public:
  inline Velocity() : Velocity(nullptr) {}
  ~Velocity() override;
  explicit PROTOBUF_CONSTEXPR Velocity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Velocity(const Velocity& from);
  Velocity(Velocity&& from) noexcept
    : Velocity() {
    *this = ::std::move(from);
  }

  inline Velocity& operator=(const Velocity& from) {
    CopyFrom(from);
    return *this;
  }
  inline Velocity& operator=(Velocity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Velocity& default_instance() {
    return *internal_default_instance();
  }
  static inline const Velocity* internal_default_instance() {
    return reinterpret_cast<const Velocity*>(
               &_Velocity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(Velocity& a, Velocity& b) {
    a.Swap(&b);
  }
  inline void Swap(Velocity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Velocity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Velocity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Velocity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Velocity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Velocity& from) {
    Velocity::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Velocity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.Velocity";
  }
  protected:
  explicit Velocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHeadingFieldNumber = 1,
    kSpeedFieldNumber = 2,
    kAccelerationFieldNumber = 3,
  };
  // float heading = 1;
  void clear_heading();
  float heading() const;
  void set_heading(float value);
  private:
  float _internal_heading() const;
  void _internal_set_heading(float value);
  public:

  // float speed = 2;
  void clear_speed();
  float speed() const;
  void set_speed(float value);
  private:
  float _internal_speed() const;
  void _internal_set_speed(float value);
  public:

  // float acceleration = 3;
  void clear_acceleration();
  float acceleration() const;
  void set_acceleration(float value);
  private:
  float _internal_acceleration() const;
  void _internal_set_acceleration(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.Velocity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    float heading_;
    float speed_;
    float acceleration_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class WayPoints final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.WayPoints) */ {
 public:
  inline WayPoints() : WayPoints(nullptr) {}
  ~WayPoints() override;
  explicit PROTOBUF_CONSTEXPR WayPoints(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WayPoints(const WayPoints& from);
  WayPoints(WayPoints&& from) noexcept
    : WayPoints() {
    *this = ::std::move(from);
  }

  inline WayPoints& operator=(const WayPoints& from) {
    CopyFrom(from);
    return *this;
  }
  inline WayPoints& operator=(WayPoints&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WayPoints& default_instance() {
    return *internal_default_instance();
  }
  static inline const WayPoints* internal_default_instance() {
    return reinterpret_cast<const WayPoints*>(
               &_WayPoints_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(WayPoints& a, WayPoints& b) {
    a.Swap(&b);
  }
  inline void Swap(WayPoints* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WayPoints* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WayPoints* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WayPoints>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WayPoints& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WayPoints& from) {
    WayPoints::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WayPoints* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.WayPoints";
  }
  protected:
  explicit WayPoints(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWaypointsFieldNumber = 1,
    kProbabilityFieldNumber = 2,
  };
  // repeated .perception.detect.WayPoint waypoints = 1;
  int waypoints_size() const;
  private:
  int _internal_waypoints_size() const;
  public:
  void clear_waypoints();
  ::perception::detect::WayPoint* mutable_waypoints(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoint >*
      mutable_waypoints();
  private:
  const ::perception::detect::WayPoint& _internal_waypoints(int index) const;
  ::perception::detect::WayPoint* _internal_add_waypoints();
  public:
  const ::perception::detect::WayPoint& waypoints(int index) const;
  ::perception::detect::WayPoint* add_waypoints();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoint >&
      waypoints() const;

  // float probability = 2;
  void clear_probability();
  float probability() const;
  void set_probability(float value);
  private:
  float _internal_probability() const;
  void _internal_set_probability(float value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.WayPoints)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoint > waypoints_;
    float probability_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// -------------------------------------------------------------------

class WayPoint final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.detect.WayPoint) */ {
 public:
  inline WayPoint() : WayPoint(nullptr) {}
  ~WayPoint() override;
  explicit PROTOBUF_CONSTEXPR WayPoint(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WayPoint(const WayPoint& from);
  WayPoint(WayPoint&& from) noexcept
    : WayPoint() {
    *this = ::std::move(from);
  }

  inline WayPoint& operator=(const WayPoint& from) {
    CopyFrom(from);
    return *this;
  }
  inline WayPoint& operator=(WayPoint&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WayPoint& default_instance() {
    return *internal_default_instance();
  }
  static inline const WayPoint* internal_default_instance() {
    return reinterpret_cast<const WayPoint*>(
               &_WayPoint_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(WayPoint& a, WayPoint& b) {
    a.Swap(&b);
  }
  inline void Swap(WayPoint* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WayPoint* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WayPoint* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WayPoint>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WayPoint& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const WayPoint& from) {
    WayPoint::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WayPoint* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.detect.WayPoint";
  }
  protected:
  explicit WayPoint(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPositionFieldNumber = 2,
    kVelocityFieldNumber = 3,
    kTimeMeasFieldNumber = 1,
  };
  // .perception.detect.Vector3f position = 2;
  bool has_position() const;
  private:
  bool _internal_has_position() const;
  public:
  void clear_position();
  const ::perception::detect::Vector3f& position() const;
  PROTOBUF_NODISCARD ::perception::detect::Vector3f* release_position();
  ::perception::detect::Vector3f* mutable_position();
  void set_allocated_position(::perception::detect::Vector3f* position);
  private:
  const ::perception::detect::Vector3f& _internal_position() const;
  ::perception::detect::Vector3f* _internal_mutable_position();
  public:
  void unsafe_arena_set_allocated_position(
      ::perception::detect::Vector3f* position);
  ::perception::detect::Vector3f* unsafe_arena_release_position();

  // .perception.detect.Velocity velocity = 3;
  bool has_velocity() const;
  private:
  bool _internal_has_velocity() const;
  public:
  void clear_velocity();
  const ::perception::detect::Velocity& velocity() const;
  PROTOBUF_NODISCARD ::perception::detect::Velocity* release_velocity();
  ::perception::detect::Velocity* mutable_velocity();
  void set_allocated_velocity(::perception::detect::Velocity* velocity);
  private:
  const ::perception::detect::Velocity& _internal_velocity() const;
  ::perception::detect::Velocity* _internal_mutable_velocity();
  public:
  void unsafe_arena_set_allocated_velocity(
      ::perception::detect::Velocity* velocity);
  ::perception::detect::Velocity* unsafe_arena_release_velocity();

  // sfixed64 time_meas = 1;
  void clear_time_meas();
  int64_t time_meas() const;
  void set_time_meas(int64_t value);
  private:
  int64_t _internal_time_meas() const;
  void _internal_set_time_meas(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:perception.detect.WayPoint)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::perception::detect::Vector3f* position_;
    ::perception::detect::Velocity* velocity_;
    int64_t time_meas_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fperc_2fgroup_5fdetected_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DetectedObjects

// sfixed64 time_meas = 1;
inline void DetectedObjects::clear_time_meas() {
  _impl_.time_meas_ = int64_t{0};
}
inline int64_t DetectedObjects::_internal_time_meas() const {
  return _impl_.time_meas_;
}
inline int64_t DetectedObjects::time_meas() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObjects.time_meas)
  return _internal_time_meas();
}
inline void DetectedObjects::_internal_set_time_meas(int64_t value) {
  
  _impl_.time_meas_ = value;
}
inline void DetectedObjects::set_time_meas(int64_t value) {
  _internal_set_time_meas(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObjects.time_meas)
}

// sfixed64 time_pub = 2;
inline void DetectedObjects::clear_time_pub() {
  _impl_.time_pub_ = int64_t{0};
}
inline int64_t DetectedObjects::_internal_time_pub() const {
  return _impl_.time_pub_;
}
inline int64_t DetectedObjects::time_pub() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObjects.time_pub)
  return _internal_time_pub();
}
inline void DetectedObjects::_internal_set_time_pub(int64_t value) {
  
  _impl_.time_pub_ = value;
}
inline void DetectedObjects::set_time_pub(int64_t value) {
  _internal_set_time_pub(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObjects.time_pub)
}

// bytes group_name = 3;
inline void DetectedObjects::clear_group_name() {
  _impl_.group_name_.ClearToEmpty();
}
inline const std::string& DetectedObjects::group_name() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObjects.group_name)
  return _internal_group_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObjects::set_group_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObjects.group_name)
}
inline std::string* DetectedObjects::mutable_group_name() {
  std::string* _s = _internal_mutable_group_name();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObjects.group_name)
  return _s;
}
inline const std::string& DetectedObjects::_internal_group_name() const {
  return _impl_.group_name_.Get();
}
inline void DetectedObjects::_internal_set_group_name(const std::string& value) {
  
  _impl_.group_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObjects::_internal_mutable_group_name() {
  
  return _impl_.group_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObjects::release_group_name() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObjects.group_name)
  return _impl_.group_name_.Release();
}
inline void DetectedObjects::set_allocated_group_name(std::string* group_name) {
  if (group_name != nullptr) {
    
  } else {
    
  }
  _impl_.group_name_.SetAllocated(group_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_name_.IsDefault()) {
    _impl_.group_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObjects.group_name)
}

// bytes group_info = 4;
inline void DetectedObjects::clear_group_info() {
  _impl_.group_info_.ClearToEmpty();
}
inline const std::string& DetectedObjects::group_info() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObjects.group_info)
  return _internal_group_info();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObjects::set_group_info(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_info_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObjects.group_info)
}
inline std::string* DetectedObjects::mutable_group_info() {
  std::string* _s = _internal_mutable_group_info();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObjects.group_info)
  return _s;
}
inline const std::string& DetectedObjects::_internal_group_info() const {
  return _impl_.group_info_.Get();
}
inline void DetectedObjects::_internal_set_group_info(const std::string& value) {
  
  _impl_.group_info_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObjects::_internal_mutable_group_info() {
  
  return _impl_.group_info_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObjects::release_group_info() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObjects.group_info)
  return _impl_.group_info_.Release();
}
inline void DetectedObjects::set_allocated_group_info(std::string* group_info) {
  if (group_info != nullptr) {
    
  } else {
    
  }
  _impl_.group_info_.SetAllocated(group_info, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_info_.IsDefault()) {
    _impl_.group_info_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObjects.group_info)
}

// repeated .perception.detect.DetectedObject objects = 5;
inline int DetectedObjects::_internal_objects_size() const {
  return _impl_.objects_.size();
}
inline int DetectedObjects::objects_size() const {
  return _internal_objects_size();
}
inline void DetectedObjects::clear_objects() {
  _impl_.objects_.Clear();
}
inline ::perception::detect::DetectedObject* DetectedObjects::mutable_objects(int index) {
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObjects.objects)
  return _impl_.objects_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::DetectedObject >*
DetectedObjects::mutable_objects() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.DetectedObjects.objects)
  return &_impl_.objects_;
}
inline const ::perception::detect::DetectedObject& DetectedObjects::_internal_objects(int index) const {
  return _impl_.objects_.Get(index);
}
inline const ::perception::detect::DetectedObject& DetectedObjects::objects(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObjects.objects)
  return _internal_objects(index);
}
inline ::perception::detect::DetectedObject* DetectedObjects::_internal_add_objects() {
  return _impl_.objects_.Add();
}
inline ::perception::detect::DetectedObject* DetectedObjects::add_objects() {
  ::perception::detect::DetectedObject* _add = _internal_add_objects();
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObjects.objects)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::DetectedObject >&
DetectedObjects::objects() const {
  // @@protoc_insertion_point(field_list:perception.detect.DetectedObjects.objects)
  return _impl_.objects_;
}

// -------------------------------------------------------------------

// LicenePlate

// int32 color = 1;
inline void LicenePlate::clear_color() {
  _impl_.color_ = 0;
}
inline int32_t LicenePlate::_internal_color() const {
  return _impl_.color_;
}
inline int32_t LicenePlate::color() const {
  // @@protoc_insertion_point(field_get:perception.detect.LicenePlate.color)
  return _internal_color();
}
inline void LicenePlate::_internal_set_color(int32_t value) {
  
  _impl_.color_ = value;
}
inline void LicenePlate::set_color(int32_t value) {
  _internal_set_color(value);
  // @@protoc_insertion_point(field_set:perception.detect.LicenePlate.color)
}

// float color_confidence = 2;
inline void LicenePlate::clear_color_confidence() {
  _impl_.color_confidence_ = 0;
}
inline float LicenePlate::_internal_color_confidence() const {
  return _impl_.color_confidence_;
}
inline float LicenePlate::color_confidence() const {
  // @@protoc_insertion_point(field_get:perception.detect.LicenePlate.color_confidence)
  return _internal_color_confidence();
}
inline void LicenePlate::_internal_set_color_confidence(float value) {
  
  _impl_.color_confidence_ = value;
}
inline void LicenePlate::set_color_confidence(float value) {
  _internal_set_color_confidence(value);
  // @@protoc_insertion_point(field_set:perception.detect.LicenePlate.color_confidence)
}

// string number = 3;
inline void LicenePlate::clear_number() {
  _impl_.number_.ClearToEmpty();
}
inline const std::string& LicenePlate::number() const {
  // @@protoc_insertion_point(field_get:perception.detect.LicenePlate.number)
  return _internal_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LicenePlate::set_number(ArgT0&& arg0, ArgT... args) {
 
 _impl_.number_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.LicenePlate.number)
}
inline std::string* LicenePlate::mutable_number() {
  std::string* _s = _internal_mutable_number();
  // @@protoc_insertion_point(field_mutable:perception.detect.LicenePlate.number)
  return _s;
}
inline const std::string& LicenePlate::_internal_number() const {
  return _impl_.number_.Get();
}
inline void LicenePlate::_internal_set_number(const std::string& value) {
  
  _impl_.number_.Set(value, GetArenaForAllocation());
}
inline std::string* LicenePlate::_internal_mutable_number() {
  
  return _impl_.number_.Mutable(GetArenaForAllocation());
}
inline std::string* LicenePlate::release_number() {
  // @@protoc_insertion_point(field_release:perception.detect.LicenePlate.number)
  return _impl_.number_.Release();
}
inline void LicenePlate::set_allocated_number(std::string* number) {
  if (number != nullptr) {
    
  } else {
    
  }
  _impl_.number_.SetAllocated(number, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.number_.IsDefault()) {
    _impl_.number_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.LicenePlate.number)
}

// float number_confidence = 4;
inline void LicenePlate::clear_number_confidence() {
  _impl_.number_confidence_ = 0;
}
inline float LicenePlate::_internal_number_confidence() const {
  return _impl_.number_confidence_;
}
inline float LicenePlate::number_confidence() const {
  // @@protoc_insertion_point(field_get:perception.detect.LicenePlate.number_confidence)
  return _internal_number_confidence();
}
inline void LicenePlate::_internal_set_number_confidence(float value) {
  
  _impl_.number_confidence_ = value;
}
inline void LicenePlate::set_number_confidence(float value) {
  _internal_set_number_confidence(value);
  // @@protoc_insertion_point(field_set:perception.detect.LicenePlate.number_confidence)
}

// -------------------------------------------------------------------

// TrackConfidence

// uint32 color = 1;
inline void TrackConfidence::clear_color() {
  _impl_.color_ = 0u;
}
inline uint32_t TrackConfidence::_internal_color() const {
  return _impl_.color_;
}
inline uint32_t TrackConfidence::color() const {
  // @@protoc_insertion_point(field_get:perception.detect.TrackConfidence.color)
  return _internal_color();
}
inline void TrackConfidence::_internal_set_color(uint32_t value) {
  
  _impl_.color_ = value;
}
inline void TrackConfidence::set_color(uint32_t value) {
  _internal_set_color(value);
  // @@protoc_insertion_point(field_set:perception.detect.TrackConfidence.color)
}

// uint32 type = 2;
inline void TrackConfidence::clear_type() {
  _impl_.type_ = 0u;
}
inline uint32_t TrackConfidence::_internal_type() const {
  return _impl_.type_;
}
inline uint32_t TrackConfidence::type() const {
  // @@protoc_insertion_point(field_get:perception.detect.TrackConfidence.type)
  return _internal_type();
}
inline void TrackConfidence::_internal_set_type(uint32_t value) {
  
  _impl_.type_ = value;
}
inline void TrackConfidence::set_type(uint32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.detect.TrackConfidence.type)
}

// uint32 plate_number = 3;
inline void TrackConfidence::clear_plate_number() {
  _impl_.plate_number_ = 0u;
}
inline uint32_t TrackConfidence::_internal_plate_number() const {
  return _impl_.plate_number_;
}
inline uint32_t TrackConfidence::plate_number() const {
  // @@protoc_insertion_point(field_get:perception.detect.TrackConfidence.plate_number)
  return _internal_plate_number();
}
inline void TrackConfidence::_internal_set_plate_number(uint32_t value) {
  
  _impl_.plate_number_ = value;
}
inline void TrackConfidence::set_plate_number(uint32_t value) {
  _internal_set_plate_number(value);
  // @@protoc_insertion_point(field_set:perception.detect.TrackConfidence.plate_number)
}

// uint32 plate_color = 4;
inline void TrackConfidence::clear_plate_color() {
  _impl_.plate_color_ = 0u;
}
inline uint32_t TrackConfidence::_internal_plate_color() const {
  return _impl_.plate_color_;
}
inline uint32_t TrackConfidence::plate_color() const {
  // @@protoc_insertion_point(field_get:perception.detect.TrackConfidence.plate_color)
  return _internal_plate_color();
}
inline void TrackConfidence::_internal_set_plate_color(uint32_t value) {
  
  _impl_.plate_color_ = value;
}
inline void TrackConfidence::set_plate_color(uint32_t value) {
  _internal_set_plate_color(value);
  // @@protoc_insertion_point(field_set:perception.detect.TrackConfidence.plate_color)
}

// -------------------------------------------------------------------

// DetectedObject

// bytes overlap_name = 1;
inline void DetectedObject::clear_overlap_name() {
  _impl_.overlap_name_.ClearToEmpty();
}
inline const std::string& DetectedObject::overlap_name() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.overlap_name)
  return _internal_overlap_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObject::set_overlap_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.overlap_name_.SetBytes(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.overlap_name)
}
inline std::string* DetectedObject::mutable_overlap_name() {
  std::string* _s = _internal_mutable_overlap_name();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.overlap_name)
  return _s;
}
inline const std::string& DetectedObject::_internal_overlap_name() const {
  return _impl_.overlap_name_.Get();
}
inline void DetectedObject::_internal_set_overlap_name(const std::string& value) {
  
  _impl_.overlap_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObject::_internal_mutable_overlap_name() {
  
  return _impl_.overlap_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObject::release_overlap_name() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.overlap_name)
  return _impl_.overlap_name_.Release();
}
inline void DetectedObject::set_allocated_overlap_name(std::string* overlap_name) {
  if (overlap_name != nullptr) {
    
  } else {
    
  }
  _impl_.overlap_name_.SetAllocated(overlap_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.overlap_name_.IsDefault()) {
    _impl_.overlap_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.overlap_name)
}

// fixed64 uuid = 2;
inline void DetectedObject::clear_uuid() {
  _impl_.uuid_ = uint64_t{0u};
}
inline uint64_t DetectedObject::_internal_uuid() const {
  return _impl_.uuid_;
}
inline uint64_t DetectedObject::uuid() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.uuid)
  return _internal_uuid();
}
inline void DetectedObject::_internal_set_uuid(uint64_t value) {
  
  _impl_.uuid_ = value;
}
inline void DetectedObject::set_uuid(uint64_t value) {
  _internal_set_uuid(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.uuid)
}

// sint32 type = 3;
inline void DetectedObject::clear_type() {
  _impl_.type_ = 0;
}
inline int32_t DetectedObject::_internal_type() const {
  return _impl_.type_;
}
inline int32_t DetectedObject::type() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.type)
  return _internal_type();
}
inline void DetectedObject::_internal_set_type(int32_t value) {
  
  _impl_.type_ = value;
}
inline void DetectedObject::set_type(int32_t value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.type)
}

// float confidence = 4;
inline void DetectedObject::clear_confidence() {
  _impl_.confidence_ = 0;
}
inline float DetectedObject::_internal_confidence() const {
  return _impl_.confidence_;
}
inline float DetectedObject::confidence() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.confidence)
  return _internal_confidence();
}
inline void DetectedObject::_internal_set_confidence(float value) {
  
  _impl_.confidence_ = value;
}
inline void DetectedObject::set_confidence(float value) {
  _internal_set_confidence(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.confidence)
}

// .perception.detect.Vector3f position = 5;
inline bool DetectedObject::_internal_has_position() const {
  return this != internal_default_instance() && _impl_.position_ != nullptr;
}
inline bool DetectedObject::has_position() const {
  return _internal_has_position();
}
inline const ::perception::detect::Vector3f& DetectedObject::_internal_position() const {
  const ::perception::detect::Vector3f* p = _impl_.position_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Vector3f&>(
      ::perception::detect::_Vector3f_default_instance_);
}
inline const ::perception::detect::Vector3f& DetectedObject::position() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.position)
  return _internal_position();
}
inline void DetectedObject::unsafe_arena_set_allocated_position(
    ::perception::detect::Vector3f* position) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  _impl_.position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.position)
}
inline ::perception::detect::Vector3f* DetectedObject::release_position() {
  
  ::perception::detect::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.position)
  
  ::perception::detect::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::_internal_mutable_position() {
  
  if (_impl_.position_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Vector3f>(GetArenaForAllocation());
    _impl_.position_ = p;
  }
  return _impl_.position_;
}
inline ::perception::detect::Vector3f* DetectedObject::mutable_position() {
  ::perception::detect::Vector3f* _msg = _internal_mutable_position();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.position)
  return _msg;
}
inline void DetectedObject::set_allocated_position(::perception::detect::Vector3f* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position));
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.position_ = position;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.position)
}

// .perception.detect.Vector3f shape = 6;
inline bool DetectedObject::_internal_has_shape() const {
  return this != internal_default_instance() && _impl_.shape_ != nullptr;
}
inline bool DetectedObject::has_shape() const {
  return _internal_has_shape();
}
inline const ::perception::detect::Vector3f& DetectedObject::_internal_shape() const {
  const ::perception::detect::Vector3f* p = _impl_.shape_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Vector3f&>(
      ::perception::detect::_Vector3f_default_instance_);
}
inline const ::perception::detect::Vector3f& DetectedObject::shape() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.shape)
  return _internal_shape();
}
inline void DetectedObject::unsafe_arena_set_allocated_shape(
    ::perception::detect::Vector3f* shape) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  _impl_.shape_ = shape;
  if (shape) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.shape)
}
inline ::perception::detect::Vector3f* DetectedObject::release_shape() {
  
  ::perception::detect::Vector3f* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::unsafe_arena_release_shape() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.shape)
  
  ::perception::detect::Vector3f* temp = _impl_.shape_;
  _impl_.shape_ = nullptr;
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::_internal_mutable_shape() {
  
  if (_impl_.shape_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Vector3f>(GetArenaForAllocation());
    _impl_.shape_ = p;
  }
  return _impl_.shape_;
}
inline ::perception::detect::Vector3f* DetectedObject::mutable_shape() {
  ::perception::detect::Vector3f* _msg = _internal_mutable_shape();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.shape)
  return _msg;
}
inline void DetectedObject::set_allocated_shape(::perception::detect::Vector3f* shape) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_);
  }
  if (shape) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape));
    if (message_arena != submessage_arena) {
      shape = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_ = shape;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.shape)
}

// .perception.detect.Polygon hull = 7;
inline bool DetectedObject::_internal_has_hull() const {
  return this != internal_default_instance() && _impl_.hull_ != nullptr;
}
inline bool DetectedObject::has_hull() const {
  return _internal_has_hull();
}
inline const ::perception::detect::Polygon& DetectedObject::_internal_hull() const {
  const ::perception::detect::Polygon* p = _impl_.hull_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Polygon&>(
      ::perception::detect::_Polygon_default_instance_);
}
inline const ::perception::detect::Polygon& DetectedObject::hull() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.hull)
  return _internal_hull();
}
inline void DetectedObject::unsafe_arena_set_allocated_hull(
    ::perception::detect::Polygon* hull) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hull_);
  }
  _impl_.hull_ = hull;
  if (hull) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.hull)
}
inline ::perception::detect::Polygon* DetectedObject::release_hull() {
  
  ::perception::detect::Polygon* temp = _impl_.hull_;
  _impl_.hull_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Polygon* DetectedObject::unsafe_arena_release_hull() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.hull)
  
  ::perception::detect::Polygon* temp = _impl_.hull_;
  _impl_.hull_ = nullptr;
  return temp;
}
inline ::perception::detect::Polygon* DetectedObject::_internal_mutable_hull() {
  
  if (_impl_.hull_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Polygon>(GetArenaForAllocation());
    _impl_.hull_ = p;
  }
  return _impl_.hull_;
}
inline ::perception::detect::Polygon* DetectedObject::mutable_hull() {
  ::perception::detect::Polygon* _msg = _internal_mutable_hull();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.hull)
  return _msg;
}
inline void DetectedObject::set_allocated_hull(::perception::detect::Polygon* hull) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.hull_);
  }
  if (hull) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(hull));
    if (message_arena != submessage_arena) {
      hull = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, hull, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.hull_ = hull;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.hull)
}

// float orientation = 8;
inline void DetectedObject::clear_orientation() {
  _impl_.orientation_ = 0;
}
inline float DetectedObject::_internal_orientation() const {
  return _impl_.orientation_;
}
inline float DetectedObject::orientation() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.orientation)
  return _internal_orientation();
}
inline void DetectedObject::_internal_set_orientation(float value) {
  
  _impl_.orientation_ = value;
}
inline void DetectedObject::set_orientation(float value) {
  _internal_set_orientation(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.orientation)
}

// .perception.detect.Velocity velocity = 9;
inline bool DetectedObject::_internal_has_velocity() const {
  return this != internal_default_instance() && _impl_.velocity_ != nullptr;
}
inline bool DetectedObject::has_velocity() const {
  return _internal_has_velocity();
}
inline void DetectedObject::clear_velocity() {
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
}
inline const ::perception::detect::Velocity& DetectedObject::_internal_velocity() const {
  const ::perception::detect::Velocity* p = _impl_.velocity_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Velocity&>(
      ::perception::detect::_Velocity_default_instance_);
}
inline const ::perception::detect::Velocity& DetectedObject::velocity() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.velocity)
  return _internal_velocity();
}
inline void DetectedObject::unsafe_arena_set_allocated_velocity(
    ::perception::detect::Velocity* velocity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.velocity_);
  }
  _impl_.velocity_ = velocity;
  if (velocity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.velocity)
}
inline ::perception::detect::Velocity* DetectedObject::release_velocity() {
  
  ::perception::detect::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Velocity* DetectedObject::unsafe_arena_release_velocity() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.velocity)
  
  ::perception::detect::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
  return temp;
}
inline ::perception::detect::Velocity* DetectedObject::_internal_mutable_velocity() {
  
  if (_impl_.velocity_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Velocity>(GetArenaForAllocation());
    _impl_.velocity_ = p;
  }
  return _impl_.velocity_;
}
inline ::perception::detect::Velocity* DetectedObject::mutable_velocity() {
  ::perception::detect::Velocity* _msg = _internal_mutable_velocity();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.velocity)
  return _msg;
}
inline void DetectedObject::set_allocated_velocity(::perception::detect::Velocity* velocity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.velocity_;
  }
  if (velocity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(velocity);
    if (message_arena != submessage_arena) {
      velocity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, velocity, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.velocity_ = velocity;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.velocity)
}

// bool is_static = 10;
inline void DetectedObject::clear_is_static() {
  _impl_.is_static_ = false;
}
inline bool DetectedObject::_internal_is_static() const {
  return _impl_.is_static_;
}
inline bool DetectedObject::is_static() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.is_static)
  return _internal_is_static();
}
inline void DetectedObject::_internal_set_is_static(bool value) {
  
  _impl_.is_static_ = value;
}
inline void DetectedObject::set_is_static(bool value) {
  _internal_set_is_static(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.is_static)
}

// .perception.detect.Color color = 11;
inline bool DetectedObject::_internal_has_color() const {
  return this != internal_default_instance() && _impl_.color_ != nullptr;
}
inline bool DetectedObject::has_color() const {
  return _internal_has_color();
}
inline const ::perception::detect::Color& DetectedObject::_internal_color() const {
  const ::perception::detect::Color* p = _impl_.color_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Color&>(
      ::perception::detect::_Color_default_instance_);
}
inline const ::perception::detect::Color& DetectedObject::color() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.color)
  return _internal_color();
}
inline void DetectedObject::unsafe_arena_set_allocated_color(
    ::perception::detect::Color* color) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.color_);
  }
  _impl_.color_ = color;
  if (color) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.color)
}
inline ::perception::detect::Color* DetectedObject::release_color() {
  
  ::perception::detect::Color* temp = _impl_.color_;
  _impl_.color_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Color* DetectedObject::unsafe_arena_release_color() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.color)
  
  ::perception::detect::Color* temp = _impl_.color_;
  _impl_.color_ = nullptr;
  return temp;
}
inline ::perception::detect::Color* DetectedObject::_internal_mutable_color() {
  
  if (_impl_.color_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Color>(GetArenaForAllocation());
    _impl_.color_ = p;
  }
  return _impl_.color_;
}
inline ::perception::detect::Color* DetectedObject::mutable_color() {
  ::perception::detect::Color* _msg = _internal_mutable_color();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.color)
  return _msg;
}
inline void DetectedObject::set_allocated_color(::perception::detect::Color* color) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.color_);
  }
  if (color) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(color));
    if (message_arena != submessage_arena) {
      color = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, color, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.color_ = color;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.color)
}

// repeated float feature = 12;
inline int DetectedObject::_internal_feature_size() const {
  return _impl_.feature_.size();
}
inline int DetectedObject::feature_size() const {
  return _internal_feature_size();
}
inline void DetectedObject::clear_feature() {
  _impl_.feature_.Clear();
}
inline float DetectedObject::_internal_feature(int index) const {
  return _impl_.feature_.Get(index);
}
inline float DetectedObject::feature(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.feature)
  return _internal_feature(index);
}
inline void DetectedObject::set_feature(int index, float value) {
  _impl_.feature_.Set(index, value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.feature)
}
inline void DetectedObject::_internal_add_feature(float value) {
  _impl_.feature_.Add(value);
}
inline void DetectedObject::add_feature(float value) {
  _internal_add_feature(value);
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObject.feature)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DetectedObject::_internal_feature() const {
  return _impl_.feature_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
DetectedObject::feature() const {
  // @@protoc_insertion_point(field_list:perception.detect.DetectedObject.feature)
  return _internal_feature();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DetectedObject::_internal_mutable_feature() {
  return &_impl_.feature_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
DetectedObject::mutable_feature() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.DetectedObject.feature)
  return _internal_mutable_feature();
}

// repeated .perception.detect.WayPoints trajectories = 13;
inline int DetectedObject::_internal_trajectories_size() const {
  return _impl_.trajectories_.size();
}
inline int DetectedObject::trajectories_size() const {
  return _internal_trajectories_size();
}
inline void DetectedObject::clear_trajectories() {
  _impl_.trajectories_.Clear();
}
inline ::perception::detect::WayPoints* DetectedObject::mutable_trajectories(int index) {
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.trajectories)
  return _impl_.trajectories_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoints >*
DetectedObject::mutable_trajectories() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.DetectedObject.trajectories)
  return &_impl_.trajectories_;
}
inline const ::perception::detect::WayPoints& DetectedObject::_internal_trajectories(int index) const {
  return _impl_.trajectories_.Get(index);
}
inline const ::perception::detect::WayPoints& DetectedObject::trajectories(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.trajectories)
  return _internal_trajectories(index);
}
inline ::perception::detect::WayPoints* DetectedObject::_internal_add_trajectories() {
  return _impl_.trajectories_.Add();
}
inline ::perception::detect::WayPoints* DetectedObject::add_trajectories() {
  ::perception::detect::WayPoints* _add = _internal_add_trajectories();
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObject.trajectories)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoints >&
DetectedObject::trajectories() const {
  // @@protoc_insertion_point(field_list:perception.detect.DetectedObject.trajectories)
  return _impl_.trajectories_;
}

// repeated bytes str_array = 14;
inline int DetectedObject::_internal_str_array_size() const {
  return _impl_.str_array_.size();
}
inline int DetectedObject::str_array_size() const {
  return _internal_str_array_size();
}
inline void DetectedObject::clear_str_array() {
  _impl_.str_array_.Clear();
}
inline std::string* DetectedObject::add_str_array() {
  std::string* _s = _internal_add_str_array();
  // @@protoc_insertion_point(field_add_mutable:perception.detect.DetectedObject.str_array)
  return _s;
}
inline const std::string& DetectedObject::_internal_str_array(int index) const {
  return _impl_.str_array_.Get(index);
}
inline const std::string& DetectedObject::str_array(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.str_array)
  return _internal_str_array(index);
}
inline std::string* DetectedObject::mutable_str_array(int index) {
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.str_array)
  return _impl_.str_array_.Mutable(index);
}
inline void DetectedObject::set_str_array(int index, const std::string& value) {
  _impl_.str_array_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, std::string&& value) {
  _impl_.str_array_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.str_array_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::set_str_array(int index, const void* value, size_t size) {
  _impl_.str_array_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:perception.detect.DetectedObject.str_array)
}
inline std::string* DetectedObject::_internal_add_str_array() {
  return _impl_.str_array_.Add();
}
inline void DetectedObject::add_str_array(const std::string& value) {
  _impl_.str_array_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(std::string&& value) {
  _impl_.str_array_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  _impl_.str_array_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:perception.detect.DetectedObject.str_array)
}
inline void DetectedObject::add_str_array(const void* value, size_t size) {
  _impl_.str_array_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:perception.detect.DetectedObject.str_array)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
DetectedObject::str_array() const {
  // @@protoc_insertion_point(field_list:perception.detect.DetectedObject.str_array)
  return _impl_.str_array_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
DetectedObject::mutable_str_array() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.DetectedObject.str_array)
  return &_impl_.str_array_;
}

// repeated sint32 int_array = 15;
inline int DetectedObject::_internal_int_array_size() const {
  return _impl_.int_array_.size();
}
inline int DetectedObject::int_array_size() const {
  return _internal_int_array_size();
}
inline void DetectedObject::clear_int_array() {
  _impl_.int_array_.Clear();
}
inline int32_t DetectedObject::_internal_int_array(int index) const {
  return _impl_.int_array_.Get(index);
}
inline int32_t DetectedObject::int_array(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.int_array)
  return _internal_int_array(index);
}
inline void DetectedObject::set_int_array(int index, int32_t value) {
  _impl_.int_array_.Set(index, value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.int_array)
}
inline void DetectedObject::_internal_add_int_array(int32_t value) {
  _impl_.int_array_.Add(value);
}
inline void DetectedObject::add_int_array(int32_t value) {
  _internal_add_int_array(value);
  // @@protoc_insertion_point(field_add:perception.detect.DetectedObject.int_array)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
DetectedObject::_internal_int_array() const {
  return _impl_.int_array_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
DetectedObject::int_array() const {
  // @@protoc_insertion_point(field_list:perception.detect.DetectedObject.int_array)
  return _internal_int_array();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
DetectedObject::_internal_mutable_int_array() {
  return &_impl_.int_array_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
DetectedObject::mutable_int_array() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.DetectedObject.int_array)
  return _internal_mutable_int_array();
}

// int64 parking_time = 16;
inline void DetectedObject::clear_parking_time() {
  _impl_.parking_time_ = int64_t{0};
}
inline int64_t DetectedObject::_internal_parking_time() const {
  return _impl_.parking_time_;
}
inline int64_t DetectedObject::parking_time() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.parking_time)
  return _internal_parking_time();
}
inline void DetectedObject::_internal_set_parking_time(int64_t value) {
  
  _impl_.parking_time_ = value;
}
inline void DetectedObject::set_parking_time(int64_t value) {
  _internal_set_parking_time(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.parking_time)
}

// .perception.detect.LicenePlate plate = 17;
inline bool DetectedObject::_internal_has_plate() const {
  return this != internal_default_instance() && _impl_.plate_ != nullptr;
}
inline bool DetectedObject::has_plate() const {
  return _internal_has_plate();
}
inline void DetectedObject::clear_plate() {
  if (GetArenaForAllocation() == nullptr && _impl_.plate_ != nullptr) {
    delete _impl_.plate_;
  }
  _impl_.plate_ = nullptr;
}
inline const ::perception::detect::LicenePlate& DetectedObject::_internal_plate() const {
  const ::perception::detect::LicenePlate* p = _impl_.plate_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::LicenePlate&>(
      ::perception::detect::_LicenePlate_default_instance_);
}
inline const ::perception::detect::LicenePlate& DetectedObject::plate() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.plate)
  return _internal_plate();
}
inline void DetectedObject::unsafe_arena_set_allocated_plate(
    ::perception::detect::LicenePlate* plate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.plate_);
  }
  _impl_.plate_ = plate;
  if (plate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.plate)
}
inline ::perception::detect::LicenePlate* DetectedObject::release_plate() {
  
  ::perception::detect::LicenePlate* temp = _impl_.plate_;
  _impl_.plate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::LicenePlate* DetectedObject::unsafe_arena_release_plate() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.plate)
  
  ::perception::detect::LicenePlate* temp = _impl_.plate_;
  _impl_.plate_ = nullptr;
  return temp;
}
inline ::perception::detect::LicenePlate* DetectedObject::_internal_mutable_plate() {
  
  if (_impl_.plate_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::LicenePlate>(GetArenaForAllocation());
    _impl_.plate_ = p;
  }
  return _impl_.plate_;
}
inline ::perception::detect::LicenePlate* DetectedObject::mutable_plate() {
  ::perception::detect::LicenePlate* _msg = _internal_mutable_plate();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.plate)
  return _msg;
}
inline void DetectedObject::set_allocated_plate(::perception::detect::LicenePlate* plate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.plate_;
  }
  if (plate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(plate);
    if (message_arena != submessage_arena) {
      plate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plate, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.plate_ = plate;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.plate)
}

// int32 obj_color = 18;
inline void DetectedObject::clear_obj_color() {
  _impl_.obj_color_ = 0;
}
inline int32_t DetectedObject::_internal_obj_color() const {
  return _impl_.obj_color_;
}
inline int32_t DetectedObject::obj_color() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.obj_color)
  return _internal_obj_color();
}
inline void DetectedObject::_internal_set_obj_color(int32_t value) {
  
  _impl_.obj_color_ = value;
}
inline void DetectedObject::set_obj_color(int32_t value) {
  _internal_set_obj_color(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.obj_color)
}

// string group_id = 19;
inline void DetectedObject::clear_group_id() {
  _impl_.group_id_.ClearToEmpty();
}
inline const std::string& DetectedObject::group_id() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.group_id)
  return _internal_group_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObject::set_group_id(ArgT0&& arg0, ArgT... args) {
 
 _impl_.group_id_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.group_id)
}
inline std::string* DetectedObject::mutable_group_id() {
  std::string* _s = _internal_mutable_group_id();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.group_id)
  return _s;
}
inline const std::string& DetectedObject::_internal_group_id() const {
  return _impl_.group_id_.Get();
}
inline void DetectedObject::_internal_set_group_id(const std::string& value) {
  
  _impl_.group_id_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObject::_internal_mutable_group_id() {
  
  return _impl_.group_id_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObject::release_group_id() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.group_id)
  return _impl_.group_id_.Release();
}
inline void DetectedObject::set_allocated_group_id(std::string* group_id) {
  if (group_id != nullptr) {
    
  } else {
    
  }
  _impl_.group_id_.SetAllocated(group_id, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.group_id_.IsDefault()) {
    _impl_.group_id_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.group_id)
}

// float obj_color_conf = 20;
inline void DetectedObject::clear_obj_color_conf() {
  _impl_.obj_color_conf_ = 0;
}
inline float DetectedObject::_internal_obj_color_conf() const {
  return _impl_.obj_color_conf_;
}
inline float DetectedObject::obj_color_conf() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.obj_color_conf)
  return _internal_obj_color_conf();
}
inline void DetectedObject::_internal_set_obj_color_conf(float value) {
  
  _impl_.obj_color_conf_ = value;
}
inline void DetectedObject::set_obj_color_conf(float value) {
  _internal_set_obj_color_conf(value);
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.obj_color_conf)
}

// .perception.detect.TrackConfidence track_conf = 21;
inline bool DetectedObject::_internal_has_track_conf() const {
  return this != internal_default_instance() && _impl_.track_conf_ != nullptr;
}
inline bool DetectedObject::has_track_conf() const {
  return _internal_has_track_conf();
}
inline void DetectedObject::clear_track_conf() {
  if (GetArenaForAllocation() == nullptr && _impl_.track_conf_ != nullptr) {
    delete _impl_.track_conf_;
  }
  _impl_.track_conf_ = nullptr;
}
inline const ::perception::detect::TrackConfidence& DetectedObject::_internal_track_conf() const {
  const ::perception::detect::TrackConfidence* p = _impl_.track_conf_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::TrackConfidence&>(
      ::perception::detect::_TrackConfidence_default_instance_);
}
inline const ::perception::detect::TrackConfidence& DetectedObject::track_conf() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.track_conf)
  return _internal_track_conf();
}
inline void DetectedObject::unsafe_arena_set_allocated_track_conf(
    ::perception::detect::TrackConfidence* track_conf) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.track_conf_);
  }
  _impl_.track_conf_ = track_conf;
  if (track_conf) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.track_conf)
}
inline ::perception::detect::TrackConfidence* DetectedObject::release_track_conf() {
  
  ::perception::detect::TrackConfidence* temp = _impl_.track_conf_;
  _impl_.track_conf_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::TrackConfidence* DetectedObject::unsafe_arena_release_track_conf() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.track_conf)
  
  ::perception::detect::TrackConfidence* temp = _impl_.track_conf_;
  _impl_.track_conf_ = nullptr;
  return temp;
}
inline ::perception::detect::TrackConfidence* DetectedObject::_internal_mutable_track_conf() {
  
  if (_impl_.track_conf_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::TrackConfidence>(GetArenaForAllocation());
    _impl_.track_conf_ = p;
  }
  return _impl_.track_conf_;
}
inline ::perception::detect::TrackConfidence* DetectedObject::mutable_track_conf() {
  ::perception::detect::TrackConfidence* _msg = _internal_mutable_track_conf();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.track_conf)
  return _msg;
}
inline void DetectedObject::set_allocated_track_conf(::perception::detect::TrackConfidence* track_conf) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.track_conf_;
  }
  if (track_conf) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(track_conf);
    if (message_arena != submessage_arena) {
      track_conf = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, track_conf, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.track_conf_ = track_conf;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.track_conf)
}

// string camera_name = 22;
inline void DetectedObject::clear_camera_name() {
  _impl_.camera_name_.ClearToEmpty();
}
inline const std::string& DetectedObject::camera_name() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.camera_name)
  return _internal_camera_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DetectedObject::set_camera_name(ArgT0&& arg0, ArgT... args) {
 
 _impl_.camera_name_.Set(static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:perception.detect.DetectedObject.camera_name)
}
inline std::string* DetectedObject::mutable_camera_name() {
  std::string* _s = _internal_mutable_camera_name();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.camera_name)
  return _s;
}
inline const std::string& DetectedObject::_internal_camera_name() const {
  return _impl_.camera_name_.Get();
}
inline void DetectedObject::_internal_set_camera_name(const std::string& value) {
  
  _impl_.camera_name_.Set(value, GetArenaForAllocation());
}
inline std::string* DetectedObject::_internal_mutable_camera_name() {
  
  return _impl_.camera_name_.Mutable(GetArenaForAllocation());
}
inline std::string* DetectedObject::release_camera_name() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.camera_name)
  return _impl_.camera_name_.Release();
}
inline void DetectedObject::set_allocated_camera_name(std::string* camera_name) {
  if (camera_name != nullptr) {
    
  } else {
    
  }
  _impl_.camera_name_.SetAllocated(camera_name, GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (_impl_.camera_name_.IsDefault()) {
    _impl_.camera_name_.Set("", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.camera_name)
}

// .perception.detect.Vector3f position_image = 23;
inline bool DetectedObject::_internal_has_position_image() const {
  return this != internal_default_instance() && _impl_.position_image_ != nullptr;
}
inline bool DetectedObject::has_position_image() const {
  return _internal_has_position_image();
}
inline const ::perception::detect::Vector3f& DetectedObject::_internal_position_image() const {
  const ::perception::detect::Vector3f* p = _impl_.position_image_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Vector3f&>(
      ::perception::detect::_Vector3f_default_instance_);
}
inline const ::perception::detect::Vector3f& DetectedObject::position_image() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.position_image)
  return _internal_position_image();
}
inline void DetectedObject::unsafe_arena_set_allocated_position_image(
    ::perception::detect::Vector3f* position_image) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_image_);
  }
  _impl_.position_image_ = position_image;
  if (position_image) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.position_image)
}
inline ::perception::detect::Vector3f* DetectedObject::release_position_image() {
  
  ::perception::detect::Vector3f* temp = _impl_.position_image_;
  _impl_.position_image_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::unsafe_arena_release_position_image() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.position_image)
  
  ::perception::detect::Vector3f* temp = _impl_.position_image_;
  _impl_.position_image_ = nullptr;
  return temp;
}
inline ::perception::detect::Vector3f* DetectedObject::_internal_mutable_position_image() {
  
  if (_impl_.position_image_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Vector3f>(GetArenaForAllocation());
    _impl_.position_image_ = p;
  }
  return _impl_.position_image_;
}
inline ::perception::detect::Vector3f* DetectedObject::mutable_position_image() {
  ::perception::detect::Vector3f* _msg = _internal_mutable_position_image();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.position_image)
  return _msg;
}
inline void DetectedObject::set_allocated_position_image(::perception::detect::Vector3f* position_image) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_image_);
  }
  if (position_image) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position_image));
    if (message_arena != submessage_arena) {
      position_image = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position_image, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.position_image_ = position_image;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.position_image)
}

// .perception.detect.Vector2f shape_image = 24;
inline bool DetectedObject::_internal_has_shape_image() const {
  return this != internal_default_instance() && _impl_.shape_image_ != nullptr;
}
inline bool DetectedObject::has_shape_image() const {
  return _internal_has_shape_image();
}
inline const ::perception::detect::Vector2f& DetectedObject::_internal_shape_image() const {
  const ::perception::detect::Vector2f* p = _impl_.shape_image_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Vector2f&>(
      ::perception::detect::_Vector2f_default_instance_);
}
inline const ::perception::detect::Vector2f& DetectedObject::shape_image() const {
  // @@protoc_insertion_point(field_get:perception.detect.DetectedObject.shape_image)
  return _internal_shape_image();
}
inline void DetectedObject::unsafe_arena_set_allocated_shape_image(
    ::perception::detect::Vector2f* shape_image) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_image_);
  }
  _impl_.shape_image_ = shape_image;
  if (shape_image) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.DetectedObject.shape_image)
}
inline ::perception::detect::Vector2f* DetectedObject::release_shape_image() {
  
  ::perception::detect::Vector2f* temp = _impl_.shape_image_;
  _impl_.shape_image_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Vector2f* DetectedObject::unsafe_arena_release_shape_image() {
  // @@protoc_insertion_point(field_release:perception.detect.DetectedObject.shape_image)
  
  ::perception::detect::Vector2f* temp = _impl_.shape_image_;
  _impl_.shape_image_ = nullptr;
  return temp;
}
inline ::perception::detect::Vector2f* DetectedObject::_internal_mutable_shape_image() {
  
  if (_impl_.shape_image_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Vector2f>(GetArenaForAllocation());
    _impl_.shape_image_ = p;
  }
  return _impl_.shape_image_;
}
inline ::perception::detect::Vector2f* DetectedObject::mutable_shape_image() {
  ::perception::detect::Vector2f* _msg = _internal_mutable_shape_image();
  // @@protoc_insertion_point(field_mutable:perception.detect.DetectedObject.shape_image)
  return _msg;
}
inline void DetectedObject::set_allocated_shape_image(::perception::detect::Vector2f* shape_image) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.shape_image_);
  }
  if (shape_image) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(shape_image));
    if (message_arena != submessage_arena) {
      shape_image = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, shape_image, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.shape_image_ = shape_image;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.DetectedObject.shape_image)
}

// -------------------------------------------------------------------

// Velocity

// float heading = 1;
inline void Velocity::clear_heading() {
  _impl_.heading_ = 0;
}
inline float Velocity::_internal_heading() const {
  return _impl_.heading_;
}
inline float Velocity::heading() const {
  // @@protoc_insertion_point(field_get:perception.detect.Velocity.heading)
  return _internal_heading();
}
inline void Velocity::_internal_set_heading(float value) {
  
  _impl_.heading_ = value;
}
inline void Velocity::set_heading(float value) {
  _internal_set_heading(value);
  // @@protoc_insertion_point(field_set:perception.detect.Velocity.heading)
}

// float speed = 2;
inline void Velocity::clear_speed() {
  _impl_.speed_ = 0;
}
inline float Velocity::_internal_speed() const {
  return _impl_.speed_;
}
inline float Velocity::speed() const {
  // @@protoc_insertion_point(field_get:perception.detect.Velocity.speed)
  return _internal_speed();
}
inline void Velocity::_internal_set_speed(float value) {
  
  _impl_.speed_ = value;
}
inline void Velocity::set_speed(float value) {
  _internal_set_speed(value);
  // @@protoc_insertion_point(field_set:perception.detect.Velocity.speed)
}

// float acceleration = 3;
inline void Velocity::clear_acceleration() {
  _impl_.acceleration_ = 0;
}
inline float Velocity::_internal_acceleration() const {
  return _impl_.acceleration_;
}
inline float Velocity::acceleration() const {
  // @@protoc_insertion_point(field_get:perception.detect.Velocity.acceleration)
  return _internal_acceleration();
}
inline void Velocity::_internal_set_acceleration(float value) {
  
  _impl_.acceleration_ = value;
}
inline void Velocity::set_acceleration(float value) {
  _internal_set_acceleration(value);
  // @@protoc_insertion_point(field_set:perception.detect.Velocity.acceleration)
}

// -------------------------------------------------------------------

// WayPoints

// repeated .perception.detect.WayPoint waypoints = 1;
inline int WayPoints::_internal_waypoints_size() const {
  return _impl_.waypoints_.size();
}
inline int WayPoints::waypoints_size() const {
  return _internal_waypoints_size();
}
inline void WayPoints::clear_waypoints() {
  _impl_.waypoints_.Clear();
}
inline ::perception::detect::WayPoint* WayPoints::mutable_waypoints(int index) {
  // @@protoc_insertion_point(field_mutable:perception.detect.WayPoints.waypoints)
  return _impl_.waypoints_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoint >*
WayPoints::mutable_waypoints() {
  // @@protoc_insertion_point(field_mutable_list:perception.detect.WayPoints.waypoints)
  return &_impl_.waypoints_;
}
inline const ::perception::detect::WayPoint& WayPoints::_internal_waypoints(int index) const {
  return _impl_.waypoints_.Get(index);
}
inline const ::perception::detect::WayPoint& WayPoints::waypoints(int index) const {
  // @@protoc_insertion_point(field_get:perception.detect.WayPoints.waypoints)
  return _internal_waypoints(index);
}
inline ::perception::detect::WayPoint* WayPoints::_internal_add_waypoints() {
  return _impl_.waypoints_.Add();
}
inline ::perception::detect::WayPoint* WayPoints::add_waypoints() {
  ::perception::detect::WayPoint* _add = _internal_add_waypoints();
  // @@protoc_insertion_point(field_add:perception.detect.WayPoints.waypoints)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::perception::detect::WayPoint >&
WayPoints::waypoints() const {
  // @@protoc_insertion_point(field_list:perception.detect.WayPoints.waypoints)
  return _impl_.waypoints_;
}

// float probability = 2;
inline void WayPoints::clear_probability() {
  _impl_.probability_ = 0;
}
inline float WayPoints::_internal_probability() const {
  return _impl_.probability_;
}
inline float WayPoints::probability() const {
  // @@protoc_insertion_point(field_get:perception.detect.WayPoints.probability)
  return _internal_probability();
}
inline void WayPoints::_internal_set_probability(float value) {
  
  _impl_.probability_ = value;
}
inline void WayPoints::set_probability(float value) {
  _internal_set_probability(value);
  // @@protoc_insertion_point(field_set:perception.detect.WayPoints.probability)
}

// -------------------------------------------------------------------

// WayPoint

// sfixed64 time_meas = 1;
inline void WayPoint::clear_time_meas() {
  _impl_.time_meas_ = int64_t{0};
}
inline int64_t WayPoint::_internal_time_meas() const {
  return _impl_.time_meas_;
}
inline int64_t WayPoint::time_meas() const {
  // @@protoc_insertion_point(field_get:perception.detect.WayPoint.time_meas)
  return _internal_time_meas();
}
inline void WayPoint::_internal_set_time_meas(int64_t value) {
  
  _impl_.time_meas_ = value;
}
inline void WayPoint::set_time_meas(int64_t value) {
  _internal_set_time_meas(value);
  // @@protoc_insertion_point(field_set:perception.detect.WayPoint.time_meas)
}

// .perception.detect.Vector3f position = 2;
inline bool WayPoint::_internal_has_position() const {
  return this != internal_default_instance() && _impl_.position_ != nullptr;
}
inline bool WayPoint::has_position() const {
  return _internal_has_position();
}
inline const ::perception::detect::Vector3f& WayPoint::_internal_position() const {
  const ::perception::detect::Vector3f* p = _impl_.position_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Vector3f&>(
      ::perception::detect::_Vector3f_default_instance_);
}
inline const ::perception::detect::Vector3f& WayPoint::position() const {
  // @@protoc_insertion_point(field_get:perception.detect.WayPoint.position)
  return _internal_position();
}
inline void WayPoint::unsafe_arena_set_allocated_position(
    ::perception::detect::Vector3f* position) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  _impl_.position_ = position;
  if (position) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.WayPoint.position)
}
inline ::perception::detect::Vector3f* WayPoint::release_position() {
  
  ::perception::detect::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Vector3f* WayPoint::unsafe_arena_release_position() {
  // @@protoc_insertion_point(field_release:perception.detect.WayPoint.position)
  
  ::perception::detect::Vector3f* temp = _impl_.position_;
  _impl_.position_ = nullptr;
  return temp;
}
inline ::perception::detect::Vector3f* WayPoint::_internal_mutable_position() {
  
  if (_impl_.position_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Vector3f>(GetArenaForAllocation());
    _impl_.position_ = p;
  }
  return _impl_.position_;
}
inline ::perception::detect::Vector3f* WayPoint::mutable_position() {
  ::perception::detect::Vector3f* _msg = _internal_mutable_position();
  // @@protoc_insertion_point(field_mutable:perception.detect.WayPoint.position)
  return _msg;
}
inline void WayPoint::set_allocated_position(::perception::detect::Vector3f* position) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.position_);
  }
  if (position) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(position));
    if (message_arena != submessage_arena) {
      position = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, position, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.position_ = position;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.WayPoint.position)
}

// .perception.detect.Velocity velocity = 3;
inline bool WayPoint::_internal_has_velocity() const {
  return this != internal_default_instance() && _impl_.velocity_ != nullptr;
}
inline bool WayPoint::has_velocity() const {
  return _internal_has_velocity();
}
inline void WayPoint::clear_velocity() {
  if (GetArenaForAllocation() == nullptr && _impl_.velocity_ != nullptr) {
    delete _impl_.velocity_;
  }
  _impl_.velocity_ = nullptr;
}
inline const ::perception::detect::Velocity& WayPoint::_internal_velocity() const {
  const ::perception::detect::Velocity* p = _impl_.velocity_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::detect::Velocity&>(
      ::perception::detect::_Velocity_default_instance_);
}
inline const ::perception::detect::Velocity& WayPoint::velocity() const {
  // @@protoc_insertion_point(field_get:perception.detect.WayPoint.velocity)
  return _internal_velocity();
}
inline void WayPoint::unsafe_arena_set_allocated_velocity(
    ::perception::detect::Velocity* velocity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.velocity_);
  }
  _impl_.velocity_ = velocity;
  if (velocity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.detect.WayPoint.velocity)
}
inline ::perception::detect::Velocity* WayPoint::release_velocity() {
  
  ::perception::detect::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::detect::Velocity* WayPoint::unsafe_arena_release_velocity() {
  // @@protoc_insertion_point(field_release:perception.detect.WayPoint.velocity)
  
  ::perception::detect::Velocity* temp = _impl_.velocity_;
  _impl_.velocity_ = nullptr;
  return temp;
}
inline ::perception::detect::Velocity* WayPoint::_internal_mutable_velocity() {
  
  if (_impl_.velocity_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::detect::Velocity>(GetArenaForAllocation());
    _impl_.velocity_ = p;
  }
  return _impl_.velocity_;
}
inline ::perception::detect::Velocity* WayPoint::mutable_velocity() {
  ::perception::detect::Velocity* _msg = _internal_mutable_velocity();
  // @@protoc_insertion_point(field_mutable:perception.detect.WayPoint.velocity)
  return _msg;
}
inline void WayPoint::set_allocated_velocity(::perception::detect::Velocity* velocity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.velocity_;
  }
  if (velocity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(velocity);
    if (message_arena != submessage_arena) {
      velocity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, velocity, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.velocity_ = velocity;
  // @@protoc_insertion_point(field_set_allocated:perception.detect.WayPoint.velocity)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace detect
}  // namespace perception

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::perception::detect::ObjectType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::perception::detect::ObjectType>() {
  return ::perception::detect::ObjectType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_esurfing_2fperc_2fgroup_5fdetected_2eproto
