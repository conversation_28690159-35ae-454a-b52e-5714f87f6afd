#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <deque>
#include <mutex>
#include <algorithm>
#include <nlohmann/json.hpp>

#include "rclcpp/rclcpp.hpp"
#include "rclcpp_components/register_node_macro.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "cv_bridge/cv_bridge.h"

#include "trt_worker.hpp"
#include "trt_logger.hpp"
#include "trt_model.hpp"
#include "esurfing/perc/group_detected.pb.h"
#include "esurfing/math/geo.pb.h"

using namespace std::chrono_literals;
using perception::detect::DetectedObject;
using perception::detect::DetectedObjects;
using perception::detect::Vector3f;
using perception::detect::Vector2f;
using json = nlohmann::json;

namespace Det2dWorker {

// 定义消息类型别名
using ImageMsg = sensor_msgs::msg::Image;
using UInt8MultiArrayMsg = std_msgs::msg::UInt8MultiArray;

// 定义骑行者检测结果结构
struct CyclistDetection {
    int32_t type;                // 对象类型
    float confidence;            // 置信度
    float x1, y1, x2, y2;        // 边界框坐标
    std::string camera_name;     // 相机名称
    int64_t timestamp;           // 时间戳
};

// 定义头盔分类结果结构
struct HelmetClassification {
    int cls;                     // 分类结果: 0-有头盔, 1-无头盔
    float confidence;            // 置信度
    std::string sensorName;      // 相机名称
    int64_t timestamp;           // 时间戳
    float x1, y1, x2, y2;        // 原始边界框坐标
};

// 定义图像数据结构，包含时间戳
struct ImageData {
    std::shared_ptr<ImageMsg> msg;  // 原始消息
    cv::Mat image;                  // 图像数据
    int64_t timestamp;              // 时间戳（微秒）
};

class HelmetClsNode : public rclcpp::Node {
public:

    explicit HelmetClsNode(const rclcpp::NodeOptions& options)
    : Node("helmet_cls_node", options)
    {
        // 声明参数
        this->declare_parameter<std::vector<std::string>>("image_topics",
            {"/camera/image1", "/camera/image2", "/camera/image3", "/camera/image4"});
        this->declare_parameter<std::vector<std::string>>("vehicle_det_topics",
            {"/vehicle/det1", "/vehicle/det2", "/vehicle/det3", "/vehicle/det4"});
        this->declare_parameter<std::string>("output_topic", "/helmet/cls");
        this->declare_parameter<std::string>("model_path", "src/dev_pkg_det2d/models/onnx/helmet_cls_batch4.onnx");
        this->declare_parameter<int>("max_queue_size", 10);
        this->declare_parameter<float>("inference_rate", 10.0);  // Hz
        this->declare_parameter<float>("confidence_threshold", 0.5);
        this->declare_parameter<int>("max_batch_size", 8);
        this->declare_parameter<std::vector<std::string>>("sensor_names",
            {"camera1", "camera2", "camera3", "camera4"});
        this->declare_parameter<int>("min_roi_width", 100);
        this->declare_parameter<int>("min_roi_height", 100);
        this->declare_parameter<int>("log_level", 2);  // 默认为WARN级别
        this->declare_parameter<int>("test_mode", 1);  // 0: 实验室环境，1: 路侧环境

        // 获取参数
        this->get_parameter("image_topics", image_topics_);
        this->get_parameter("vehicle_det_topics", vehicle_det_topics_);
        this->get_parameter("output_topic", output_topic_);
        this->get_parameter("model_path", model_path_);
        this->get_parameter("max_queue_size", max_queue_size_);
        this->get_parameter("inference_rate", inference_rate_);
        this->get_parameter("confidence_threshold", confidence_threshold_);
        this->get_parameter("max_batch_size", max_batch_size_);
        this->get_parameter("sensor_names", sensor_names_);
        this->get_parameter("min_roi_width", min_roi_width_);
        this->get_parameter("min_roi_height", min_roi_height_);
        this->get_parameter("log_level", log_level_);
        this->get_parameter("test_mode", test_mode_);

        // 限制输入topic数量
        if (image_topics_.size() > 8) {
            RCLCPP_WARN(this->get_logger(), "Too many image topics (>8), using only the first 8");
            image_topics_.resize(8);
        } else if (image_topics_.size() < 1) {
            RCLCPP_ERROR(this->get_logger(), "No image topics specified");
            return;
        }

        // 确保vehicle_det_topics数量与image_topics一致
        if (vehicle_det_topics_.size() != image_topics_.size()) {
            RCLCPP_WARN(this->get_logger(),
                "Number of vehicle detection topics (%zu) doesn't match image topics (%zu), adjusting...",
                vehicle_det_topics_.size(), image_topics_.size());

            // 如果vehicle_det_topics太少，添加默认topic
            while (vehicle_det_topics_.size() < image_topics_.size()) {
                std::string new_topic = "/vehicle/det" + std::to_string(vehicle_det_topics_.size() + 1);
                vehicle_det_topics_.push_back(new_topic);
            }

            // 如果vehicle_det_topics太多，截断
            if (vehicle_det_topics_.size() > image_topics_.size()) {
                vehicle_det_topics_.resize(image_topics_.size());
            }
        }

        // 确保sensor_names数量与输入一致
        if (sensor_names_.size() != image_topics_.size()) {
            RCLCPP_WARN(this->get_logger(),
                "Number of sensor names (%zu) doesn't match image topics (%zu), adjusting...",
                sensor_names_.size(), image_topics_.size());

            // 如果sensor_names太少，添加默认名称
            while (sensor_names_.size() < image_topics_.size()) {
                std::string new_sensor = "camera" + std::to_string(sensor_names_.size() + 1);
                sensor_names_.push_back(new_sensor);
            }

            // 如果sensor_names太多，截断
            if (sensor_names_.size() > image_topics_.size()) {
                sensor_names_.resize(image_topics_.size());
            }
        }

        // 创建topic和sensor_name的映射关系
        for (size_t i = 0; i < image_topics_.size(); ++i) {
            topic_to_sensor_map_[image_topics_[i]] = sensor_names_[i];
            RCLCPP_INFO(this->get_logger(), "Mapping topic '%s' to sensor name '%s'",
                        image_topics_[i].c_str(), sensor_names_[i].c_str());
        }

        // 初始化TRT Worker
        init_trt_worker();

        // 创建图像订阅者
        for (size_t i = 0; i < image_topics_.size(); ++i) {
            const auto& topic = image_topics_[i];
            auto callback = [this, i, topic](const ImageMsg::SharedPtr msg) {
                this->image_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<ImageMsg>(
                topic, 10, callback);

            image_subscribers_.push_back(sub);
            image_queues_[i] = std::deque<ImageData>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic.c_str());
        }

        // 创建车辆检测结果订阅者
        for (size_t i = 0; i < vehicle_det_topics_.size(); ++i) {
            const auto& topic = vehicle_det_topics_[i];
            auto callback = [this, i, topic](const UInt8MultiArrayMsg::SharedPtr msg) {
                this->vehicle_detection_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<UInt8MultiArrayMsg>(
                topic, 10, callback);

            vehicle_det_subscribers_.push_back(sub);
            detection_queues_[i] = std::deque<std::pair<UInt8MultiArrayMsg::SharedPtr, std::vector<CyclistDetection>>>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to vehicle detection topic: %s", topic.c_str());
        }

        // 创建头盔分类结果发布者
        helmet_cls_publisher_ = this->create_publisher<std_msgs::msg::String>(output_topic_, 10);
        RCLCPP_INFO(this->get_logger(), "Created publisher for topic: %s", output_topic_.c_str());

        // 创建推理定时器
        inference_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / inference_rate_)),
            std::bind(&HelmetClsNode::inference_callback, this));

        RCLCPP_INFO(this->get_logger(), "Helmet classification node initialized with %zu cameras", image_topics_.size());
    }

    ~HelmetClsNode() {
        // 清理资源
        if (inference_timer_) {
            inference_timer_->cancel();
        }
    }

private:
    void init_trt_worker() {
        // 模型配置
        std::string onnxPath = model_path_;
        // auto level = logger::Level::WARN; //FATAL;
        auto level = static_cast<logger::Level>(log_level_);
        auto params = model::Params();

        // 通用参数 - 参考helmet_cls_img_buffer_test.cpp
        params.batch_size = 4;  // 批处理大小
        params.img = {384, 384, 3};
        params.task = model::task_type::HELMET_CLASSIFICATION;
        params.dev = model::device::CPU;
        params.prec = model::precision::FP16;
        params.calib_data_file = "src/dev_pkg_det2d/calibration/calibration_list_helmet_cls.txt";
        params.calib_table_file = "src/dev_pkg_det2d/calibration/calibration_helmet_cls_batch4_gpu_table_minmax.txt";

        // DLA参数
        params.use_dla = false;
        params.dla_core = 0;

        // 创建TRT Worker
        worker_ = thread::create_worker(onnxPath, level, params);

        if (!worker_) {
            RCLCPP_ERROR(this->get_logger(), "Failed to create TRT worker");
        } else {
            RCLCPP_INFO(this->get_logger(), "TRT worker created successfully");
        }
    }

    void image_callback(const ImageMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            if (msg->encoding != "bgr8" && msg->encoding != "rgb8") {
                RCLCPP_ERROR(this->get_logger(), "Unsupported encoding: %s",
                                msg->encoding.c_str());
                return;
            }
            cv::Mat image(msg->height, msg->width, CV_8UC3,
                const_cast<uint8_t*>(msg->data.data()), msg->step);
            if (image.empty()) {
                RCLCPP_ERROR(this->get_logger(),
                                "Received empty image for camera: %d",
                                camera_idx);
                return;
            }
            if (msg->encoding == "rgb8") {
                cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
            }

            // 获取图像时间戳（转换为微秒）
            int64_t timestamp_us = 0;
            if (test_mode_ == 1) {
                // 路侧环境
                timestamp_us = rclcpp::Time(msg->header.stamp).nanoseconds() / 1000;
            } else if (test_mode_ == 0) {
                // 实验室环境
                timestamp_us = std::chrono::duration_cast<std::chrono::microseconds>(
                    std::chrono::system_clock::now().time_since_epoch()).count();
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                auto& queue = image_queues_[camera_idx];
                // 添加到对应相机的队列，包含时间戳信息
                ImageData image_data;
                image_data.msg = msg;
                image_data.image = image.clone();
                image_data.timestamp = timestamp_us;
                queue.push_back(image_data);
                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received image from %s, timestamp: %ld, queue size: %zu",
                        topic.c_str(), timestamp_us, image_queues_[camera_idx].size());
        }
        catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "CV bridge exception: %s", e.what());
        }
    }

    void vehicle_detection_callback(const UInt8MultiArrayMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            // 解析protobuf消息
            DetectedObjects detected_objects;
            if (!detected_objects.ParseFromArray(msg->data.data(), msg->data.size())) {
                RCLCPP_ERROR(this->get_logger(), "Failed to parse detection results from %s", topic.c_str());
                return;
            }

            // 提取骑行者检测结果
            std::vector<CyclistDetection> cyclist_detections;
            int64_t timestamp = detected_objects.time_meas();
            std::string sensor_name = sensor_names_[camera_idx];

            for (int i = 0; i < detected_objects.objects_size(); ++i) {
                const DetectedObject& obj = detected_objects.objects(i);

                // 只处理CYCLIST类型且置信度大于阈值的检测结果
                if (obj.type() == perception::detect::CYCLIST && obj.confidence() > confidence_threshold_) {
                    CyclistDetection detection;
                    detection.type = obj.type();
                    detection.confidence = obj.confidence();

                    // 获取边界框坐标
                    const Vector3f& pos = obj.position();
                    const Vector3f& shape = obj.shape();
                    float center_x = pos.x();
                    float center_y = pos.y();
                    float width = shape.x();
                    float height = shape.y();

                    // 计算边界框坐标
                    detection.x1 = center_x - width / 2.0f;
                    detection.y1 = center_y - height / 2.0f;
                    detection.x2 = center_x + width / 2.0f;
                    detection.y2 = center_y + height / 2.0f;

                    detection.camera_name = sensor_name;
                    detection.timestamp = timestamp;

                    cyclist_detections.push_back(detection);
                }
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);

                // 添加到对应相机的检测队列
                auto& queue = detection_queues_[camera_idx];
                queue.push_back(std::make_pair(msg, cyclist_detections));

                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received %zu cyclist detections from %s",
                        cyclist_detections.size(), topic.c_str());
        }
        catch (std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Exception in vehicle detection callback: %s", e.what());
        }
    }

    void inference_callback() {
        /* Step 0: Init Check */
        // 检查TRT worker是否已初始化
        if (!worker_) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                                "TRT worker not initialized");
            return;
        }
        // 检查前后两次进入inference_callback的时间戳
        static rclcpp::Time last_callback_time = this->now();
        rclcpp::Time current_time = this->now();
        // 计算时间差（单位：毫秒）
        double time_diff = (current_time - last_callback_time).seconds() * 1000;
        // 如果时间差大于100ms，打印警告
        if (time_diff > 102.0) {
            RCLCPP_DEBUG(this->get_logger(),
                        "Long delay between start of inference callbacks: %.2f ms",
                        time_diff);
        }
        // 更新上次调用时间
        last_callback_time = current_time;

        /* Step 1: Data Load */
        std::chrono::steady_clock::time_point begin_data_load = std::chrono::steady_clock::now();

        // 准备处理的检测结果和对应图像
        std::vector<CyclistDetection> cyclist_detections;
        std::map<size_t, std::map<int64_t, ImageData>> camera_images; // 相机ID -> (时间戳 -> 图像数据)

        // 加锁访问队列
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);

            // 获取每个相机的所有图像，并按时间戳索引
            for (size_t i = 0; i < image_topics_.size(); ++i) {
                auto& queue = image_queues_[i];
                for (const auto& image_data : queue) {
                    camera_images[i][image_data.timestamp] = image_data;
                }
            }

            // 获取所有相机的骑行者检测结果
            for (size_t i = 0; i < vehicle_det_topics_.size(); ++i) {
                auto& queue = detection_queues_[i];
                if (!queue.empty()) {
                    // 获取最新的检测结果
                    auto& detections = queue.back().second;
                    for (const auto& det : detections) {
                        cyclist_detections.push_back(det);
                    }
                    queue.pop_front();
                }
            }
        }

        // 如果没有检测结果或图像，直接返回
        if (cyclist_detections.empty() || camera_images.empty()) {
            return;
        }

        // RCLCPP_INFO(this->get_logger(), "Processing %zu cyclist detections", cyclist_detections.size());

        /* Step 2: Crop Images */
        std::vector<cv::Mat> cropped_images;
        std::vector<HelmetClassification> helmet_results;

        for (const auto& detection : cyclist_detections) {
            // 查找对应相机的图像
            size_t camera_idx = 0;
            bool found = false;

            for (size_t i = 0; i < sensor_names_.size(); ++i) {
                if (sensor_names_[i] == detection.camera_name) {
                    camera_idx = i;
                    found = true;
                    break;
                }
            }

            if (!found || camera_images.find(camera_idx) == camera_images.end()) {
                RCLCPP_WARN(this->get_logger(), "Camera %zu not found for detection", camera_idx);
                continue;
            }

            // 查找与检测结果时间戳匹配的图像
            // 首先尝试精确匹配
            auto& camera_image_map = camera_images[camera_idx];
            auto it = camera_image_map.find(detection.timestamp);

            // 如果没有精确匹配，找最接近的时间戳
            if (it == camera_image_map.end()) {
                int64_t min_diff = INT64_MAX;
                int64_t closest_ts = 0;

                for (const auto& [ts, _] : camera_image_map) {
                    int64_t diff = std::abs(ts - detection.timestamp);
                    if (diff < min_diff) {
                        min_diff = diff;
                        closest_ts = ts;
                    }
                }

                // 如果时间差太大（超过102ms），则跳过
                if (min_diff > 102000) {
                    RCLCPP_WARN(this->get_logger(),
                               "No matching image found for detection timestamp %ld (closest diff: %ld us)",
                               detection.timestamp, min_diff);
                    continue;
                }

                it = camera_image_map.find(closest_ts);
                if (it == camera_image_map.end()) {
                    continue;
                }

                RCLCPP_DEBUG(this->get_logger(),
                           "Using image with timestamp %ld for detection timestamp %ld (diff: %ld us)",
                           closest_ts, detection.timestamp, min_diff);
            }

            // 获取图像
            const cv::Mat& image = it->second.image;

            // 计算扩展5%后的边界框
            float width = detection.x2 - detection.x1;
            float height = detection.y2 - detection.y1;
            float expand_x = width * 0.05f;
            float expand_y = height * 0.05f;

            int x1 = std::max(0, static_cast<int>(detection.x1 - expand_x));
            int y1 = std::max(0, static_cast<int>(detection.y1 - expand_y));
            int x2 = std::min(image.cols - 1, static_cast<int>(detection.x2 + expand_x));
            int y2 = std::min(image.rows - 1, static_cast<int>(detection.y2 + expand_y));

            // 裁剪图像
            cv::Rect roi(x1, y1, x2 - x1, y2 - y1);
            if (roi.width <= min_roi_width_ || roi.height <= min_roi_height_ ||
                roi.x + roi.width > image.cols ||
                roi.y + roi.height > image.rows) {
                // RCLCPP_WARN(this->get_logger(), "Invalid ROI: x=%d, y=%d, w=%d, h=%d for image %dx%d",
                //            roi.x, roi.y, roi.width, roi.height, image.cols, image.rows);
                continue;
            }

            cv::Mat cropped = image(roi).clone();
            cropped_images.push_back(cropped);

            // // Debug: 如果cropped_images不为空，保存cropped_images中的图像
            // if (!cropped_images.empty()) {
            //     for (size_t i = 0; i < cropped_images.size(); ++i) {
            //         std::string filename = "cropped_image_" + std::to_string(i) + ".jpg";
            //         cv::imwrite(filename, cropped_images[i]);
            //     }
            // }

            // 准备结果结构
            HelmetClassification result;
            result.sensorName = detection.camera_name;
            result.timestamp = detection.timestamp;
            result.x1 = detection.x1;
            result.y1 = detection.y1;
            result.x2 = detection.x2;
            result.y2 = detection.y2;
            helmet_results.push_back(result);
        }

        std::chrono::steady_clock::time_point end_data_load = std::chrono::steady_clock::now();

        /* Step 3: Inference */
        // 如果没有裁剪图像，直接返回
        if (cropped_images.empty()) {
            return;
        }

        // 执行批量推理
        for (size_t i = 0; i < cropped_images.size(); i += worker_->m_params->batch_size) {
            // 准备当前批次
            std::vector<cv::Mat> batch;
            size_t end_idx = std::min(i + worker_->m_params->batch_size, cropped_images.size());
            size_t batch_size = end_idx - i;

            // 只添加实际需要处理的图像
            for (size_t j = i; j < end_idx; ++j) {
                batch.push_back(cropped_images[j]);
            }

            // 执行推理 - batch_inference内部会设置m_activedBatchSize
            worker_->batch_inference(batch);

            // 处理结果 - 直接使用HelmetClassifier的推理结果
            if (worker_->m_helmet_classifier) {
                // 获取所有分类结果
                const auto& batch_results = worker_->m_helmet_classifier->get_all_classification_results();

                // 更新结果
                for (size_t j = 0; j < batch_size; ++j) {
                    if (j < batch_results.size()) {
                        helmet_results[i + j].cls = batch_results[j].cls;
                        helmet_results[i + j].confidence = batch_results[j].confidence;
                    }
                }
            }
        }

        std::chrono::steady_clock::time_point end_inference = std::chrono::steady_clock::now();

        /* Step 4: Result Process */
        // 处理推理结果
        process_helmet_results(helmet_results);
        std::chrono::steady_clock::time_point end_result_process = std::chrono::steady_clock::now();

        // Calculate timing for each step
        auto data_load_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_data_load - begin_data_load).count();
        auto inference_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_inference - end_data_load).count();
        auto result_process_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_result_process - end_inference).count();
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_result_process - begin_data_load).count();

        LOGV("\t  Processed %zu cropped images", cropped_images.size());
        LOGV("\t  Data Load:       %d us", data_load_time);
        LOGV("\t  Inference:       %d us", inference_time);
        LOGV("\t  Result Process:  %d us", result_process_time);
        LOGV("\t  Total Time:      %d us", total_time);

        if (total_time > 102000) {
            RCLCPP_WARN(this->get_logger(),
                        "Long delay between inference once: %.2f ms",
                        total_time / 1000.0);
        }
    }

    void process_helmet_results(const std::vector<HelmetClassification>& helmet_results) {
        if (helmet_results.empty()) {
            return;
        }

        // 创建JSON数组
        json results_array = json::array();

        // 添加所有头盔分类结果
        for (const auto& result : helmet_results) {
            // 创建单个结果的JSON对象
            json result_obj;
            result_obj["cls"] = result.cls;
            result_obj["confidence"] = result.confidence;
            result_obj["sensorName"] = result.sensorName;
            result_obj["timestamp"] = result.timestamp / 1000;  // ms
            result_obj["x1"] = static_cast<int>(result.x1);
            result_obj["y1"] = static_cast<int>(result.y1);
            result_obj["x2"] = static_cast<int>(result.x2);
            result_obj["y2"] = static_cast<int>(result.y2);

            // 添加到结果数组
            results_array.push_back(result_obj);
        }

        // 转换为字符串
        std::string json_str = results_array.dump();

        // 创建消息
        std_msgs::msg::String output_msg;
        output_msg.data = json_str;

        // 发布结果
        helmet_cls_publisher_->publish(output_msg);

        // RCLCPP_INFO(this->get_logger(), "Published %zu helmet classification results", helmet_results.size());
        // RCLCPP_INFO(this->get_logger(), "Published JSON: %s", json_str.c_str());
    }

private:
    // 参数
    std::vector<std::string> image_topics_;
    std::vector<std::string> vehicle_det_topics_;
    std::string output_topic_;
    std::vector<std::string> sensor_names_;
    std::string model_path_;
    int max_queue_size_;
    float inference_rate_;
    float confidence_threshold_;
    int max_batch_size_;
    std::map<std::string, std::string> topic_to_sensor_map_; // 映射topic到sensor_name
    int min_roi_width_;
    int min_roi_height_;
    int log_level_;
    int test_mode_;   // 0: 实验室环境，1: 路侧环境

    // ROS2 接口
    std::vector<rclcpp::Subscription<ImageMsg>::SharedPtr> image_subscribers_;
    std::vector<rclcpp::Subscription<UInt8MultiArrayMsg>::SharedPtr> vehicle_det_subscribers_;
    rclcpp::Publisher<std_msgs::msg::String>::SharedPtr helmet_cls_publisher_;
    rclcpp::TimerBase::SharedPtr inference_timer_;

    // 图像和检测结果队列
    std::map<size_t, std::deque<ImageData>> image_queues_;
    std::map<size_t, std::deque<std::pair<UInt8MultiArrayMsg::SharedPtr, std::vector<CyclistDetection>>>> detection_queues_;
    std::mutex queue_mutex_;

    // TRT Worker
    std::shared_ptr<thread::Worker> worker_;
};

} // namespace Det2dWorker

RCLCPP_COMPONENTS_REGISTER_NODE(Det2dWorker::HelmetClsNode)
