#ifndef __TRT_EVALUATION_HPP__
#define __TRT_EVALUATION_HPP__

#include <fstream>
#include <memory>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include "NvInfer.h"

namespace evaluation {

struct CocoDetectionInfo {
    CocoDetectionInfo() = delete;
    CocoDetectionInfo(std::string image_id, int category_id, float x_left,
                      float y_top, float width, float height, float score)
        : image_id(image_id),
          category_id(category_id),
          x_left(x_left),
          y_top(y_top),
          width(width),
          height(height),
          score(score){};

    std::string image_id;
    int category_id;
    float x_left;
    float y_top;
    float width;
    float height;
    float score;
};

class CocoEvaluator {

  public:
    CocoEvaluator() = delete;
    CocoEvaluator(std::string output_path)
        : m_outputPath(output_path),
          m_categories({1,  2,  3,  4,  5,  6,  7,  8,  9,  10, 11, 13, 14, 15,
                        16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 27, 28, 31, 32,
                        33, 34, 35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 46, 47,
                        48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 58, 59, 60, 61,
                        62, 63, 64, 65, 67, 70, 72, 73, 74, 75, 76, 77, 78, 79,
                        80, 81, 82, 84, 85, 86, 87, 88, 89, 90}){};

    void add_coco_result(const CocoDetectionInfo& result) {
        nlohmann::json coco_result;
        coco_result["image_id"] = result.image_id;
        uint64_t image_id_num = 0;
        bool is_numeric = true;
        for (char c : result.image_id) {
            if (!std::isdigit(c)) {
                is_numeric = false;
                break;
            }
        }
        if (is_numeric) {
            image_id_num = std::stoull(result.image_id);
            coco_result["image_id"] = image_id_num;
        }
        if (result.category_id >= 0 && result.category_id < 80) {
            coco_result["category_id"] = m_categories[result.category_id];
        } else {
            coco_result["category_id"] = result.category_id;
        }
        coco_result["bbox"].push_back(result.x_left);
        coco_result["bbox"].push_back(result.y_top);
        coco_result["bbox"].push_back(result.width);
        coco_result["bbox"].push_back(result.height);
        coco_result["score"] = result.score;
        m_results.push_back(coco_result);
    }

    void save_json_results() {
        std::ofstream file(m_outputPath);
        file << m_results.dump(4);
        file.close();
    }

  private:
    std::string m_outputPath;
    nlohmann::json m_results;
    int m_categories[80];
};

}  // namespace evaluation

#endif  // __TRT_EVALUATION_HPP__
