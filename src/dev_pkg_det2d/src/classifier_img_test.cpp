#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "trt_worker.hpp"
#include "utils.hpp"

#include <iostream>
#include <dirent.h> // For directory traversal
#include <sys/stat.h> // For file status
#include <cstring> // For std::strerror

#include <future>

using namespace std;

int main(int argc, char const *argv[])
{
    /*这么实现目的在于让调用的整个过程精简化*/
    string onnxPath         = "src/dev_pkg_det2d/models/onnx/inference_batch4.onnx";

    auto level              = logger::Level::VERB;
    auto params             = model::Params();

    // common params
    params.batch_size       = 4;
    params.img              = {192, 256, 3};
    params.task             = model::task_type::CLASSIFICATION;
    params.dev              = model::device::CPU;
    params.prec             = model::precision::INT8;  // INT8
    params.calib_data_file    = "src/dev_pkg_det2d/calibration/calibration_list_custom.txt";
    params.calib_table_file   = "src/dev_pkg_det2d/calibration/calibration_classifier_batch4_dla_table.txt";
    // classification params
    params.num_cls          = 21;
    std::vector<std::vector<int>>().swap(params.class_domains);
    params.class_domains.push_back({0,1,2,3,4,5,6,7,8,9});
    params.class_domains.push_back({10,11,12,13,14,15,16,17,18,19,20});
    // dla params
    params.use_dla          = true;
    params.dla_core         = 0;

    // 创建一个worker的实例, 在创建的时候就完成初始化
    auto worker   = thread::create_worker(onnxPath, level, params);
    std::chrono::steady_clock::time_point begin;
    std::chrono::steady_clock::time_point end;
    // jump first launch
    worker->inference("src/dev_pkg_det2d/data/source/classification");
    // 根据worker中的task类型进行推理
    begin = std::chrono::steady_clock::now();
    for (int i = 0; i < 5; i++) {
        worker->inference("src/dev_pkg_det2d/data/source/classification");
    }
    end = std::chrono::steady_clock::now();
    LOG("\ttime difference of 5 sync inference uses %d ms", 
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());

    begin = std::chrono::steady_clock::now();
    std::vector<std::future<void>> futures;
    for (int i = 0; i < 5; i++) {
        futures.push_back(worker->async_inference("src/dev_pkg_det2d/data/source/classification"));
    }
    for (auto& future : futures) {
        future.wait();
    }
    end = std::chrono::steady_clock::now();
    LOG("\ttime difference of 5 async inference uses %d ms", 
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
         
    return 0;
}

