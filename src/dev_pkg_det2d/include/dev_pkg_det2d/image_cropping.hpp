#ifndef __IMAGE_CROPPING_HPP__
#define __IMAGE_CROPPING_HPP__

#include <vector>
#include "opencv2/opencv.hpp"

namespace model {
    /**
     * @brief 图像切割类，用于从输入图像中切割出多个区域
     */
    class ImageCropper {
    public:
        /**
         * @brief 构造函数
         * @param padding 切割区域四周扩展的像素数
         */
        ImageCropper(int padding = 0) : m_padding(padding) {}

        /**
         * @brief 设置切割区域四周扩展的像素数
         * @param padding 扩展的像素数
         */
        void setPadding(int padding) {
            m_padding = padding;
        }

        /**
         * @brief 添加矩形框向量的内容
         * @param rects 矩形框向量
         */
        void addRect(const cv::Rect& rect) {
            m_rects.push_back(rect);
        }

        /**
         * @brief 清空矩形框向量
         */
        void clearRects() {
            m_rects.clear();
        }
        
        /**
         * @brief 从输入图像中切割出多个区域
         * @param image 输入图像
         * @param rects 切割区域的矩形框向量
         * @return 切割出的图像向量
         */
        std::vector<cv::Mat> crop(const cv::Mat& image) {
            std::vector<cv::Mat> cropped_images;
            cropped_images.reserve(m_rects.size());

            for (const auto& rect : m_rects) {
                // 计算扩展后的矩形区域
                cv::Rect padded_rect = expandRect(rect, m_padding, image.cols, image.rows);
                // 切割图像
                cv::Mat cropped = image(padded_rect);
                cropped_images.push_back(cropped.clone());
            }

            return cropped_images;
        }

        /**
         * @brief 从输入图像中切割出单个区域
         * @param image 输入图像
         * @param rect 切割区域的矩形框
         * @return 切割出的图像
         */
        cv::Mat cropSingle(const cv::Mat& image, const cv::Rect& rect) {
            cv::Rect padded_rect = expandRect(rect, m_padding, image.cols, image.rows);
            return image(padded_rect).clone();
        }

    private:
        int m_padding; // 切割区域四周扩展的像素数
        std::vector<cv::Rect> m_rects;

        /**
         * @brief 扩展矩形区域
         * @param rect 原始矩形区域
         * @param padding 扩展的像素数
         * @param max_width 图像最大宽度
         * @param max_height 图像最大高度
         * @return 扩展后的矩形区域
         */
        cv::Rect expandRect(const cv::Rect& rect, int padding, int max_width, int max_height) {
            int x = std::max(0, rect.x - padding);
            int y = std::max(0, rect.y - padding);
            int width = std::min(max_width - x, rect.width + 2 * padding);
            int height = std::min(max_height - y, rect.height + 2 * padding);
            
            return cv::Rect(x, y, width, height);
        }
    };
} // namespace model

#endif