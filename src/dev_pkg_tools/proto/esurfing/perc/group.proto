syntax = "proto3";

package perception.config;

import "esurfing/math/geometry.proto";

option java_outer_classname = "GroupConfig";
option java_multiple_files = false;

message LidarIntrisic {
  bool enabled = 1;
  int32 min_intensity = 2;
  int32 max_intensity = 3;

  float rot = 4;
  float vert = 5;
  float dist = 6;
  float dist_x = 7;
  float dist_y = 8;
  float vert_offset = 9;
  float horiz_offset = 10;
  float focal_distance = 11;
  float focal_scope = 12;
  int32 ring = 13;
  float cos_rot = 14;
  float sin_rot = 15;
  float cos_vert = 16;
  float sin_vert = 17;
}

message Lidar {
  bytes name = 1;
  enum Type {
    UNKNOWN = 0;
    VLP_16 = 1;
    VLP_32 = 2;
    HESAI_64 = 3;
  }
  Type type = 2;
  repeated LidarIntrisic intrinsics = 3;

  float min_distance = 4;
  float max_distance = 5;

  Transformation3d tf_map_lidar = 6;  // Lidar到地图坐标系的转换外参

  bytes topic = 7;
  int32 usage = 8;
}

message Radar {
  bytes name = 1;
  enum Type {
    UNKNOWN = 0;
    ARBE = 1;
  }
  Type type = 2;
  repeated LidarIntrisic intrinsics = 3; // Radar与Lidar公用一组内参定义

  float min_distance = 4;
  float max_distance = 5;

  Transformation3d tf_map_radar = 6;  // Radar到地图坐标系的转换外参

  bytes topic = 7;
  int32 usage = 8;
}

message Camera {
  bytes name = 1;
  int32 image_width = 2;
  int32 image_height = 3;

  Transformation3d tf_map_camera = 4; // 相机到地图坐标系的转换外参
  double fx = 5;
  double fy = 6;
  double cx = 7;
  double cy = 8;
  repeated double radial_distortion = 9;

  bytes topic = 10;
  bool time_synced = 11;

  int32 usage = 12;
}

message ConfigGroup {
  bytes group_name = 1;
  int32 group_type = 2;
  Transformation3d tf_map_group = 3; //小组坐标系到地图坐标系的转换
  repeated Radar radars = 4;
  repeated Camera cameras = 5;
}
