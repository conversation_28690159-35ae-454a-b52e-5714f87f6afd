import os
import nacos

'''
shell test
export NACOS_SERVER_IP="127.0.0.1" \
export NACOS_NAMESPACE_ID="4d11a15d-b36c-46e6-96a4-520ac1326e14" \
export GROUP_ID="G32050700001B01"
'''

nacos_server_ip = os.environ.get('NACOS_SERVER_IP')
print('NACOS_SERVER_IP:', nacos_server_ip)
nacos_namespace_id = os.environ.get('NACOS_NAMESPACE_ID')
print('NACOS_NAMESPACE_ID:', nacos_namespace_id)
group_id = os.environ.get('GROUP_ID')
print('GROUP_ID:', group_id)
if nacos_server_ip is None or nacos_namespace_id is None or group_id is None:
    exit(1)

client = nacos.NacosClient(server_addresses = nacos_server_ip,namespace = nacos_namespace_id) # namespace='public'

drv = client.get_config(data_id = 'config_drv.yaml', group = group_id)
if drv is None:
    print('config_drv.yaml is None')

camera = client.get_config(data_id = 'config_camera.yaml', group = group_id)
if camera is None:
    print('config_camera.yaml is None')

lidar = client.get_config(data_id = 'config_lidar.yaml', group = group_id)
if lidar is None:
    print('config_lidar.yaml is None')

group = client.get_config(data_id = 'config_group.cfg', group = group_id)
if group is None:
    print('config_group.cfg is None')

with open('/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_drv.yaml', 'w') as f:
    f.write(drv)
with open('/app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/config/config_drv.yaml', 'w') as f:
    f.write(drv)
with open('/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_camera.yaml', 'w') as f:
    f.write(camera)
with open('/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_lidar.yaml', 'w') as f:
    f.write(lidar)
with open('/app/dev_ws_det2d/install/dev_pkg_drv/share/dev_pkg_drv/config/config_group.cfg', 'w') as f:
    f.write(group)
with open('/app/dev_ws_det2d/install/dev_pkg_kafka/share/dev_pkg_kafka/config/config_group.cfg', 'w') as f:
    f.write(group)