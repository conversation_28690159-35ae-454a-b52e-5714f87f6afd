// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/math/geometry.proto

#include "esurfing/math/geometry.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace perception {
namespace config {
PROTOBUF_CONSTEXPR Transformation3d::Transformation3d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.translation_)*/nullptr
  , /*decltype(_impl_.rotation_)*/nullptr
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Transformation3dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Transformation3dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Transformation3dDefaultTypeInternal() {}
  union {
    Transformation3d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Transformation3dDefaultTypeInternal _Transformation3d_default_instance_;
PROTOBUF_CONSTEXPR Vector3d::Vector3d(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct Vector3dDefaultTypeInternal {
  PROTOBUF_CONSTEXPR Vector3dDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~Vector3dDefaultTypeInternal() {}
  union {
    Vector3d _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 Vector3dDefaultTypeInternal _Vector3d_default_instance_;
PROTOBUF_CONSTEXPR Quaterniond::Quaterniond(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.x_)*/0
  , /*decltype(_impl_.y_)*/0
  , /*decltype(_impl_.z_)*/0
  , /*decltype(_impl_.w_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct QuaterniondDefaultTypeInternal {
  PROTOBUF_CONSTEXPR QuaterniondDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~QuaterniondDefaultTypeInternal() {}
  union {
    Quaterniond _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 QuaterniondDefaultTypeInternal _Quaterniond_default_instance_;
}  // namespace config
}  // namespace perception
static ::_pb::Metadata file_level_metadata_esurfing_2fmath_2fgeometry_2eproto[3];
static constexpr ::_pb::EnumDescriptor const** file_level_enum_descriptors_esurfing_2fmath_2fgeometry_2eproto = nullptr;
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_esurfing_2fmath_2fgeometry_2eproto = nullptr;

const uint32_t TableStruct_esurfing_2fmath_2fgeometry_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Transformation3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Transformation3d, _impl_.translation_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Transformation3d, _impl_.rotation_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Vector3d, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Vector3d, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Vector3d, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Vector3d, _impl_.z_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Quaterniond, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Quaterniond, _impl_.x_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Quaterniond, _impl_.y_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Quaterniond, _impl_.z_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Quaterniond, _impl_.w_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::perception::config::Transformation3d)},
  { 8, -1, -1, sizeof(::perception::config::Vector3d)},
  { 17, -1, -1, sizeof(::perception::config::Quaterniond)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::perception::config::_Transformation3d_default_instance_._instance,
  &::perception::config::_Vector3d_default_instance_._instance,
  &::perception::config::_Quaterniond_default_instance_._instance,
};

const char descriptor_table_protodef_esurfing_2fmath_2fgeometry_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034esurfing/math/geometry.proto\022\021percepti"
  "on.config\"v\n\020Transformation3d\0220\n\013transla"
  "tion\030\001 \001(\0132\033.perception.config.Vector3d\022"
  "0\n\010rotation\030\002 \001(\0132\036.perception.config.Qu"
  "aterniond\"+\n\010Vector3d\022\t\n\001x\030\001 \001(\001\022\t\n\001y\030\002 "
  "\001(\001\022\t\n\001z\030\003 \001(\001\"9\n\013Quaterniond\022\t\n\001x\030\001 \001(\001"
  "\022\t\n\001y\030\002 \001(\001\022\t\n\001z\030\003 \001(\001\022\t\n\001w\030\004 \001(\001B\022B\016Geo"
  "metryConfigP\000b\006proto3"
  ;
static ::_pbi::once_flag descriptor_table_esurfing_2fmath_2fgeometry_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_esurfing_2fmath_2fgeometry_2eproto = {
    false, false, 301, descriptor_table_protodef_esurfing_2fmath_2fgeometry_2eproto,
    "esurfing/math/geometry.proto",
    &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_once, nullptr, 0, 3,
    schemas, file_default_instances, TableStruct_esurfing_2fmath_2fgeometry_2eproto::offsets,
    file_level_metadata_esurfing_2fmath_2fgeometry_2eproto, file_level_enum_descriptors_esurfing_2fmath_2fgeometry_2eproto,
    file_level_service_descriptors_esurfing_2fmath_2fgeometry_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_esurfing_2fmath_2fgeometry_2eproto_getter() {
  return &descriptor_table_esurfing_2fmath_2fgeometry_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_esurfing_2fmath_2fgeometry_2eproto(&descriptor_table_esurfing_2fmath_2fgeometry_2eproto);
namespace perception {
namespace config {

// ===================================================================

class Transformation3d::_Internal {
 public:
  static const ::perception::config::Vector3d& translation(const Transformation3d* msg);
  static const ::perception::config::Quaterniond& rotation(const Transformation3d* msg);
};

const ::perception::config::Vector3d&
Transformation3d::_Internal::translation(const Transformation3d* msg) {
  return *msg->_impl_.translation_;
}
const ::perception::config::Quaterniond&
Transformation3d::_Internal::rotation(const Transformation3d* msg) {
  return *msg->_impl_.rotation_;
}
Transformation3d::Transformation3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Transformation3d)
}
Transformation3d::Transformation3d(const Transformation3d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Transformation3d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.translation_){nullptr}
    , decltype(_impl_.rotation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_translation()) {
    _this->_impl_.translation_ = new ::perception::config::Vector3d(*from._impl_.translation_);
  }
  if (from._internal_has_rotation()) {
    _this->_impl_.rotation_ = new ::perception::config::Quaterniond(*from._impl_.rotation_);
  }
  // @@protoc_insertion_point(copy_constructor:perception.config.Transformation3d)
}

inline void Transformation3d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.translation_){nullptr}
    , decltype(_impl_.rotation_){nullptr}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Transformation3d::~Transformation3d() {
  // @@protoc_insertion_point(destructor:perception.config.Transformation3d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Transformation3d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete _impl_.translation_;
  if (this != internal_default_instance()) delete _impl_.rotation_;
}

void Transformation3d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Transformation3d::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Transformation3d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Transformation3d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .perception.config.Vector3d translation = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_translation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Quaterniond rotation = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_rotation(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Transformation3d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Transformation3d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .perception.config.Vector3d translation = 1;
  if (this->_internal_has_translation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, _Internal::translation(this),
        _Internal::translation(this).GetCachedSize(), target, stream);
  }

  // .perception.config.Quaterniond rotation = 2;
  if (this->_internal_has_rotation()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, _Internal::rotation(this),
        _Internal::rotation(this).GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Transformation3d)
  return target;
}

size_t Transformation3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Transformation3d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .perception.config.Vector3d translation = 1;
  if (this->_internal_has_translation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.translation_);
  }

  // .perception.config.Quaterniond rotation = 2;
  if (this->_internal_has_rotation()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.rotation_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Transformation3d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Transformation3d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Transformation3d::GetClassData() const { return &_class_data_; }


void Transformation3d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Transformation3d*>(&to_msg);
  auto& from = static_cast<const Transformation3d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Transformation3d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_translation()) {
    _this->_internal_mutable_translation()->::perception::config::Vector3d::MergeFrom(
        from._internal_translation());
  }
  if (from._internal_has_rotation()) {
    _this->_internal_mutable_rotation()->::perception::config::Quaterniond::MergeFrom(
        from._internal_rotation());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Transformation3d::CopyFrom(const Transformation3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Transformation3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Transformation3d::IsInitialized() const {
  return true;
}

void Transformation3d::InternalSwap(Transformation3d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Transformation3d, _impl_.rotation_)
      + sizeof(Transformation3d::_impl_.rotation_)
      - PROTOBUF_FIELD_OFFSET(Transformation3d, _impl_.translation_)>(
          reinterpret_cast<char*>(&_impl_.translation_),
          reinterpret_cast<char*>(&other->_impl_.translation_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Transformation3d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_getter, &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_once,
      file_level_metadata_esurfing_2fmath_2fgeometry_2eproto[0]);
}

// ===================================================================

class Vector3d::_Internal {
 public:
};

Vector3d::Vector3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Vector3d)
}
Vector3d::Vector3d(const Vector3d& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Vector3d* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.z_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  // @@protoc_insertion_point(copy_constructor:perception.config.Vector3d)
}

inline void Vector3d::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Vector3d::~Vector3d() {
  // @@protoc_insertion_point(destructor:perception.config.Vector3d)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Vector3d::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Vector3d::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Vector3d::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Vector3d)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.z_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.z_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Vector3d::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Vector3d::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Vector3d)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Vector3d)
  return target;
}

size_t Vector3d::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Vector3d)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Vector3d::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Vector3d::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Vector3d::GetClassData() const { return &_class_data_; }


void Vector3d::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Vector3d*>(&to_msg);
  auto& from = static_cast<const Vector3d&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Vector3d)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = from._internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Vector3d::CopyFrom(const Vector3d& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Vector3d)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Vector3d::IsInitialized() const {
  return true;
}

void Vector3d::InternalSwap(Vector3d* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Vector3d, _impl_.z_)
      + sizeof(Vector3d::_impl_.z_)
      - PROTOBUF_FIELD_OFFSET(Vector3d, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Vector3d::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_getter, &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_once,
      file_level_metadata_esurfing_2fmath_2fgeometry_2eproto[1]);
}

// ===================================================================

class Quaterniond::_Internal {
 public:
};

Quaterniond::Quaterniond(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Quaterniond)
}
Quaterniond::Quaterniond(const Quaterniond& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Quaterniond* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){}
    , decltype(_impl_.y_){}
    , decltype(_impl_.z_){}
    , decltype(_impl_.w_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.x_, &from._impl_.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.w_) -
    reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.w_));
  // @@protoc_insertion_point(copy_constructor:perception.config.Quaterniond)
}

inline void Quaterniond::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.x_){0}
    , decltype(_impl_.y_){0}
    , decltype(_impl_.z_){0}
    , decltype(_impl_.w_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

Quaterniond::~Quaterniond() {
  // @@protoc_insertion_point(destructor:perception.config.Quaterniond)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Quaterniond::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Quaterniond::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Quaterniond::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Quaterniond)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.w_) -
      reinterpret_cast<char*>(&_impl_.x_)) + sizeof(_impl_.w_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Quaterniond::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _impl_.x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _impl_.y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double z = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _impl_.z_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double w = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _impl_.w_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Quaterniond::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Quaterniond)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(1, this->_internal_x(), target);
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(2, this->_internal_y(), target);
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(3, this->_internal_z(), target);
  }

  // double w = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = this->_internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(4, this->_internal_w(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Quaterniond)
  return target;
}

size_t Quaterniond::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Quaterniond)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double x = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = this->_internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 8;
  }

  // double y = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = this->_internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 8;
  }

  // double z = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = this->_internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    total_size += 1 + 8;
  }

  // double w = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = this->_internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Quaterniond::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Quaterniond::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Quaterniond::GetClassData() const { return &_class_data_; }


void Quaterniond::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Quaterniond*>(&to_msg);
  auto& from = static_cast<const Quaterniond&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Quaterniond)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_x = from._internal_x();
  uint64_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _this->_internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_y = from._internal_y();
  uint64_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _this->_internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_z = from._internal_z();
  uint64_t raw_z;
  memcpy(&raw_z, &tmp_z, sizeof(tmp_z));
  if (raw_z != 0) {
    _this->_internal_set_z(from._internal_z());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_w = from._internal_w();
  uint64_t raw_w;
  memcpy(&raw_w, &tmp_w, sizeof(tmp_w));
  if (raw_w != 0) {
    _this->_internal_set_w(from._internal_w());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Quaterniond::CopyFrom(const Quaterniond& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Quaterniond)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Quaterniond::IsInitialized() const {
  return true;
}

void Quaterniond::InternalSwap(Quaterniond* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Quaterniond, _impl_.w_)
      + sizeof(Quaterniond::_impl_.w_)
      - PROTOBUF_FIELD_OFFSET(Quaterniond, _impl_.x_)>(
          reinterpret_cast<char*>(&_impl_.x_),
          reinterpret_cast<char*>(&other->_impl_.x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Quaterniond::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_getter, &descriptor_table_esurfing_2fmath_2fgeometry_2eproto_once,
      file_level_metadata_esurfing_2fmath_2fgeometry_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::perception::config::Transformation3d*
Arena::CreateMaybeMessage< ::perception::config::Transformation3d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Transformation3d >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::Vector3d*
Arena::CreateMaybeMessage< ::perception::config::Vector3d >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Vector3d >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::Quaterniond*
Arena::CreateMaybeMessage< ::perception::config::Quaterniond >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Quaterniond >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
