import os
import yaml

'''
shell test
export KAFKA_BROKER_ID="127.0.0.1:9092" \
export KAFKA_TOPIC_NAME="virtual_events" \
export KAFKA_PARTITION_ID=0
'''

kafka_broker_id = os.getenv('KAFKA_BROKER_ID')
print('KAFKA_BROKER_ID:', kafka_broker_id)
kafka_topic_name = os.getenv('KAFKA_TOPIC_NAME')
print('KAFKA_TOPIC_NAME:', kafka_topic_name)
kafka_partition_id = int(os.getenv('KAFKA_PARTITION_ID'))
print('KAFKA_PARTITION_ID:', kafka_partition_id)
if kafka_broker_id is None or kafka_topic_name is None or kafka_partition_id is None:
    exit(1)

with open('/app/dev_ws_det2d/install/dev_pkg_kafka/share/dev_pkg_kafka/config/params.yaml', 'r') as file:
    data = yaml.safe_load(file)

parameters = data.get('kafka_node', {}).get('ros__parameters', {})
parameters['kafka_broker'] = kafka_broker_id
parameters['output_kafka_topic_name'] = kafka_topic_name
parameters['kafka_partition'] = kafka_partition_id
data['kafka_node']['ros__parameters'] = parameters
updated_yaml_content = yaml.dump(data, default_flow_style=False)
print("Set kafka_broker:", kafka_broker_id, "to params.yaml")
print("Set kafka_topic_name:", kafka_topic_name, "to params.yaml")
print("Set kafka_partition:", kafka_partition_id, "to params.yaml")

with open('/app/dev_ws_det2d/install/dev_pkg_kafka/share/dev_pkg_kafka/config/params.yaml', 'w') as file:
    file.write(updated_yaml_content)


with open('/app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/config/params.yaml', 'r') as file:
    data = yaml.safe_load(file)
updated_yaml_content = yaml.dump(data, default_flow_style=False)
with open('/app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/config/params.yaml', 'w') as file:
    file.write(updated_yaml_content)