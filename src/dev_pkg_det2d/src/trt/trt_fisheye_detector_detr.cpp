#include "opencv2/core/types.hpp"
#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp"
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <algorithm>
#include <string>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc//imgproc.hpp"
#include "opencv2/opencv.hpp"
#include "trt_fisheye_detector_detr.hpp"
#include "trt_preprocess.hpp"
#include "fisheye_labels_detr.hpp"
#include "image_cropping.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace detector {

float FisheyeDetectorDetr::iou_calc(bbox bbox1, bbox bbox2){
    auto inter_x0 = std::max(bbox1.x0, bbox2.x0);
    auto inter_y0 = std::max(bbox1.y0, bbox2.y0);
    auto inter_x1 = std::min(bbox1.x1, bbox2.x1);
    auto inter_y1 = std::min(bbox1.y1, bbox2.y1);

    float inter_w = inter_x1 - inter_x0;
    float inter_h = inter_y1 - inter_y0;

    float inter_area = inter_w * inter_h;
    float union_area =
        (bbox1.x1 - bbox1.x0) * (bbox1.y1 - bbox1.y0) +
        (bbox2.x1 - bbox2.x0) * (bbox2.y1 - bbox2.y0) -
        inter_area;

    return inter_area / union_area;
}

void FisheyeDetectorDetr::setup(void const* data, size_t size) {
   /*
     * detector setup需要做的事情
     *   创建engine, context
     *   设置bindings。这里需要注意，不同版本的yolo的输出binding可能还不一样
     *   分配memory空间。这里需要注意，不同版本的yolo的输出所需要的空间也还不一样
     */

    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    // m_outputDims  = m_context->getBindingDimensions(1);

    CUDA_CHECK(cudaStreamCreate(&m_stream));

    m_inputSize     = m_params->batch_size * m_params->img.h * m_params->img.w * m_params->img.c * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;
    // m_outputSize    = m_params->batch_size * m_outputDims.d[1] * m_outputDims.d[2] * sizeof(float);
    m_boxesOutputSize = m_params->batch_size * m_context->getBindingDimensions(1).d[1] * m_context->getBindingDimensions(1).d[2] * sizeof(float);
    m_logitsOutputSize = m_params->batch_size * m_context->getBindingDimensions(2).d[1] * m_context->getBindingDimensions(2).d[2] * sizeof(float);
    // LOGW("logits output size: %d, boxes output size: %d", m_context->getBindingDimensions(2).d[2], m_context->getBindingDimensions(1).d[2]);

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_logitsOutputMemory[0], m_logitsOutputSize));
    CUDA_CHECK(cudaMallocHost(&m_boxesOutputMemory[0], m_boxesOutputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_logitsOutputMemory[1], m_logitsOutputSize));
    CUDA_CHECK(cudaMalloc(&m_boxesOutputMemory[1], m_boxesOutputSize));

    // 创建m_bindings，之后再寻址就直接从这里找
    m_bindingsOverride[0] = m_inputMemory[1];
    m_bindingsOverride[2] = m_logitsOutputMemory[1];
    m_bindingsOverride[1] = m_boxesOutputMemory[1];
}

void FisheyeDetectorDetr::reset_task(){}

bool FisheyeDetectorDetr::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    auto start = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
    // // mean and std of rgb
    // float mean[]       = {0.485, 0.456, 0.406};
    // float std[]        = {0.229, 0.224, 0.225};
    // mean and std of bgr
    float mean[]       = {0.406, 0.456, 0.485};
    float std[]        = {0.225, 0.224, 0.229};
    /*Preprocess -- DETR model preprocessing*/
    m_originalSizes.clear();
    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i);
            return false;
        }
        // 保存原始图像尺寸
        m_originalSizes.push_back({m_inputImages[i].cols, m_inputImages[i].rows});
    }

    /*Preprocess -- resize to model input size*/
    std::vector<cv::Mat> resized_images(m_activedBatchSize);
    for (int i = 0; i < m_activedBatchSize; i++) {
        cv::resize(m_inputImages[i], resized_images[i],
                   cv::Size(m_params->img.w, m_params->img.h), 0, 0, cv::INTER_LINEAR);
    }

    /*Preprocess -- normalization and BGR2RGB conversion, NHWC->NCHW*/
    for (int b = 0; b < m_params->batch_size; b++) {
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        // 每个通道在当前批次内的起始位置
        const int offset_ch0 = offset_batch;          // R通道起始位置
        const int offset_ch1 = offset_batch + m_imgArea;   // G通道起始位置
        const int offset_ch2 = offset_batch + 2 * m_imgArea; // B通道起始位置

        if (b < m_activedBatchSize) {
            for (int i = 0; i < m_params->img.h; i++) {
                for (int j = 0; j < m_params->img.w; j++) {
                    // 原图NHWC布局中的像素索引（BGR顺序）
                    const int src_index = (i * m_params->img.w + j) * m_params->img.c;

                    // BGR转RGB并归一化
                    float blue = resized_images[b].data[src_index + 0] / 255.0f;
                    float green = resized_images[b].data[src_index + 1] / 255.0f;
                    float red = resized_images[b].data[src_index + 2] / 255.0f;

                    // 减去均值并除以标准差
                    blue = (blue - mean[0]) / std[0];
                    green = (green - mean[1]) / std[1];
                    red = (red - mean[2]) / std[2];

                    // 存储到输入内存（NCHW格式）
                    m_inputMemory[0][offset_ch2 + i * m_params->img.w + j] = blue;
                    m_inputMemory[0][offset_ch1 + i * m_params->img.w + j] = green;
                    m_inputMemory[0][offset_ch0 + i * m_params->img.w + j] = red;
                }
            }
        } else {
            // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
            for (int i = 0; i < m_params->img.h; i++) {
                for (int j = 0; j < m_params->img.w; j++) {
                    m_inputMemory[0][offset_ch0 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch1 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch2 + i * m_params->img.w + j] = 0;
                }
            }
        }
    }

    /*Preprocess -- copy host data to device*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));
    auto end = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
    LOG("\t  ******************** Start log from trt_fisheye_detector_detr ********************");
    LOG("\t\t  Preprocess:      %d us", (end - start).count());
    return true;
}

bool FisheyeDetectorDetr::preprocess_gpu() {
    /*Preprocess -- DETR model preprocessing*/
    if (m_inputImage.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }

    /*Preprocess -- 使用GPU进行warpAffine, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(m_inputImage, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w,
                                   preprocess::tactics::GPU_WARP_AFFINE);

    return true;
}

bool FisheyeDetectorDetr::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);
    m_batch_obj_bboxes = std::vector<std::vector<bbox>>(m_activedBatchSize, std::vector<bbox>());
    m_batch_cropped_images = std::vector<std::vector<cv::Mat>>(m_activedBatchSize, std::vector<cv::Mat>());

    auto start = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
    /*Postprocess -- copy device data to host*/
    CUDA_CHECK(cudaMemcpyAsync(m_logitsOutputMemory[0], m_logitsOutputMemory[1], m_logitsOutputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaMemcpyAsync(m_boxesOutputMemory[0], m_boxesOutputMemory[1], m_boxesOutputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    // 获取logits和boxes的维度信息
    const int num_queries = 300;
    const int num_classes = 91;
    const int max_number_boxes = 100; // Default value, can be made configurable

    // LOGW("%f", m_boxesOutputMemory[0][0 * num_queries * 4 + 0]);
    // LOGW("%f", m_boxesOutputMemory[0][0 * num_queries * 4 + 1]);
    // LOGW("%f", m_boxesOutputMemory[0][0 * num_queries * 4 + 2]);
    // LOGW("%f", m_boxesOutputMemory[0][0 * num_queries * 4 + 3]);
    // LOGW("%f", m_logitsOutputMemory[0][0 * num_queries * 4 + 3]);

    // 处理每个batch
    for (int b = 0; b < m_activedBatchSize; b++) {
        std::vector<bbox> boxes;

        // Step 1: Apply sigmoid to all logits and flatten probabilities
        std::vector<float> flat_prob;
        flat_prob.reserve(num_queries * num_classes);

        for (int q = 0; q < num_queries; q++) {
            for (int c = 0; c < num_classes; c++) {
                float logit = m_logitsOutputMemory[0][b * num_queries * num_classes + q * num_classes + c];
                float prob = 1.0f / (1.0f + exp(-logit)); // sigmoid
                flat_prob.push_back(prob);
            }
        }

        // Step 2: Get top-k values and indices (equivalent to np.argsort()[-max_number_boxes:][::-1])
        std::vector<std::pair<float, int>> prob_index_pairs;
        prob_index_pairs.reserve(flat_prob.size());

        for (int i = 0; i < flat_prob.size(); i++) {
            prob_index_pairs.push_back({flat_prob[i], i});
        }

        // Sort by probability in descending order
        std::sort(prob_index_pairs.begin(), prob_index_pairs.end(),
                  [](const std::pair<float, int>& a, const std::pair<float, int>& b) {
                      return a.first > b.first;
                  });

        // Take top-k (limited by max_number_boxes)
        int k = std::min(max_number_boxes, static_cast<int>(prob_index_pairs.size()));

        std::vector<float> scores;
        std::vector<int> topk_boxes;
        std::vector<int> labels;

        scores.reserve(k);
        topk_boxes.reserve(k);
        labels.reserve(k);

        for (int i = 0; i < k; i++) {
            int topk_index = prob_index_pairs[i].second;
            scores.push_back(prob_index_pairs[i].first);
            topk_boxes.push_back(topk_index / num_classes); // equivalent to topk_indexes // pred_logits.shape[2]
            labels.push_back(topk_index % num_classes);     // equivalent to topk_indexes % pred_logits.shape[2]
        }

        // Step 3: Convert boxes from cxcywh to xyxy format and gather corresponding boxes
        for (int i = 0; i < k; i++) {
            int box_idx = topk_boxes[i];

            // Get box coordinates (cx, cy, w, h format)
            float cx = m_boxesOutputMemory[0][b * num_queries * 4 + box_idx * 4 + 0];
            float cy = m_boxesOutputMemory[0][b * num_queries * 4 + box_idx * 4 + 1];
            float w = m_boxesOutputMemory[0][b * num_queries * 4 + box_idx * 4 + 2];
            float h = m_boxesOutputMemory[0][b * num_queries * 4 + box_idx * 4 + 3];

            // Clip width and height to positive values (equivalent to np.clip(w, a_min=0.0, a_max=None))
            w = std::max(w, 0.0f);
            h = std::max(h, 0.0f);

            // Convert to x1, y1, x2, y2 format (equivalent to box_cxcywh_to_xyxy_numpy)
            float x1 = cx - 0.5f * w;
            float y1 = cy - 0.5f * h;
            float x2 = cx + 0.5f * w;
            float y2 = cy + 0.5f * h;

            // Step 4: Rescale box locations (equivalent to boxes * scale_fct[0, :])
            float orig_w = m_originalSizes[b].width;
            float orig_h = m_originalSizes[b].height;

            // Scale factor: [img_w, img_h, img_w, img_h]
            x1 *= orig_w;
            y1 *= orig_h;
            x2 *= orig_w;
            y2 *= orig_h;

            // Ensure coordinates are within valid range
            x1 = std::floor(std::min(std::max(0.0f, x1), orig_w - 1));
            y1 = std::floor(std::min(std::max(0.0f, y1), orig_h - 1));
            x2 = std::ceil(std::min(std::max(0.0f, x2), orig_w - 1));
            y2 = std::ceil(std::min(std::max(0.0f, y2), orig_h - 1));

            bbox det_box(x1, y1, x2, y2, scores[i], labels[i]);
            boxes.push_back(det_box);
        }

        // Step 5: Filter detections based on confidence threshold
        // (equivalent to high_confidence_indices = np.argmin(scores > confidence_threshold))
        std::vector<bbox> filtered_boxes;
        for (const auto& box : boxes) {
            if (box.confidence > m_params->conf_thresh) {
                filtered_boxes.push_back(box);
            }
        }

        boxes = filtered_boxes;

        /*Postprocess -- draw_bbox*/
        int   font_face  = 0;
        float font_scale = 0.0005 * MIN(m_inputImages[b].cols, m_inputImages[b].rows);
        int   font_thick = 2;
        int   baseline;
        FisheyeLabelsDetr labelsShow;
        for (int i = 0; i < boxes.size(); i++) {
            auto box = boxes[i];
            auto name = labelsShow.coco_get_label(box.label);
            // auto rec_color = labelsShow.coco_get_color(box.label);
            // auto txt_color = labelsShow.get_inverse_color(rec_color);
            // auto txt = cv::format({"%s: %.2f%%"}, name.c_str(), box.confidence * 100);
            // auto txt_size = cv::getTextSize(txt, font_face, font_scale, font_thick, &baseline);
            // int txt_height = txt_size.height + baseline + 10;
            // int txt_width  = txt_size.width + 3;
            // cv::Point txt_pos(round(box.x0), round(box.y0 - (txt_size.height - baseline + font_thick)));
            // cv::Rect  txt_rec(round(box.x0 - font_thick), round(box.y0 - txt_height), txt_width, txt_height);
            // cv::Rect  box_rec(round(box.x0), round(box.y0), round(box.x1 - box.x0), round(box.y1 - box.y0));
            // cv::rectangle(m_inputImages[b], box_rec, rec_color, 3);
            // cv::rectangle(m_inputImages[b], txt_rec, rec_color, -1);
            // cv::putText(m_inputImages[b], txt, txt_pos, font_face, font_scale, txt_color, font_thick, 16);
            LOGV("\t\t  %s detected. Confidence: %.2f%%. Cord: (x0, y0):(%6.2f, %6.2f), (x1, y1)(%6.2f, %6.2f)", 
                name.c_str(), box.confidence * 100, box.x0, box.y0, box.x1, box.y1);
        }
        LOGV("\t\t  Batch %d Summary - Detected Objects: %d", b, boxes.size());
        // std::string timestamp = std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
        // std::string image_path = "./tmp/" + timestamp + ".jpg";
        // cv::imwrite(image_path, m_inputImages[b]);

        // 存储该batch的检测结果
        m_batch_obj_bboxes[b] = boxes;
    }

    auto end = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
    LOG("\t\t  Postprocess:     %d us", (end - start).count());
    LOG("\t  ******************** End log from trt_fisheye_detector_detr **********************");

    return true;
}

bool FisheyeDetectorDetr::postprocess_gpu() {
    return postprocess_cpu();
}

// this func will be called by other thread
bool FisheyeDetectorDetr::get_obj_result(std::vector<bbox> &objects, std::string id) {
    int loop_cnt = 0;
    while (obj_results.find(id) == obj_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_obj_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    objects = obj_results[id];
    obj_results.erase(id);

    return true;
}

bool FisheyeDetectorDetr::check_model() {
    if (Model::m_params->det_model == model::NULLDET) {
        LOGW("No detection model specified");
        return false;
    }
    return true;
}

void FisheyeDetectorDetr::inference() {
    if (m_params->dev == CPU) {
        preprocess_cpu();
    } else {
        preprocess_gpu();
    }

    {
        std::lock_guard<std::mutex> lock(m_enqueue_mutex);
        auto start = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
        if (!m_context->enqueueV2((void**)m_bindingsOverride, m_stream, nullptr)){
            LOGE("Error happens during DNN inference part, program terminated");
        }
        CUDA_CHECK(cudaStreamSynchronize(m_stream));  // 不使用cuda event，在CPU端测试耗时，需要流同步
        auto end = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
        LOG("\t\t  Only-Inference:  %d us", (end - start).count());
    }

    if (m_params->dev == CPU) {
        postprocess_cpu();
    } else {
        postprocess_gpu();
    }
}

// this func will be called by other thread
bool FisheyeDetectorDetr::get_img_result(cv::Mat &img, std::string id) {
    int loop_cnt = 0;
    while (img_results.find(id) == img_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_img_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    img = img_results[id];
    img_results.erase(id);

    return true;
}

std::vector<std::vector<bbox>> FisheyeDetectorDetr::get_batch_obj_bboxes() {
    return m_batch_obj_bboxes;
}

std::vector<std::vector<cv::Mat>> FisheyeDetectorDetr::get_batch_cropped_images() {
    return m_batch_cropped_images;
}

shared_ptr<FisheyeDetectorDetr> make_fisheye_detector_detr(
    std::string onnx_path, logger::Level level, Params params)
{
    return make_shared<FisheyeDetectorDetr>(onnx_path, level, params);
}

}; // namespace detector
}; // namespace model
