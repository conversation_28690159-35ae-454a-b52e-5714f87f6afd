#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <set> // For target types and configured topics
#include <deque> // For image buffering
#include <mutex> // For thread safety of buffers
#include <cmath> // For std::abs
#include <limits> // For std::numeric_limits

// ROS
#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
// #include "std_msgs/msg/header.hpp" // No longer needed directly here

// Image Processing
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"
#include "image_cropping.hpp" // ImageCropper

// Object Processing
#include "esurfing/perc/group_detected.pb.h" // Protobuf definitions

// Shared Buffer
#include "image_buffer.hpp"
#include "image_buffer_manager.hpp"


// --- Typedefs ---
typedef sensor_msgs::msg::Image ImageMsg;
typedef std_msgs::msg::UInt8MultiArray RawObjectMsg; // Use standard type for raw objects
// typedef dev_pkg_interfaces::msg::ProtoWithHeader ObjectMsgWithHeader; // Removed

// --- Protobuf Namespace Aliases ---
using perception::detect::DetectedObject;
using perception::detect::DetectedObjects;

class ImageCropperNode : public rclcpp::Node
{
private:
    static const size_t DEFAULT_IMAGE_BUFFER_SIZE = 10; // Max images to store per topic buffer
    static const size_t DEFAULT_OBJECT_BUFFER_SIZE = 10;  // Max objects to store in object buffer
    static constexpr int64_t DEFAULT_TIME_TOLERANCE_MS = 50; // Default time tolerance for matching images

public:
    ImageCropperNode()
        : Node("image_cropper_node"),
          cropper_(0)
    {
        RCLCPP_INFO(this->get_logger(), "Initializing ImageCropperNode...");

        // --- Declare Parameters ---
        this->declare_parameter<std::vector<std::string>>("image_topics",
            std::vector<std::string>({"/image_topic_1"}));
        this->declare_parameter<std::string>("object_topic", "/objects_topic"); // Original raw topic
        this->declare_parameter<std::string>("buffer_name", "cropped_objects");
        this->declare_parameter<int>("buffer_size", 50); // Shared memory buffer size
        this->declare_parameter<double>("confidence_threshold", 0.5);
        this->declare_parameter<std::vector<int64_t>>("target_object_types", std::vector<int64_t>({1}));
        this->declare_parameter<int>("cropper_padding", 10);
        this->declare_parameter<int>("cleanup_max_age_sec", 60);
        this->declare_parameter<int>("cleanup_interval_sec", 30);
        // this->declare_parameter<std::string>("internal_object_topic", "/objects_topic_with_header"); // Removed
        this->declare_parameter<int64_t>("time_tolerance_ms", DEFAULT_TIME_TOLERANCE_MS);
        this->declare_parameter<int>("image_buffer_queue_size", (int)DEFAULT_IMAGE_BUFFER_SIZE);
        this->declare_parameter<int>("object_buffer_queue_size", (int)DEFAULT_OBJECT_BUFFER_SIZE);


        // --- Get Parameters ---
        image_topics_ = this->get_parameter("image_topics").as_string_array();
        std::string object_topic = this->get_parameter("object_topic").as_string(); // Renamed for clarity
        // internal_object_topic_ = this->get_parameter("internal_object_topic").as_string(); // Removed
        buffer_name_ = this->get_parameter("buffer_name").as_string();
        int shm_buffer_size = this->get_parameter("buffer_size").as_int();
        confidence_threshold_ = this->get_parameter("confidence_threshold").as_double();
        auto target_types_vec = this->get_parameter("target_object_types").as_integer_array();
        target_object_types_.insert(target_types_vec.begin(), target_types_vec.end());
        int padding = this->get_parameter("cropper_padding").as_int();
        cleanup_max_age_ns_ = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::seconds(this->get_parameter("cleanup_max_age_sec").as_int())).count();
        int cleanup_interval = this->get_parameter("cleanup_interval_sec").as_int();
        time_tolerance_ns_ = this->get_parameter("time_tolerance_ms").as_int() * 1'000'000LL;
        image_buffer_queue_size_ = this->get_parameter("image_buffer_queue_size").as_int();
        object_buffer_queue_size_ = this->get_parameter("object_buffer_queue_size").as_int();


        // --- Parameter Validation ---
        if (image_topics_.empty()) {
            RCLCPP_FATAL(this->get_logger(), "Parameter 'image_topics' cannot be empty. Please provide at least one image topic.");
            rclcpp::shutdown();
            return;
        }
        if (image_topics_.size() > 8) {
             RCLCPP_WARN(this->get_logger(), "More than 8 image topics configured (%zu). Ensure system resources are sufficient.", image_topics_.size());
        }
         RCLCPP_INFO(this->get_logger(), "Configured for %zu image topics.", image_topics_.size());

        if (shm_buffer_size <= 0) {
             RCLCPP_WARN(this->get_logger(), "Shared memory buffer size must be positive. Using default value 50.");
             shm_buffer_size = 50;
        }
        if (image_buffer_queue_size_ <= 0) {
            RCLCPP_WARN(this->get_logger(), "Image buffer queue size must be positive. Using default value %zu.", DEFAULT_IMAGE_BUFFER_SIZE);
            image_buffer_queue_size_ = DEFAULT_IMAGE_BUFFER_SIZE;
        }
        if (object_buffer_queue_size_ <= 0) {
            RCLCPP_WARN(this->get_logger(), "Object buffer queue size must be positive. Using default value %zu.", DEFAULT_OBJECT_BUFFER_SIZE);
            object_buffer_queue_size_ = DEFAULT_OBJECT_BUFFER_SIZE;
        }


        // --- Setup ---
        cropper_.setPadding(padding);
        manager_ = &ImageBufferManager::getInstance();
        configured_image_topics_.insert(image_topics_.begin(), image_topics_.end());

        // --- Initialize or Connect to Shared Memory Buffer ---
        RCLCPP_INFO(this->get_logger(), "Initializing/Connecting shared memory buffer: %s with size %d", buffer_name_.c_str(), shm_buffer_size);
        if (!manager_->initSharedMemory(buffer_name_, shm_buffer_size)) {
            RCLCPP_FATAL(this->get_logger(), "Failed to initialize or connect to shared memory buffer '%s'.", buffer_name_.c_str());
            rclcpp::shutdown();
            return;
        }
        image_buffer_ = manager_->getBuffer(buffer_name_);
        if (!image_buffer_) {
             RCLCPP_FATAL(this->get_logger(), "Failed to get buffer pointer for '%s' after init/connect.", buffer_name_.c_str());
             rclcpp::shutdown();
             return;
        }
         RCLCPP_INFO(this->get_logger(), "Successfully initialized/connected to buffer: %s", buffer_name_.c_str());

        // --- Create Publisher/Subscribers ---
        // obj_republisher_ = this->create_publisher<ObjectMsgWithHeader>(internal_object_topic_, 10); // Removed
        // RCLCPP_INFO(this->get_logger(), "Created internal publisher on topic '%s'", internal_object_topic_.c_str()); // Removed

        // Subscribe directly to the raw object topic and process in its callback
        obj_sub_ = this->create_subscription<RawObjectMsg>( // Renamed obj_raw_sub_ to obj_sub_
            object_topic, 10, // Use the configured object_topic
            std::bind(&ImageCropperNode::process_objects_callback, this, std::placeholders::_1)); // <<< Use the combined callback
         RCLCPP_INFO(this->get_logger(), "Created subscriber for raw objects on topic '%s'", object_topic.c_str());

        // obj_internal_sub_ = this->create_subscription<ObjectMsgWithHeader>(...); // Removed


        // --- Initialize Image Subscribers Dynamically ---
        image_subs_.clear();
        image_buffers_.clear();
        for (const auto& topic_name : image_topics_) {
            auto image_callback_lambda =
                [this, topic_name](const ImageMsg::ConstSharedPtr msg) {
                    this->image_callback(msg, topic_name);
                };
            auto sub = this->create_subscription<ImageMsg>(
                topic_name, rclcpp::SensorDataQoS(),
                image_callback_lambda);
            image_subs_[topic_name] = sub;
            image_buffers_[topic_name] = std::deque<ImageMsg::ConstSharedPtr>();
            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic_name.c_str());
        }
        RCLCPP_INFO(this->get_logger(), "Finished initializing %zu image subscribers.", image_subs_.size());


        // --- Initialize Cleanup Timer ---
        if (cleanup_interval > 0) {
             cleanup_timer_ = this->create_wall_timer(
                std::chrono::seconds(cleanup_interval),
                std::bind(&ImageCropperNode::cleanup_buffer_callback, this));
            RCLCPP_INFO(this->get_logger(), "Buffer cleanup timer started (Interval: %d s, Max Age: %ld s)",
                        cleanup_interval, this->get_parameter("cleanup_max_age_sec").as_int());
        } else {
             RCLCPP_WARN(this->get_logger(), "Buffer cleanup disabled (interval <= 0).");
        }

        RCLCPP_INFO(this->get_logger(), "ImageCropperNode initialization complete.");
    }

     ~ImageCropperNode() {
         if (manager_) {
             RCLCPP_INFO(this->get_logger(), "Cleaning up shared memory resources for %s as potential creator.", buffer_name_.c_str());
             manager_->cleanupSharedMemory(buffer_name_, true);
         }
         RCLCPP_INFO(this->get_logger(), "Shutting down ImageCropperNode.");
     }


private:
    // *** CALLBACK for individual image topics ***
    void image_callback(const ImageMsg::ConstSharedPtr& msg, const std::string& topic_name)
    {
        // --- Timing Start ---
        auto callback_start_time = std::chrono::steady_clock::now();
        // --------------------

        int64_t img_time_ns = rclcpp::Time(msg->header.stamp).nanoseconds();
        RCLCPP_DEBUG(this->get_logger(), "Received image from %s (TS_ns: %ld)",
                     topic_name.c_str(), img_time_ns);

        RCLCPP_WARN(this->get_logger(), "System timestamp now in image_callback (ms): %ld",
                    std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count());

        // Store image in buffer
        {
            std::lock_guard<std::mutex> lock(image_buffers_mutex_);

            if (image_buffers_.count(topic_name)) {
                auto& buffer = image_buffers_[topic_name];
                buffer.push_back(msg);

                while (buffer.size() > image_buffer_queue_size_) {
                    buffer.pop_front();
                    RCLCPP_DEBUG(this->get_logger(), "Removed oldest image from buffer %s (size now %zu)",
                                topic_name.c_str(), buffer.size());
                }
            } else {
                RCLCPP_WARN(this->get_logger(), "Received image from unexpected topic: %s", topic_name.c_str());
                return;
            }
        }

        // Process this image with available objects
        process_image_with_objects(msg, topic_name);
    }


    // *** CALLBACK: Stores raw object messages in buffer ***
    void process_objects_callback(const RawObjectMsg::ConstSharedPtr msg)
    {
        // --- Timing Start ---
        auto callback_start_time = std::chrono::steady_clock::now();
        // --------------------

        // 1. Parse Object Message
        DetectedObjects detected_objects_msg;
        if (!detected_objects_msg.ParseFromArray(msg->data.data(), msg->data.size())) {
            RCLCPP_WARN(this->get_logger(), "Failed to parse raw DetectedObjects message.");
            return;
        }

        // *** Extract timestamp directly from parsed protobuf message ***
        int64_t object_time_ms = detected_objects_msg.time_meas();
        int64_t object_time_ns = object_time_ms * 1'000'000LL; // Convert ms to ns
        // rclcpp::Time object_time = rclcpp::Time(object_time_ns); // Can create rclcpp::Time if needed

        RCLCPP_DEBUG(this->get_logger(), "Received raw object message, parsed TS_ns: %ld", object_time_ns);

        // --- Timing after Parse ---
        auto parse_end_time = std::chrono::steady_clock::now();
        // --------------------------

        if (detected_objects_msg.objects_size() == 0) {
            RCLCPP_DEBUG(this->get_logger(), "Received empty object list.");
            auto callback_end_time = std::chrono::steady_clock::now();
            auto total_duration = std::chrono::duration_cast<std::chrono::microseconds>(callback_end_time - callback_start_time);
            RCLCPP_DEBUG(this->get_logger(), "[Timing] Callback total: %ld us (empty object list)", total_duration.count());
            return;
        }

        // 2. Store object message in buffer
        {
            std::lock_guard<std::mutex> lock(object_buffer_mutex_);
            object_buffer_.push_back(std::make_pair(object_time_ns, detected_objects_msg));

            // Maintain buffer size
            while (object_buffer_.size() > object_buffer_queue_size_) {
                object_buffer_.pop_front();
                RCLCPP_DEBUG(this->get_logger(), "Removed oldest object message from buffer (size now %zu)", object_buffer_.size());
            }
        }

        // Log timing
        auto callback_end_time = std::chrono::steady_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::microseconds>(callback_end_time - callback_start_time);
        // RCLCPP_INFO(this->get_logger(), "[Timing] Object callback total: %ld us", total_duration.count());
    }

    // Process an image with available objects
    void process_image_with_objects(const ImageMsg::ConstSharedPtr& img_msg, const std::string& topic_name)
    {
        // --- Timing Start ---
        auto process_start_time = std::chrono::steady_clock::now();
        // --------------------

        int64_t img_time_ns = rclcpp::Time(img_msg->header.stamp).nanoseconds();

        int cropped_count = 0;
        int skipped_unconfigured_cam = 0;
        int skipped_no_object = 0;
        int skipped_confidence = 0;
        int skipped_type = 0;
        int skipped_invalid_roi = 0;
        int skipped_other = 0;

        // Variables to track total crop and buffer durations
        std::chrono::microseconds crop_duration(0);
        std::chrono::microseconds buffer_duration(0);

        // Find best matching objects for this image
        std::vector<std::pair<const DetectedObject*, int64_t>> matching_objects; // Object pointer and time diff

        // Lock object buffer to find matches
        {
            std::lock_guard<std::mutex> lock(object_buffer_mutex_);

            if (object_buffer_.empty()) {
                RCLCPP_DEBUG(this->get_logger(), "Object buffer is empty. Cannot process image from %s.", topic_name.c_str());
                return;
            }

            // Iterate through all objects in buffer to find matches for this image
            for (const auto& obj_pair : object_buffer_) {
                int64_t object_time_ns = obj_pair.first;
                const DetectedObjects& detected_objects = obj_pair.second;

                // Check each object in the message
                for (int i = 0; i < detected_objects.objects_size(); i++) {
                    const DetectedObject& obj = detected_objects.objects(i);

                    // Basic check
                    if (!obj.has_position_image() || !obj.has_shape_image() || obj.camera_name().empty()) {
                        continue;
                    }

                    // Filter by Camera Name - only process objects for this camera topic
                    if (obj.camera_name() != topic_name) {
                        continue;
                    }

                    // Filter by Confidence and Type
                    if (obj.confidence() < confidence_threshold_) {
                        continue;
                    }
                    if (target_object_types_.find(obj.type()) == target_object_types_.end()) {
                        continue;
                    }

                    // Calculate time difference
                    int64_t diff_ns = std::abs(img_time_ns - object_time_ns);

                    // Check if within tolerance
                    if (diff_ns <= time_tolerance_ns_) {
                        matching_objects.push_back(std::make_pair(&obj, diff_ns));
                    }
                }
            }
        }

        if (matching_objects.empty()) {
            RCLCPP_DEBUG(this->get_logger(), "No matching objects found for image from %s (TS_ns: %ld)",
                         topic_name.c_str(), img_time_ns);
            return;
        }

        RCLCPP_DEBUG(this->get_logger(), "Found %zu matching objects for image from %s",
                     matching_objects.size(), topic_name.c_str());

        // Convert Image once for all objects
        cv_bridge::CvImagePtr cv_ptr;
        try {
            cv_ptr = cv_bridge::toCvCopy(img_msg, sensor_msgs::image_encodings::BGR8);
        } catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "cv_bridge exception: %s", e.what());
            return;
        }
        if (!cv_ptr || cv_ptr->image.empty()) {
            RCLCPP_WARN(this->get_logger(), "Failed to convert image from topic %s", topic_name.c_str());
            return;
        }

        // Process each matching object
        for (const auto& obj_pair : matching_objects) {
            const DetectedObject* obj = obj_pair.first;
            int64_t diff_ns = obj_pair.second;

            // Crop Image
            auto crop_start_time = std::chrono::steady_clock::now();

            cv::Rect roi(static_cast<int>(obj->position_image().x()),
                         static_cast<int>(obj->position_image().y()),
                         static_cast<int>(obj->shape_image().x()),
                         static_cast<int>(obj->shape_image().y()));

            // Clamp ROI
            int orig_x = roi.x, orig_y = roi.y, orig_w = roi.width, orig_h = roi.height;
            roi.x = std::max(0, roi.x);
            roi.y = std::max(0, roi.y);
            roi.width = std::min(roi.width, cv_ptr->image.cols - roi.x);
            roi.height = std::min(roi.height, cv_ptr->image.rows - roi.y);

            if (roi.width <= 0 || roi.height <= 0) {
                RCLCPP_WARN(this->get_logger(), "Invalid ROI after clamping for object %lu on camera %s: Orig=[%d, %d, %d, %d], Clamped=[%d, %d, %d, %d] from image size [%d, %d]. Skipping.",
                            obj->uuid(), topic_name.c_str(), orig_x, orig_y, orig_w, orig_h, roi.x, roi.y, roi.width, roi.height, cv_ptr->image.cols, cv_ptr->image.rows);
                skipped_invalid_roi++;
                continue;
            } else if (roi.x != orig_x || roi.y != orig_y || roi.width != orig_w || roi.height != orig_h) {
                RCLCPP_DEBUG(this->get_logger(), "Clamped ROI for object %lu on camera %s: Orig=[%d, %d, %d, %d], Clamped=[%d, %d, %d, %d]",
                             obj->uuid(), topic_name.c_str(), orig_x, orig_y, orig_w, orig_h, roi.x, roi.y, roi.width, roi.height);
            }

            cv::Mat cropped_img = cropper_.cropSingle(cv_ptr->image, roi);
            if (cropped_img.empty()) {
                RCLCPP_WARN(this->get_logger(), "Cropping failed for object %lu from camera %s (ROI: [%d, %d, %d, %d])",
                            obj->uuid(), topic_name.c_str(), roi.x, roi.y, roi.width, roi.height);
                skipped_other++;
                continue;
            }

            auto crop_end_time = std::chrono::steady_clock::now();
            auto current_crop_duration = std::chrono::duration_cast<std::chrono::microseconds>(crop_end_time - crop_start_time);
            crop_duration += current_crop_duration;

            // Prepare Metadata
            model::ImageMetadata metadata;
            metadata.unique_id = obj->uuid();
            metadata.timestamp = img_time_ns; // Use image timestamp
            metadata.frame_id = topic_name;

            // Store in Buffer
            auto buffer_start_time = std::chrono::steady_clock::now();

            if (image_buffer_) {
                RCLCPP_DEBUG(this->get_logger(), "Adding cropped image for object %lu (cam %s) to buffer %s with time diff %.3f ms",
                             obj->uuid(), metadata.frame_id.c_str(), buffer_name_.c_str(), (double)diff_ns / 1.0e6);

                manager_->lockBuffer(buffer_name_);
                image_buffer_->add_image(cropped_img, metadata);
                bool write_ok = manager_->writeBufferToSharedMemory(buffer_name_);
                manager_->unlockBuffer(buffer_name_);

                if (!write_ok) {
                    RCLCPP_ERROR(this->get_logger(), "Failed to write buffer %s to shared memory after adding image!", buffer_name_.c_str());
                } else {
                    cropped_count++;
                }
            } else {
                RCLCPP_ERROR(this->get_logger(), "Shared image buffer '%s' is not available!", buffer_name_.c_str());
                skipped_other++;
            }

            auto buffer_end_time = std::chrono::steady_clock::now();
            auto current_buffer_duration = std::chrono::duration_cast<std::chrono::microseconds>(buffer_end_time - buffer_start_time);
            buffer_duration += current_buffer_duration;

            // Log individual object timing
            RCLCPP_INFO(this->get_logger(), "[Timing] Object %lu (cam %s): Crop=%ld us, Buffer=%ld us, TimeDiff=%.3f ms",
                        obj->uuid(), topic_name.c_str(),
                        current_crop_duration.count(), current_buffer_duration.count(), (double)diff_ns / 1.0e6);
        }

        // Log summary
        auto process_end_time = std::chrono::steady_clock::now();
        auto total_duration = std::chrono::duration_cast<std::chrono::microseconds>(process_end_time - process_start_time);

        int total_processed = matching_objects.size();
        int total_skipped = skipped_unconfigured_cam + skipped_no_object + skipped_confidence + skipped_type + skipped_invalid_roi + skipped_other;

        if (cropped_count > 0 || total_skipped > 0) {
            RCLCPP_INFO(this->get_logger(), "Processed image from %s (TS_ns: %ld): Matched=%d, Cropped=%d, Skipped=%d (ROI:%d, Other:%d)",
                        topic_name.c_str(), img_time_ns, total_processed, cropped_count, total_skipped,
                        skipped_invalid_roi, skipped_other);
        }

        // Calculate average crop and buffer durations if any objects were processed
        int64_t avg_crop_duration = 0;
        int64_t avg_buffer_duration = 0;
        if (cropped_count > 0) {
            avg_crop_duration = std::chrono::duration_cast<std::chrono::microseconds>(crop_duration).count() / cropped_count;
            avg_buffer_duration = std::chrono::duration_cast<std::chrono::microseconds>(buffer_duration).count() / cropped_count;
        }

        RCLCPP_INFO(this->get_logger(), "[Timing] Image processing (us): Total=%ld | AvgCrop=%ld | AvgBuffer=%ld",
                    total_duration.count(), avg_crop_duration, avg_buffer_duration);
    }


    // --- Buffer Cleanup ---
    void cleanup_buffer_callback()
    {
        RCLCPP_INFO(this->get_logger(), "Running buffer cleanup for '%s'...", buffer_name_.c_str());
        if (!image_buffer_ || !manager_) {
             RCLCPP_ERROR(this->get_logger(), "Cleanup failed: Buffer or manager not available.");
             return;
        }

        int initial_count = 0;
        int final_count = 0;
        int64_t current_time_ns = this->get_clock()->now().nanoseconds();
        int64_t cutoff_time_ns = current_time_ns - cleanup_max_age_ns_;

        manager_->lockBuffer(buffer_name_);

        std::vector<cv::Mat> current_images;
        std::vector<model::ImageMetadata> current_metadata;
        if (!image_buffer_->get_all_images(current_images, current_metadata)) {
             RCLCPP_WARN(this->get_logger(), "Cleanup: Failed to get current images/metadata from buffer '%s'.", buffer_name_.c_str());
             manager_->unlockBuffer(buffer_name_);
             return;
        }
        initial_count = current_metadata.size();

        std::vector<cv::Mat> filtered_images;
        std::vector<model::ImageMetadata> filtered_metadata;
        filtered_images.reserve(initial_count);
        filtered_metadata.reserve(initial_count);

        for (size_t i = 0; i < current_metadata.size(); ++i) {
            if (current_metadata[i].timestamp >= cutoff_time_ns) {
                filtered_images.push_back(current_images[i]);
                filtered_metadata.push_back(current_metadata[i]);
            }
        }
        final_count = filtered_metadata.size();

        if (final_count < initial_count) {
            RCLCPP_INFO(this->get_logger(), "Cleanup: Found %d images older than %ld seconds. Replacing buffer content.",
                        initial_count - final_count, cleanup_max_age_ns_ / 1'000'000'000L);

            if (!image_buffer_->add_images(filtered_images, filtered_metadata)) {
                 RCLCPP_ERROR(this->get_logger(), "Cleanup: Failed to replace buffer content for '%s'.", buffer_name_.c_str());
            } else {
                 if (!manager_->writeBufferToSharedMemory(buffer_name_)) {
                     RCLCPP_ERROR(this->get_logger(), "Cleanup: Failed to write buffer %s to shared memory after replacing content!", buffer_name_.c_str());
                 }
            }

        } else {
             RCLCPP_INFO(this->get_logger(), "Cleanup: No old images found to remove.");
        }

        manager_->unlockBuffer(buffer_name_);
        RCLCPP_INFO(this->get_logger(), "Buffer cleanup finished for '%s'. Initial: %d, Final: %d.", buffer_name_.c_str(), initial_count, final_count);
    }


    // --- Member Variables ---
    // Parameters
    std::vector<std::string> image_topics_;
    std::set<std::string> configured_image_topics_;
    // std::string internal_object_topic_; // Removed
    std::string buffer_name_;
    double confidence_threshold_;
    std::set<int64_t> target_object_types_;
    int64_t cleanup_max_age_ns_;
    int64_t time_tolerance_ns_;
    size_t image_buffer_queue_size_;
    size_t object_buffer_queue_size_;

    // ROS Pub/Sub
    rclcpp::Subscription<RawObjectMsg>::SharedPtr obj_sub_; // <<< Subscriber for raw objects, renamed
    // rclcpp::Publisher<ObjectMsgWithHeader>::SharedPtr obj_republisher_; // Removed
    // rclcpp::Subscription<ObjectMsgWithHeader>::SharedPtr obj_internal_sub_; // Removed
    std::map<std::string, rclcpp::Subscription<ImageMsg>::SharedPtr> image_subs_;

    // Image and Object Buffering
    std::map<std::string, std::deque<ImageMsg::ConstSharedPtr>> image_buffers_;
    std::deque<std::pair<int64_t, DetectedObjects>> object_buffer_; // timestamp_ns, objects
    std::mutex image_buffers_mutex_;
    std::mutex object_buffer_mutex_;

    // Tools
    model::ImageCropper cropper_;
    ImageBufferManager* manager_;
    std::shared_ptr<model::ImageBuffer> image_buffer_;

    // Timers
    rclcpp::TimerBase::SharedPtr cleanup_timer_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ImageCropperNode>();
    if (rclcpp::ok()) {
        rclcpp::spin(node);
    }
    rclcpp::shutdown();
    return 0;
}