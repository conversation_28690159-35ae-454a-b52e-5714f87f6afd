#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <fstream>

// ROS and Message Headers
#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/header.hpp" // Included for clarity, though implicitly by sensor_msgs/msg/image

// Image Processing Headers
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"

// Object Processing Headers
#include <yaml-cpp/yaml.h>
#include "esurfing/perc/group_detected.pb.h" // Protobuf definitions

using namespace std::chrono_literals;

// Protobuf Namespace Aliases
using perception::detect::DetectedObject;
using perception::detect::DetectedObjects;

// Structure to hold loaded object templates from YAML
struct ObjectTemplate {
    DetectedObject proto_object;
};

class ImageObjectPublisher : public rclcpp::Node
{
public:
    ImageObjectPublisher()
    : Node("img_obj_publisher_node")
    {
        // --- Declare Parameters ---
        // Image parameters
        this->declare_parameter<std::string>("image_path", "/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_det2d/data/source/plate_det/suzhou_3840_2160CrossImage2.jpg");
        this->declare_parameter<std::vector<std::string>>("image_topics",
            {"/image_topic_1"});
        this->declare_parameter<std::string>("frame_id", "camera_frame");

        // Object parameters
        this->declare_parameter<std::string>("config_path", "/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_tools/config/objects_config.yaml"); // Relative path expected
        this->declare_parameter<std::string>("object_topic", "/objects_topic");
        this->declare_parameter<std::string>("group_name", "default_group");

        // Common parameter
        this->declare_parameter<double>("publish_frequency", 10.0);

        // --- Get Parameters ---
        std::string image_path = this->get_parameter("image_path").as_string();
        this->get_parameter("image_topics", image_topics_);
        frame_id_ = this->get_parameter("frame_id").as_string();

        config_path_ = this->get_parameter("config_path").as_string();
        object_topic_ = this->get_parameter("object_topic").as_string();
        group_name_ = this->get_parameter("group_name").as_string();

        double frequency = this->get_parameter("publish_frequency").as_double();
        if (frequency <= 0) {
             RCLCPP_WARN(this->get_logger(), "Publish frequency must be positive, defaulting to 10.0 Hz.");
             frequency = 10.0;
        }
        auto publish_period = std::chrono::duration<double>(1.0 / frequency);

        // // --- Parameter Validation ---
        // if (image_topics_.size() != 4) {
        //     RCLCPP_ERROR(this->get_logger(), "Parameter 'image_topics' must contain exactly 4 topic names. Current count: %zu", image_topics_.size());
        //     rclcpp::shutdown();
        //     return;
        // }

        // --- Initialize Image Publishing ---
        cv::Mat image = cv::imread(image_path, cv::IMREAD_COLOR);
        if (image.empty()) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load image: %s", image_path.c_str());
            rclcpp::shutdown();
            return;
        }
        // Create image message template (timestamp set in callback)
        cv_bridge::CvImage cv_image;
        cv_image.header.frame_id = frame_id_;
        cv_image.encoding = sensor_msgs::image_encodings::BGR8;
        cv_image.image = image;
        image_msg_template_ = *cv_image.toImageMsg();
        RCLCPP_INFO(this->get_logger(), "Successfully loaded image: %s", image_path.c_str());

        // Create image publishers
        for (size_t i = 0; i < image_topics_.size(); ++i) {
            image_publishers_[i] = this->create_publisher<sensor_msgs::msg::Image>(image_topics_[i], 10);
            RCLCPP_INFO(this->get_logger(), "Created image publisher: %s", image_topics_[i].c_str());
        }

        // --- Initialize Object Publishing ---
        if (!load_object_config(config_path_)) {
            RCLCPP_ERROR(this->get_logger(), "Failed to load or parse object config file: %s", config_path_.c_str());
            rclcpp::shutdown();
            return;
        }
        // Create object publisher
        object_publisher_ = this->create_publisher<std_msgs::msg::UInt8MultiArray>(object_topic_, 10);
        RCLCPP_INFO(this->get_logger(), "Created object publisher: %s", object_topic_.c_str());


        // --- Create Timer ---
        timer_ = this->create_wall_timer(
            publish_period, std::bind(&ImageObjectPublisher::timer_callback, this));

        RCLCPP_INFO(this->get_logger(), "Image and Object publisher node initialized. Frequency: %.1f Hz", frequency);
    }

private:
    // --- Helper to load object configuration from YAML ---
    bool load_object_config(const std::string& path) {
       try {
            YAML::Node config = YAML::LoadFile(path);
            if (!config["objects"]) {
                RCLCPP_ERROR(this->get_logger(), "Config file '%s' missing 'objects' key", path.c_str());
                return false;
            }

            const YAML::Node& objects_yaml = config["objects"];
            if (!objects_yaml.IsSequence()) {
                 RCLCPP_ERROR(this->get_logger(), "'objects' key must be a list in '%s'", path.c_str());
                 return false;
            }

            object_templates_.clear();
            for (const auto& obj_node : objects_yaml) {
                ObjectTemplate obj_template;
                DetectedObject& obj = obj_template.proto_object;

                // --- Required Fields ---
                if (!obj_node["uuid"] || !obj_node["type"]) {
                    RCLCPP_WARN(this->get_logger(), "Skipping object definition missing 'uuid' or 'type'");
                    continue;
                }
                obj.set_uuid(obj_node["uuid"].as<uint64_t>());
                obj.set_type(obj_node["type"].as<int32_t>());

                // --- Optional Fields ---
                if (obj_node["overlap_name"]) {
                    obj.set_overlap_name(obj_node["overlap_name"].as<std::string>()); // bytes stored as string
                }
                obj.set_confidence(obj_node["confidence"] ? obj_node["confidence"].as<float>() : 1.0f);

                if (obj_node["position"]) {
                    auto* pos = obj.mutable_position();
                    pos->set_x(obj_node["position"]["x"].as<float>());
                    pos->set_y(obj_node["position"]["y"].as<float>());
                    pos->set_z(obj_node["position"]["z"].as<float>());
                }
                if (obj_node["shape"]) {
                     auto* shape = obj.mutable_shape();
                     shape->set_x(obj_node["shape"]["x"].as<float>()); // length
                     shape->set_y(obj_node["shape"]["y"].as<float>()); // width
                     shape->set_z(obj_node["shape"]["z"].as<float>()); // height
                }
                if (obj_node["hull"] && obj_node["hull"].IsSequence()) {
                    auto* hull_proto = obj.mutable_hull();
                    for(const auto& pt_node : obj_node["hull"]) {
                        if (pt_node["x"] && pt_node["y"]) {
                            auto* hull_pt = hull_proto->add_points();
                            hull_pt->set_x(pt_node["x"].as<float>());
                            hull_pt->set_y(pt_node["y"].as<float>());
                        }
                    }
                }

                obj.set_orientation(obj_node["orientation"] ? obj_node["orientation"].as<float>() : 0.0f);

                if (obj_node["velocity"]) {
                    auto* vel = obj.mutable_velocity();
                    vel->set_heading(obj_node["velocity"]["heading"] ? obj_node["velocity"]["heading"].as<float>() : 0.0f);
                    vel->set_speed(obj_node["velocity"]["speed"] ? obj_node["velocity"]["speed"].as<float>() : 0.0f);
                    vel->set_acceleration(obj_node["velocity"]["acceleration"] ? obj_node["velocity"]["acceleration"].as<float>() : 0.0f);
                }

                obj.set_is_static(obj_node["is_static"] ? obj_node["is_static"].as<bool>() : false);

                if (obj_node["color"]) {
                    auto* color = obj.mutable_color();
                    color->set_r(obj_node["color"]["r"] ? obj_node["color"]["r"].as<uint32_t>() : 0);
                    color->set_g(obj_node["color"]["g"] ? obj_node["color"]["g"].as<uint32_t>() : 0);
                    color->set_b(obj_node["color"]["b"] ? obj_node["color"]["b"].as<uint32_t>() : 0);
                    color->set_a(obj_node["color"]["a"] ? obj_node["color"]["a"].as<float>()    : 1.0f);
                }

                if (obj_node["feature"] && obj_node["feature"].IsSequence()) {
                    for (const auto& feat_val : obj_node["feature"]) {
                        obj.add_feature(feat_val.as<float>());
                    }
                }

                // Note: Parsing trajectories is complex, requires nested structures in YAML
                // Example YAML structure for trajectories:
                // trajectories:
                //   - probability: 0.9
                //     waypoints:
                //       - time_meas: 1678886400000 # ms
                //         position: { x: 1.0, y: 2.0, z: 0.0 }
                //         velocity: { heading: 0.1, speed: 1.5, acceleration: 0.0 }
                //       - time_meas: 1678886400100 # ms
                //         position: { x: 1.1, y: 2.1, z: 0.0 }
                //         velocity: { heading: 0.1, speed: 1.5, acceleration: 0.0 }
                //   - probability: 0.1
                //     waypoints: [...]
                if (obj_node["trajectories"] && obj_node["trajectories"].IsSequence()) {
                     for (const auto& traj_node : obj_node["trajectories"]) {
                        auto* traj_proto = obj.add_trajectories();
                        traj_proto->set_probability(traj_node["probability"] ? traj_node["probability"].as<float>() : 0.0f);
                        if (traj_node["waypoints"] && traj_node["waypoints"].IsSequence()) {
                            for (const auto& wp_node : traj_node["waypoints"]) {
                                auto* wp_proto = traj_proto->add_waypoints();
                                wp_proto->set_time_meas(wp_node["time_meas"] ? wp_node["time_meas"].as<int64_t>() : 0);
                                if (wp_node["position"]) {
                                    auto* pos = wp_proto->mutable_position();
                                    pos->set_x(wp_node["position"]["x"].as<float>());
                                    pos->set_y(wp_node["position"]["y"].as<float>());
                                    pos->set_z(wp_node["position"]["z"].as<float>());
                                }
                                if (wp_node["velocity"]) {
                                     auto* vel = wp_proto->mutable_velocity();
                                    vel->set_heading(wp_node["velocity"]["heading"] ? wp_node["velocity"]["heading"].as<float>() : 0.0f);
                                    vel->set_speed(wp_node["velocity"]["speed"] ? wp_node["velocity"]["speed"].as<float>() : 0.0f);
                                    vel->set_acceleration(wp_node["velocity"]["acceleration"] ? wp_node["velocity"]["acceleration"].as<float>() : 0.0f);
                                }
                            }
                        }
                     }
                }


                if (obj_node["str_array"] && obj_node["str_array"].IsSequence()) {
                     for (const auto& str_val : obj_node["str_array"]) {
                        obj.add_str_array(str_val.as<std::string>()); // bytes stored as string
                    }
                }

                if (obj_node["int_array"] && obj_node["int_array"].IsSequence()) {
                     for (const auto& int_val : obj_node["int_array"]) {
                        obj.add_int_array(int_val.as<int32_t>());
                    }
                }

                obj.set_parking_time(obj_node["parking_time"] ? obj_node["parking_time"].as<int64_t>() : 0);

                if (obj_node["plate"]) {
                    auto* plate_proto = obj.mutable_plate();
                    plate_proto->set_color(obj_node["plate"]["color"] ? obj_node["plate"]["color"].as<int32_t>() : 0);
                    plate_proto->set_color_confidence(obj_node["plate"]["color_confidence"] ? obj_node["plate"]["color_confidence"].as<float>() : 0.0f);
                    plate_proto->set_number(obj_node["plate"]["number"] ? obj_node["plate"]["number"].as<std::string>() : "");
                    plate_proto->set_number_confidence(obj_node["plate"]["number_confidence"] ? obj_node["plate"]["number_confidence"].as<float>() : 0.0f);
                }

                obj.set_obj_color(obj_node["obj_color"] ? obj_node["obj_color"].as<int32_t>() : 0); // Assuming color codes are int
                if (obj_node["group_id"]) {
                     obj.set_group_id(obj_node["group_id"].as<std::string>());
                }
                 obj.set_obj_color_conf(obj_node["obj_color_conf"] ? obj_node["obj_color_conf"].as<float>() : 0.0f);

                if (obj_node["track_conf"]) {
                    auto* track_conf_proto = obj.mutable_track_conf();
                    track_conf_proto->set_color(obj_node["track_conf"]["color"] ? obj_node["track_conf"]["color"].as<uint32_t>() : 0);
                    track_conf_proto->set_type(obj_node["track_conf"]["type"] ? obj_node["track_conf"]["type"].as<uint32_t>() : 0);
                    track_conf_proto->set_plate_number(obj_node["track_conf"]["plate_number"] ? obj_node["track_conf"]["plate_number"].as<uint32_t>() : 0);
                    track_conf_proto->set_plate_color(obj_node["track_conf"]["plate_color"] ? obj_node["track_conf"]["plate_color"].as<uint32_t>() : 0);
                }

                if (obj_node["camera_name"]) {
                     obj.set_camera_name(obj_node["camera_name"].as<std::string>());
                }

                if (obj_node["position_image"]) {
                    auto* pos_img = obj.mutable_position_image();
                    pos_img->set_x(obj_node["position_image"]["x"].as<float>());
                    pos_img->set_y(obj_node["position_image"]["y"].as<float>());
                    pos_img->set_z(obj_node["position_image"]["z"].as<float>()); // Often 1 or similar
                }
                 if (obj_node["shape_image"]) {
                    auto* shape_img = obj.mutable_shape_image();
                    shape_img->set_x(obj_node["shape_image"]["x"].as<float>()); // width
                    shape_img->set_y(obj_node["shape_image"]["y"].as<float>()); // height
                }


                object_templates_.push_back(obj_template);
            }
            RCLCPP_INFO(this->get_logger(), "Successfully loaded %zu object templates from '%s'", object_templates_.size(), path.c_str());
            return true;

        } catch (const YAML::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error parsing YAML file '%s': %s", path.c_str(), e.what());
            return false;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error loading config file '%s': %s", path.c_str(), e.what());
            return false;
        }
    }

    // --- Timer Callback ---
    void timer_callback()
    {
        // Get current time ONCE for both message types
        rclcpp::Time now = this->get_clock()->now();
        int64_t now_ms = now.nanoseconds() / 1'000'000; // For Protobuf timestamp

        // --- Publish Images ---
        image_msg_template_.header.stamp = now; // Set timestamp for image message
        for (size_t i = 0; i < 4; ++i) {
             if (image_publishers_[i]) {
                image_publishers_[i]->publish(image_msg_template_);
             }
        }

        // --- Publish Objects ---
        // 1. Create DetectedObjects message
        DetectedObjects detected_objects_msg;
        detected_objects_msg.set_time_meas(now_ms); // Use consistent timestamp in ms
        detected_objects_msg.set_time_pub(now_ms);  // Use consistent timestamp in ms
        detected_objects_msg.set_group_name(group_name_);
        // detected_objects_msg.set_group_info(...); // Optional

        // 2. Populate from templates
        for (const auto& tmpl : object_templates_) {
             *detected_objects_msg.add_objects() = tmpl.proto_object;
             // Add logic here if object state needs modification based on 'now'
        }

        // 3. Serialize Protobuf message
        std::string serialized_data;
        if (!detected_objects_msg.SerializeToString(&serialized_data)) {
            RCLCPP_WARN(this->get_logger(), "Failed to serialize DetectedObjects message");
            return; // Skip object publishing if serialization fails
        }

        // 4. Create and fill UInt8MultiArray message
        std_msgs::msg::UInt8MultiArray output_msg;
        output_msg.layout.dim.push_back(std_msgs::msg::MultiArrayDimension());
        output_msg.layout.dim[0].size = serialized_data.size();
        output_msg.layout.dim[0].stride = 1;
        output_msg.layout.dim[0].label = "protobuf_bytes";
        output_msg.data.assign(serialized_data.begin(), serialized_data.end());

        // 5. Publish object message
        if(object_publisher_) {
            object_publisher_->publish(output_msg);
        }

        // Optional: Log publish action (can be verbose)
        // RCLCPP_DEBUG(this->get_logger(), "Published images and objects with timestamp %ld ms", now_ms);
    }

    // --- Member Variables ---
    rclcpp::TimerBase::SharedPtr timer_;

    // Image related members
    sensor_msgs::msg::Image image_msg_template_;
    rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr image_publishers_[4];
    std::vector<std::string> image_topics_;
    std::string frame_id_;

    // Object related members
    rclcpp::Publisher<std_msgs::msg::UInt8MultiArray>::SharedPtr object_publisher_;
    std::string config_path_;
    std::string object_topic_;
    std::string group_name_;
    std::vector<ObjectTemplate> object_templates_;
};

int main(int argc, char * argv[])
{
    // GOOGLE_PROTOBUF_VERIFY_VERSION; // Optional

    rclcpp::init(argc, argv);
    auto node = std::make_shared<ImageObjectPublisher>();
    if (rclcpp::ok()) {
        rclcpp::spin(node);
    }
    rclcpp::shutdown();

    // google::protobuf::ShutdownProtobufLibrary(); // Optional
    return 0;
} 