#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <fstream>
#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/u_int8_multi_array.hpp>
#include <yaml-cpp/yaml.h>
#include "esurfing/perc/group.pb.h"
#include "esurfing/perc/group_detected.pb.h"
#include "esurfing/math/color.pb.h"
#include "esurfing/math/geo.pb.h"

using namespace std::chrono_literals;
using perception::detect::DetectedObject;
using perception::detect::DetectedObjects;


// 用于存储从 YAML 加载的对象模板
struct ObjectTemplate {
    DetectedObject proto_object; // 存储从 YAML 解析出的基本对象信息
};

class ObjectPublisher : public rclcpp::Node
{
public:
    ObjectPublisher()
    : Node("object_publisher_node")
    {
        // 声明参数
        this->declare_parameter<std::string>("config_path", "/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_tools/config/objects_config.yaml"); // 配置文件路径
        this->declare_parameter<std::string>("output_topic", "/objects_topic"); // 输出 topic
        this->declare_parameter<std::string>("group_name", "default_group");
        this->declare_parameter<double>("publish_frequency", 10.0);

        // 获取参数
        config_path_ = this->get_parameter("config_path").as_string();
        output_topic_ = this->get_parameter("output_topic").as_string();
        group_name_ = this->get_parameter("group_name").as_string();
        double frequency = this->get_parameter("publish_frequency").as_double();

        if (frequency <= 0) {
             RCLCPP_WARN(this->get_logger(), "Publish frequency must be positive, defaulting to 10.0 Hz.");
             frequency = 10.0;
        }
        auto publish_period = std::chrono::duration<double>(1.0 / frequency);


        // 加载配置
        if (!load_config(config_path_)) {
            RCLCPP_ERROR(this->get_logger(), "无法加载或解析配置文件: %s", config_path_.c_str());
            rclcpp::shutdown();
            return;
        }

        // 创建发布者
        publisher_ = this->create_publisher<std_msgs::msg::UInt8MultiArray>(output_topic_, 10);
        RCLCPP_INFO(this->get_logger(), "将在话题 '%s' 上发布对象数据", output_topic_.c_str());

        // 创建定时器
        timer_ = this->create_wall_timer(
            publish_period, std::bind(&ObjectPublisher::timer_callback, this));

        RCLCPP_INFO(this->get_logger(), "对象发布节点初始化完成，频率: %.1f Hz", frequency);
    }

private:
    bool load_config(const std::string& path) {
        try {
            YAML::Node config = YAML::LoadFile(path);
            if (!config["objects"]) {
                RCLCPP_ERROR(this->get_logger(), "配置文件 '%s' 中缺少 'objects' 键", path.c_str());
                return false;
            }

            const YAML::Node& objects_yaml = config["objects"];
            if (!objects_yaml.IsSequence()) {
                 RCLCPP_ERROR(this->get_logger(), "'objects' 键的值必须是一个列表", path.c_str());
                 return false;
            }

            object_templates_.clear();
            for (const auto& obj_node : objects_yaml) {
                ObjectTemplate obj_template;
                DetectedObject& obj = obj_template.proto_object;

                if (!obj_node["uuid"] || !obj_node["type"]) {
                    RCLCPP_WARN(this->get_logger(), "跳过缺少 'uuid' 或 'type' 的对象定义");
                    continue;
                }

                obj.set_uuid(obj_node["uuid"].as<uint64_t>());
                obj.set_type(obj_node["type"].as<int32_t>());
                obj.set_confidence(obj_node["confidence"] ? obj_node["confidence"].as<float>() : 1.0f);

                if (obj_node["position"]) {
                    auto* pos = obj.mutable_position();
                    pos->set_x(obj_node["position"]["x"].as<float>());
                    pos->set_y(obj_node["position"]["y"].as<float>());
                    pos->set_z(obj_node["position"]["z"].as<float>());
                }
                if (obj_node["shape"]) {
                     auto* shape = obj.mutable_shape();
                     shape->set_x(obj_node["shape"]["x"].as<float>()); // length
                     shape->set_y(obj_node["shape"]["y"].as<float>()); // width
                     shape->set_z(obj_node["shape"]["z"].as<float>()); // height
                }
                obj.set_orientation(obj_node["orientation"] ? obj_node["orientation"].as<float>() : 0.0f);

                if (obj_node["velocity"]) {
                    auto* vel = obj.mutable_velocity();
                    vel->set_heading(obj_node["velocity"]["heading"] ? obj_node["velocity"]["heading"].as<float>() : 0.0f);
                    vel->set_speed(obj_node["velocity"]["speed"] ? obj_node["velocity"]["speed"].as<float>() : 0.0f);
                    vel->set_acceleration(obj_node["velocity"]["acceleration"] ? obj_node["velocity"]["acceleration"].as<float>() : 0.0f);
                }

                obj.set_is_static(obj_node["is_static"] ? obj_node["is_static"].as<bool>() : false);

                if (obj_node["color"]) {
                    auto* color = obj.mutable_color();
                    color->set_r(obj_node["color"]["r"].as<uint32_t>());
                    color->set_g(obj_node["color"]["g"].as<uint32_t>());
                    color->set_b(obj_node["color"]["b"].as<uint32_t>());
                    color->set_a(obj_node["color"]["a"] ? obj_node["color"]["a"].as<uint32_t>() : 255);
                }
                 // 可以根据需要添加 YAML 中其他 DetectedObject 字段的解析
                 // 例如：plate, obj_color, group_id 等

                object_templates_.push_back(obj_template);
            }
            RCLCPP_INFO(this->get_logger(), "从 '%s' 成功加载 %zu 个对象模板", path.c_str(), object_templates_.size());
            return true;

        } catch (const YAML::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "解析 YAML 文件 '%s' 时出错: %s", path.c_str(), e.what());
            return false;
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "加载配置文件 '%s' 时发生错误: %s", path.c_str(), e.what());
            return false;
        }
    }


    void timer_callback()
    {
        // 1. 创建 DetectedObjects 消息
        DetectedObjects detected_objects_msg;

        // 设置时间戳 (ms)
        auto now = this->get_clock()->now();
        int64_t now_ms = now.nanoseconds() / 1'000'000;
        detected_objects_msg.set_time_meas(now_ms); // 假设测量时间等于发布时间
        detected_objects_msg.set_time_pub(now_ms);
        detected_objects_msg.set_group_name(group_name_);

        // 2. 从模板填充 DetectedObject 列表
        for (const auto& tmpl : object_templates_) {
            // 从模板创建一个新的 DetectedObject 实例
            // 这里我们直接使用模板中的对象，因为每次发布时都会重新序列化
            // 如果需要修改对象状态（例如位置随时间变化），则应在此处进行
             *detected_objects_msg.add_objects() = tmpl.proto_object; // 高效添加
        }

        // 3. 序列化 Protobuf 消息
        std::string serialized_data;
        if (!detected_objects_msg.SerializeToString(&serialized_data)) {
            RCLCPP_WARN(this->get_logger(), "序列化 DetectedObjects 消息失败");
            return;
        }

        // 4. 创建并填充 UInt8MultiArray 消息
        std_msgs::msg::UInt8MultiArray output_msg;
        output_msg.layout.dim.push_back(std_msgs::msg::MultiArrayDimension());
        output_msg.layout.dim[0].size = serialized_data.size();
        output_msg.layout.dim[0].stride = 1;
        output_msg.layout.dim[0].label = "protobuf_bytes"; // 可选标签
        output_msg.data.assign(serialized_data.begin(), serialized_data.end());


        // 5. 发布消息
        publisher_->publish(output_msg);
    }

    rclcpp::TimerBase::SharedPtr timer_;
    rclcpp::Publisher<std_msgs::msg::UInt8MultiArray>::SharedPtr publisher_;
    std::string config_path_;
    std::string output_topic_;
    std::string group_name_;
    std::vector<ObjectTemplate> object_templates_; // 存储加载的对象定义
};

int main(int argc, char * argv[])
{
    // 初始化 Protobuf 库 (如果需要，通常在依赖库中完成)
    // GOOGLE_PROTOBUF_VERIFY_VERSION;

    rclcpp::init(argc, argv);
    auto node = std::make_shared<ObjectPublisher>();
     if (rclcpp::ok()) { // 检查节点是否成功初始化
        rclcpp::spin(node);
    }
    rclcpp::shutdown();

    // 关闭 Protobuf 库 (如果需要)
    // google::protobuf::ShutdownProtobufLibrary();
    return 0;
} 