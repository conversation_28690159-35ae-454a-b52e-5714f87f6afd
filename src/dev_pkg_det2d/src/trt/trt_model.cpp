#include "trt_model.hpp"
#include "utils.hpp" 
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include "trt_calibrator.hpp"
#include <string>

using namespace std;
using namespace nvinfer1;
using namespace nvonnxparser;

namespace model{

Model::Model(string onnx_path, logger::Level level, Params params) {
    m_onnxPath      = onnx_path;
    m_workspaceSize = WORKSPACESIZE;
    m_logger        = make_shared<logger::Logger>(level);
    // m_timer         = make_shared<timer::Timer>();
    // m_params        = new Params(params);
    m_params        = make_shared<Params>(params);
    m_enginePath    = changePath(onnx_path, "../engine", ".engine", getPrec(params.prec));
    // init m_frame_id
    m_frame_id      = "";
}

void Model::load_image(string image_path) {
    if (!fileExists(image_path)){
        LOGE("%s not found", image_path.c_str());
    } else {
        m_imagePath = image_path;
        LOG("\t**************************** INFERENCE INFORMATION ****************************");
        LOG("\tModel:      %s", getFileName(m_onnxPath).c_str());
        LOG("\tImage:      %s", getFileName(m_imagePath).c_str());
        LOG("\tPrecision:  %s", getPrec(m_params->prec).c_str());
        m_inputImage = cv::imread(m_imagePath);
    }
}

void Model::load_image(std::string frame_id, const cv::Mat& image_cap) {
    if (image_cap.empty()) {
        LOGE("no image captured to load");
    } else {
        m_frame_id = frame_id;
        m_inputImage = image_cap.clone();
    }
}

void Model::load_images(std::string image_path) {
    if (!fileExists(image_path)){
        LOGE("%s not found", image_path.c_str());
    } else {
        m_imagePath = image_path;
        m_inputImages.clear();
        std::vector<std::string> image_paths = get_image_paths(m_imagePath);
        for (int i = 0; i < image_paths.size(); i++) {
            cv::Mat input_image = cv::imread(image_paths[i]);
            if (input_image.data == nullptr) {
                LOGE("%s not found", image_paths[i].c_str());
            }
            m_inputImages.push_back(input_image);
            LOG("\tImage %d: %s", i, image_paths[i].c_str());
        }
        m_activedBatchSize = m_inputImages.size();
        if (m_activedBatchSize > m_params->batch_size) {
            LOGW("WARNING: batch_size is smaller than the number of images, set m_activedBatchSize to %d", m_params->batch_size);
            m_activedBatchSize = m_params->batch_size;
        }
        LOG("\tTotal batch_size: %d", m_params->batch_size);
        LOG("\tActived_batch_size: %d", m_activedBatchSize);
    }
}

void Model::set_image_buffer(std::shared_ptr<ImageBuffer> buffer) {
    m_imageBuffer = buffer;
}

bool Model::load_image_from_buffer(int64_t image_id) {
    if (!m_imageBuffer) return false;
    
    ImageMetadata metadata;
    cv::Mat image;
    
    if (m_imageBuffer->get_image(image_id, image, metadata)) {
        m_frame_id = metadata.frame_id;
        m_inputImage = image;
        return true;
    }
    
    return false;
}

bool Model::load_latest_image_from_buffer() {
    if (!m_imageBuffer) return false;
    
    ImageMetadata metadata;
    cv::Mat image;
    
    if (m_imageBuffer->get_latest_image(image, metadata)) {
        m_frame_id = metadata.frame_id;
        m_inputImage = image;
        return true;
    }
    
    return false;
}

void Model::init_model() {
    /* 一个model的engine, context这些一旦创建好了，当多次调用这个模型的时候就没必要每次都初始化了*/
    if (m_context == nullptr){
        if (!fileExists(m_enginePath)){
            LOG("%s not found. Building trt engine...", m_enginePath.c_str());
            build_engine();
        } else {
            LOG("%s has been generated! loading trt engine...", m_enginePath.c_str());
            load_engine();
        }
    }else{
        // m_timer->init();
        reset_task();
    }
}

bool Model::build_engine() {
    if (m_params->use_dla) {
        return build_engine_with_dla();
    } else {
        return build_engine_only_gpu();
    }
}

bool Model::build_engine_only_gpu() {
    // 我们也希望在build一个engine的时候就把一系列初始化全部做完，其中包括
    //  1. build一个engine
    //  2. 创建一个context
    //  3. 创建推理所用的stream
    //  4. 创建推理所需要的device空间
    // 这样，我们就可以在build结束以后，就可以直接推理了。这样的写法会比较干净
    auto builder       = shared_ptr<IBuilder>(createInferBuilder(*m_logger), destroy_trt_ptr<IBuilder>);
    auto network       = shared_ptr<INetworkDefinition>(builder->createNetworkV2(1), destroy_trt_ptr<INetworkDefinition>);
    auto config        = shared_ptr<IBuilderConfig>(builder->createBuilderConfig(), destroy_trt_ptr<IBuilderConfig>);
    auto parser        = shared_ptr<IParser>(createParser(*network, *m_logger), destroy_trt_ptr<IParser>);

    config->setMaxWorkspaceSize(m_workspaceSize);
    config->setProfilingVerbosity(ProfilingVerbosity::kDETAILED); //这里也可以设置为kDETAIL;

    // // if Ampere GPU
    // config->clearFlag(BuilderFlag::kTF32);

    if (!parser->parseFromFile(m_onnxPath.c_str(), 1)){
        return false;
    }

    if (builder->platformHasFastFp16() && m_params->prec == model::FP16) {
        config->setFlag(BuilderFlag::kFP16);
        config->setFlag(BuilderFlag::kPREFER_PRECISION_CONSTRAINTS);
        if (m_params->det_model == model::detection_model::DETR) {
            for (int i = 0; i < network->getNbLayers(); i++) {
                auto layer = network->getLayer(i);
                if (layer->getType() == LayerType::kSOFTMAX || layer->getType() == LayerType::kNORMALIZATION) {
                    layer->setPrecision(DataType::kFLOAT);
                }
            }
        }
    } else if (builder->platformHasFastInt8() && m_params->prec == model::INT8) {
        config->setFlag(BuilderFlag::kFP16);
        config->setFlag(BuilderFlag::kINT8);
        config->setFlag(BuilderFlag::kPREFER_PRECISION_CONSTRAINTS);
        if (m_params->det_model == model::detection_model::DETR) {
            for (int i = 0; i < network->getNbLayers(); i++) {
                auto layer = network->getLayer(i);
                if (layer->getType() == LayerType::kSOFTMAX || layer->getType() == LayerType::kNORMALIZATION) {
                    layer->setPrecision(DataType::kFLOAT);
                }
            }
        }
    }

    std::string calib_table_name;
    switch(m_params->det_model) {
        case model::detection_model::YOLOV5:
            calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov5.txt";
            break;
        case model::detection_model::YOLOV7:
            calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov7.txt";
            break;
        case model::detection_model::YOLOV8:
            calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov8.txt";
            break;
    }

    shared_ptr<Int8EntropyCalibrator> calibrator(new Int8EntropyCalibrator(
        m_params->batch_size, 
        (m_params == nullptr ? "src/dev_pkg_det2d/calibration/calibration_list_coco.txt" : m_params->calib_data_file.c_str()), 
        (m_params == nullptr ? calib_table_name.c_str() : m_params->calib_table_file.c_str()),
        m_params->img.c * m_params->img.h * m_params->img.w, 
        m_params->img.h, 
        m_params->img.w, 
        m_params->task));
    config->setInt8Calibrator(calibrator.get());

    auto engine        = shared_ptr<ICudaEngine>(builder->buildEngineWithConfig(*network, *config), destroy_trt_ptr<ICudaEngine>);
    auto plan          = builder->buildSerializedNetwork(*network, *config);
    auto runtime       = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);

    // 保存序列化后的engine
    save_plan(*plan);

    // 根据runtime初始化engine, context, 以及memory
    setup(plan->data(), plan->size());

    // 把优化前和优化后的各个层的信息打印出来
    LOGV("Before TensorRT optimization");
    print_network(*network, false);
    LOGV("After TensorRT optimization");
    print_network(*network, true);

    return true;
}

bool Model::build_engine_with_dla() {
    // auto builder = shared_ptr<IBuilder>(createInferBuilder(*m_logger), destroy_trt_ptr<IBuilder>);
    // // 关键修改1：显式启用EXPLICIT_BATCH标志
    // const auto explicitBatchFlag = 1U << static_cast<uint32_t>(NetworkDefinitionCreationFlag::kEXPLICIT_BATCH);
    // auto network = shared_ptr<INetworkDefinition>(
    //     builder->createNetworkV2(explicitBatchFlag),  // 替换原来的硬编码1
    //     destroy_trt_ptr<INetworkDefinition>
    // );
    // auto config = shared_ptr<IBuilderConfig>(builder->createBuilderConfig(), destroy_trt_ptr<IBuilderConfig>);
    // auto parser = shared_ptr<IParser>(createParser(*network, *m_logger), destroy_trt_ptr<IParser>);
    // // 关键修改2：显式配置内存池
    // config->setMaxWorkspaceSize(m_workspaceSize);
    // // config->setMemoryPoolLimit(MemoryPoolType::kDLA_MANAGED_SRAM, 1 << 20);  // 设置DLA SRAM内存池大小为1 MiB (max)
    // // config->setMemoryPoolLimit(MemoryPoolType::kDLA_LOCAL_DRAM, 32 << 20);   // 设置DLA DRAM内存池大小为32 MiB (max)
    // // config->setMemoryPoolLimit(MemoryPoolType::kDLA_GLOBAL_DRAM, 32 << 20); // 设置DLA全局DRAM内存池大小为128 MiB (max)
    // config->setProfilingVerbosity(ProfilingVerbosity::kDETAILED); // 这里也可以设置为kDETAIL;
    // // 启用严格类型检查
    // config->setFlag(BuilderFlag::kSTRICT_TYPES);
    // if (!parser->parseFromFile(m_onnxPath.c_str(), 1)){
    //     return false;
    // }
    // // 关键修改3：添加网络输入维度验证，兼容动态和静态batch
    // const int numInputs = network->getNbInputs();
    // for (int i = 0; i < numInputs; ++i) {
    //     ITensor* input = network->getInput(i);
    //     Dims dims = input->getDimensions();
    //     // 验证batch维度是否为第一个维度，且允许动态或静态batch
    //     if (dims.nbDims < 1) {
    //         LOGE("Invalid input dimension at index %d. Explicit batch requires batch dimension as first dim", i);
    //         return false;
    //     }
    //     // 如果batch维度是静态的，检查是否与m_params->batch_size一致
    //     if (dims.d[0] != -1 && dims.d[0] != m_params->batch_size) {
    //         LOGE("Invalid batch size at index %d. Expected %d but got %d", i, m_params->batch_size, dims.d[0]);
    //         return false;
    //     }
    // }
    // // 关键修改4：添加层维度检查（防止修改batch维度）
    // for (int i = 0; i < network->getNbLayers(); ++i) {
    //     ILayer* layer = network->getLayer(i);
    //     for (int j = 0; j < layer->getNbOutputs(); ++j) {
    //         ITensor* output = layer->getOutput(j);
    //         Dims dims = output->getDimensions();
    //         // 检查batch维度是否被修改
    //         if (dims.nbDims > 0 && dims.d[0] == -1) {  // 动态batch应保持为-1
    //             LOGV("Layer %s maintains dynamic batch dimension", layer->getName());
    //         } else if (dims.nbDims > 0 && dims.d[0] != m_params->batch_size) {
    //             LOGW("Layer %s may modify batch dimension from %d to %d", 
    //                 layer->getName(), m_params->batch_size, dims.d[0]);
    //         }
    //     }
    // }
    // // Set DLA Core
    // if (builder->getNbDLACores() > 0) {
    //     config->setDLACore(m_params->dla_core);
    //     config->setFlag(BuilderFlag::kGPU_FALLBACK);
    //     for (int i = 0; i < network->getNbLayers(); ++i) {
    //         auto layer = network->getLayer(i);
    //         if (config->canRunOnDLA(layer)) {
    //             config->setDeviceType(layer, DeviceType::kDLA);
    //             LOGV("Layer %s set to DLA", layer->getName());
    //         } else {
    //             config->setDeviceType(layer, DeviceType::kGPU);
    //             LOGV("Layer %s not DLA capable, set to GPU", layer->getName());
    //         }
    //     }
    // } else {
    //     LOG("DLA core unavailable");
    // }
    // // 关键修改5：设置优化profile（动态batch时必需）
    // IOptimizationProfile* profile = builder->createOptimizationProfile();
    // for (int i = 0; i < numInputs; ++i) {
    //     ITensor* input = network->getInput(i);
    //     Dims dims = input->getDimensions();
    //     // 设置动态batch范围，根据实际需求调整
    //     dims.d[0] = m_params->batch_size;  // 最小batch
    //     profile->setDimensions(input->getName(), OptProfileSelector::kMIN, dims);
    //     dims.d[0] = m_params->batch_size;  // 最优batch
    //     profile->setDimensions(input->getName(), OptProfileSelector::kOPT, dims);
    //     dims.d[0] = m_params->batch_size; // 最大batch
    //     profile->setDimensions(input->getName(), OptProfileSelector::kMAX, dims);
    // }
    // config->addOptimizationProfile(profile);
    // if (builder->platformHasFastFp16() && m_params->prec == model::FP16) {
    //     config->setFlag(BuilderFlag::kFP16);
    //     config->setFlag(BuilderFlag::kPREFER_PRECISION_CONSTRAINTS);
    // } else if (builder->platformHasFastInt8() && m_params->prec == model::INT8) {
    //     config->setFlag(BuilderFlag::kINT8);
    //     config->setFlag(BuilderFlag::kPREFER_PRECISION_CONSTRAINTS);
    // }
    // std::string calib_table_name;
    // switch(m_params->det_model) {
    //     case model::detection_model::YOLOV5:
    //         calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov5.txt";
    //         break;
    //     case model::detection_model::YOLOV7:
    //         calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov7.txt";
    //         break;
    //     case model::detection_model::YOLOV8:
    //         calib_table_name = "src/dev_pkg_det2d/calibration/calibration_table_yolov8.txt";
    //         break;
    // }
    // shared_ptr<Int8EntropyCalibrator> calibrator(new Int8EntropyCalibrator(
    //     m_params->batch_size, 
    //     (m_params == nullptr ? "src/dev_pkg_det2d/calibration/calibration_list_coco.txt" : m_params->calib_data_file.c_str()), 
    //     (m_params == nullptr ? calib_table_name.c_str() : m_params->calib_table_file.c_str()), 
    //     m_params->img.c * m_params->img.h * m_params->img.w, 
    //     m_params->img.h, 
    //     m_params->img.w, 
    //     m_params->task));
    // config->setInt8Calibrator(calibrator.get());
    
    // auto engine        = shared_ptr<ICudaEngine>(builder->buildEngineWithConfig(*network, *config), destroy_trt_ptr<ICudaEngine>);
    // auto plan          = builder->buildSerializedNetwork(*network, *config);
    // auto runtime       = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    // // 保存序列化后的engine
    // save_plan(*plan);
    // // 根据runtime初始化engine, context, 以及memory
    // setup(plan->data(), plan->size());
    // // 把优化前和优化后的各个层的信息打印出来
    // LOGV("Before TensorRT optimization");
    // print_network(*network, false);
    // LOGV("After TensorRT optimization");
    // print_network(*network, true);
    return true;
}


bool Model::load_engine() {
    // 同样的，我们也希望在load一个engine的时候就把一系列初始化全部做完，其中包括
    //  1. deserialize一个engine
    //  2. 创建一个context
    //  3. 创建推理所用的stream
    //  4. 创建推理所需要的device空间
    // 这样，我们就可以在load结束以后，就可以直接推理了。这样的写法会比较干净
    
    if (!fileExists(m_enginePath)) {
        LOGE("engine does not exits! Program terminated");
        return false;
    }

    vector<unsigned char> modelData;
    modelData     = loadFile(m_enginePath);
    
    // 根据runtime初始化engine, context, 以及memory
    setup(modelData.data(), modelData.size());

    return true;
}

void Model::save_plan(IHostMemory& plan) {
    auto f = fopen(m_enginePath.c_str(), "wb");
    fwrite(plan.data(), 1, plan.size(), f);
    fclose(f);
}

/* 
    可以根据情况选择是否在CPU上跑pre/postprocess
    对于一些edge设备，为了最大化GPU利用效率，我们可以考虑让CPU做一些pre/postprocess，让其执行与GPU重叠
*/
void Model::inference() {
    if (m_params->dev == CPU) {
        preprocess_cpu();
    } else {
        preprocess_gpu();
    }

    enqueue_bindings();

    if (m_params->dev == CPU) {
        postprocess_cpu();
    } else {
        postprocess_gpu();
    }
}


bool Model::enqueue_bindings() {
    std::lock_guard<std::mutex> lock(m_enqueue_mutex);
    // m_timer->start_gpu();

    // 计时
    auto start = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());

    if (!m_context->enqueueV2((void**)m_bindings, m_stream, nullptr)){
        LOGE("Error happens during DNN inference part, program terminated");
        return false;
    }

    CUDA_CHECK(cudaStreamSynchronize(m_stream));  // 不使用cuda event，在CPU端测试耗时，需要流同步
    auto end = std::chrono::duration_cast<std::chrono::microseconds>(std::chrono::system_clock::now().time_since_epoch());
    LOG("\t\t  Only-Inference:  %d us", (end - start).count());

    // m_timer->stop_gpu("trt-inference(GPU)");
    return true;
}

void Model::print_network(INetworkDefinition &network, bool optimized) {

    int inputCount = network.getNbInputs();
    int outputCount = network.getNbOutputs();
    string layer_info;

    for (int i = 0; i < inputCount; i++) {
        auto input = network.getInput(i);
        LOGV("Input info: %s:%s", input->getName(), printTensorShape(input).c_str());
    }

    for (int i = 0; i < outputCount; i++) {
        auto output = network.getOutput(i);
        LOGV("Output info: %s:%s", output->getName(), printTensorShape(output).c_str());
    }
    
    int layerCount = optimized ? m_engine->getNbLayers() : network.getNbLayers();
    LOGV("network has %d layers", layerCount);

    if (!optimized) {
        for (int i = 0; i < layerCount; i++) {
            char layer_info[1000];
            auto layer   = network.getLayer(i);
            auto input   = layer->getInput(0);
            int n = 0;
            if (input == nullptr){
                continue;
            }
            auto output  = layer->getOutput(0);

            LOGV("layer_info: %-40s:%-25s->%-25s[%s]", 
                layer->getName(),
                printTensorShape(input).c_str(),
                printTensorShape(output).c_str(),
                getPrecision(layer->getPrecision()).c_str());
        }

    } else {
        auto inspector = shared_ptr<IEngineInspector>(m_engine->createEngineInspector());
        for (int i = 0; i < layerCount; i++) {
            LOGV("layer_info: %s", inspector->getLayerInformation(i, nvinfer1::LayerInformationFormat::kJSON));
        }
    }
}

string Model::getPrec(model::precision prec) {
    switch(prec) {
        case model::precision::FP16:   return "fp16";
        case model::precision::INT8:   return "int8";
        default:                       return "fp32";
    }
}

} // namespace model
