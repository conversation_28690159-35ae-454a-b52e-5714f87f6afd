#ifndef __WORKER_HPP__
#define __WORKER_HPP__

#include <memory>
#include <vector>
#include <future>
#include <mutex>
#include <unordered_map>
#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "trt_vehicle_classifier.hpp"
#include "trt_plate_detector.hpp"
#include "trt_plate_classifier.hpp"
#include "trt_vehicle_detector.hpp"
#include "trt_vehicle_detector_detr.hpp"
#include "trt_helmet_classifier.hpp"
#include "trt_ocr.hpp"
#include "trt_fisheye_detector_detr.hpp"
#include "trt_thread_pool.hpp"
#include "image_buffer.hpp"

namespace thread{

class Worker {
public:
    Worker(std::string onnxPath, logger::Level level, model::Params params);
    ~Worker();

    // 同步推理接口
    void inference(std::string imagePath);
    void inference(std::string frame_id, const cv::Mat& imageCap);

    // 异步推理接口
    std::future<void> async_inference(std::string imagePath);
    std::future<void> async_inference(std::string frame_id, const cv::Mat& imageCap);

    // 等待所有异步任务完成
    void wait_all();

    // 在外部调用保存评估结果的接口
    void save_eval_results() {
        if (m_plate_detector != nullptr) {
            m_plate_detector->save_coco_results();
        }
    }

    // 在外部调用获取detect结果的接口
    bool get_obj_result(std::vector<model::detector::bbox> &objects, std::string id) {
        if (m_plate_detector != nullptr) {
            return m_plate_detector->get_obj_result(objects, id);
        }
        return false;
    }

    bool get_img_result(cv::Mat &img, std::string id) {
        if (m_plate_detector != nullptr) {
            return m_plate_detector->get_img_result(img, id);
        }
        return false;
    }

public:
    // 设置图像缓冲区
    void set_image_buffer(std::shared_ptr<model::ImageBuffer> buffer) {
        m_imageBuffer = buffer;
        // 将缓冲区传递给分类器和检测器
        if (m_vehicle_classifier) {
            m_vehicle_classifier->set_image_buffer(buffer);
        }
        if (m_plate_detector) {
            m_plate_detector->set_image_buffer(buffer);
        }
    }

    // 同步推理接口 从图像缓冲区推理
    void inference_from_buffer(int64_t image_id);
    void inference_latest_from_buffer();
    void inference_all_from_buffer();

    // 异步推理接口 从图像缓冲区推理
    std::future<void> async_inference_from_buffer(int64_t image_id);
    std::future<void> async_inference_latest_from_buffer();
    std::future<void> async_inference_all_from_buffer();

    // 批量推理接口
    void batch_inference(const std::vector<cv::Mat>& images);
    std::future<void> async_batch_inference(const std::vector<cv::Mat>& images);

private:
    // 初始化模型（仅在第一次使用时）
    void init_models();
    // 初始化分类器输入（仅在第一次使用时）
    void init_images(std::string imagePath);

    // 执行分类器推理
    void run_classifier(std::string imagePath);
    void run_classifier(std::string frame_id, const cv::Mat& imageCap);

    // 执行检测器推理
    void run_detector(std::string imagePath);
    void run_detector(std::string frame_id, const cv::Mat& imageCap);

public:
    std::shared_ptr<logger::Logger>          m_logger;
    std::shared_ptr<model::Params>           m_params;

    std::shared_ptr<model::detector::VehicleDetector>        m_vehicle_detector;
    std::shared_ptr<model::detector::VehicleDetectorDetr>    m_vehicle_detector_detr;
    std::shared_ptr<model::detector::FisheyeDetectorDetr>    m_fisheye_detector_detr;
    std::shared_ptr<model::classifier::HelmetClassifier>     m_helmet_classifier;
    std::shared_ptr<model::classifier::VehicleClassifier>    m_vehicle_classifier;
    std::shared_ptr<model::detector::PlateDetector>          m_plate_detector;
    std::shared_ptr<model::classifier::PlateClassifier>      m_plate_classifier;
    std::shared_ptr<model::ocr::OCRModel>                    m_ocr_model;
    std::vector<float>                                       m_scores;
    std::vector<model::detector::bbox>                       m_boxes;

private:
    // 线程池
    std::shared_ptr<ThreadPool>                     m_thread_pool;

    // 模型初始化状态
    std::mutex                                      m_init_mutex;
    bool                                            m_vehicle_detector_initialized;
    bool                                            m_vehicle_detector_detr_initialized;
    bool                                            m_fisheye_detector_detr_initialized;
    bool                                            m_helmet_classifier_initialized;
    bool                                            m_vehicle_classifier_initialized;
    bool                                            m_plate_detector_initialized;
    bool                                            m_plate_classifier_initialized;
    bool                                            m_ocr_model_initialized;
    // 输入初始化状态
    std::mutex                                      m_images_mutex;
    bool                                            m_vehicle_detector_images_initialized;
    bool                                            m_vehicle_detector_detr_images_initialized;
    bool                                            m_fisheye_detector_detr_images_initialized;
    bool                                            m_helmet_classifier_images_initialized;
    bool                                            m_vehicle_classifier_images_initialized;
    bool                                            m_plate_detector_images_initialized;
    bool                                            m_plate_classifier_images_initialized;
    bool                                            m_ocr_model_images_initialized;
    // 图像缓冲区
    std::shared_ptr<model::ImageBuffer>             m_imageBuffer;
};

std::shared_ptr<Worker> create_worker(
    std::string onnxPath, logger::Level level, model::Params params);

std::shared_ptr<Worker> create_worker_with_buffer(
    std::string onnxPath, logger::Level level, model::Params params,
    std::shared_ptr<model::ImageBuffer> buffer = nullptr);

}; //namespace thread

#endif //__WORKER_HPP__