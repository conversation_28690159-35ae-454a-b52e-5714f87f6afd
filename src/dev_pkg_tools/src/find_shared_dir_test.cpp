#include <ament_index_cpp/get_package_share_directory.hpp>
#include <iostream>
#include <string>

int main(int argc, char** argv) {
    std::string package_name = "dev_pkg_tools";
    try {
        std::string share_directory =
            ament_index_cpp::get_package_share_directory(package_name);
        std::cout << "Shared directory for package " << package_name << ": "
                  << share_directory << std::endl;
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
    }
    return 0;
}