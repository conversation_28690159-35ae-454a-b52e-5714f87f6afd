#ifndef __TRT_PLATE_CLASSIFIER_HPP__
#define __TRT_PLATE_CLASSIFIER_HPP__

#include <memory>
#include <vector>
#include <string>
#include "NvInfer.h"
#include "trt_logger.hpp"
#include "trt_model.hpp"

namespace model{

namespace classifier {
class PlateClassifier : public Model{

public:
    // 这个构造函数实际上调用的是父类的Model的构造函数
    PlateClassifier(std::string onnx_path, logger::Level level, Params params) : 
        Model(onnx_path, level, params) {};

public:
    // 这里classifer自己实现了一套前处理/后处理，以及内存分配的初始化
    virtual void setup(void const* data, std::size_t size) override;
    virtual void reset_task() override;
    virtual bool preprocess_cpu() override;
    virtual bool preprocess_gpu() override;
    virtual bool postprocess_cpu() override;
    virtual bool postprocess_gpu() override;
    // check model name in params
    virtual bool check_model() override;
    virtual void inference() override;

private:
    // float m_confidence;
    // std::string m_label;
    int m_inputSize; 
    int m_imgArea;
    // int m_outputSize;
    int m_colorLabelSize;
    int m_layerLabelSize;
    int m_colorOutputSize;
    int m_layerOutputSize;
    float* m_colorOutputMemory[2];
    float* m_layerOutputMemory[2];
    float* m_bindingsOverride[3];

    const std::map<int, std::string> COLOR_MAP = {
        {0, "black"}, {1, "blue"}, {2, "green"}, {3, "white"}, {4, "yellow"}
    };
    const std::map<int, std::string> LAYER_MAP = {
        {0, "single"}, {1, "double"}
    };
};

// 外部调用的接口
std::shared_ptr<PlateClassifier> make_plate_classifier(
    std::string onnx_path, logger::Level level, Params params);

}; // namespace classifier
}; // namespace model

#endif //__TRT_PLATE_CLASSIFIER_HPP__
