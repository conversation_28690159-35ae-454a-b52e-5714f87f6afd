#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <deque>
#include <mutex>
#include <algorithm>

#include "rclcpp/rclcpp.hpp"
// #include "rclcpp_components/register_node_macro.hpp"
#include "sensor_msgs/msg/image.hpp"
#include "std_msgs/msg/u_int8_multi_array.hpp"
#include "std_msgs/msg/string.hpp"
#include "cv_bridge/cv_bridge.h"

#include "trt_worker.hpp"
#include "trt_logger.hpp"
#include "trt_model.hpp"
#include "esurfing/perc/group_detected.pb.h"
#include "esurfing/math/geo.pb.h"

#include <nlohmann/json.hpp>

using namespace std::chrono_literals;
using perception::detect::DetectedObject;
using perception::detect::DetectedObjects;
using perception::detect::Vector3f;
using perception::detect::Vector2f;

// namespace Det2dWorker {

class VehicleDetNode : public rclcpp::Node {
public:
    using ImageMsg = sensor_msgs::msg::Image;
    using UInt8MultiArrayMsg = std_msgs::msg::UInt8MultiArray;

    explicit VehicleDetNode(const rclcpp::NodeOptions& options)
    : Node("vehicle_det_node", options)
    {
        // 声明参数
        this->declare_parameter<std::vector<std::string>>("input_topics",
            {"/c0", "/c1", "/c2", "/c3"});
        this->declare_parameter<std::vector<std::string>>("output_topics",
            {"o0", "/o1", "/o2", "/o3"});
        this->declare_parameter<std::string>("model_path", "src/dev_pkg_det2d/models/onnx/vehicle_det_batch4.onnx");
        this->declare_parameter<std::string>("model_path_cls", "src/dev_pkg_det2d/models/onnx/vehicle_cls_batch8.onnx");
        this->declare_parameter<int>("max_queue_size", 1);
        this->declare_parameter<float>("inference_rate", 5.0);  // Hz
        this->declare_parameter<float>("confidence_threshold", 0.3);
        this->declare_parameter<int>("max_batch_size", 8);
        this->declare_parameter<std::vector<std::string>>("group_names",
            {"vehicle_detection1", "vehicle_detection2", "vehicle_detection3", "vehicle_detection4"});

        // 获取参数
        this->get_parameter("input_topics", input_topics_);
        this->get_parameter("output_topics", output_topics_);
        this->get_parameter("model_path", model_path_);
        this->get_parameter("model_path_cls", model_path_cls_);
        this->get_parameter("max_queue_size", max_queue_size_);
        this->get_parameter("inference_rate", inference_rate_);
        this->get_parameter("confidence_threshold", confidence_threshold_);
        this->get_parameter("max_batch_size", max_batch_size_);
        this->get_parameter("group_names", group_names_);

        // 限制输入topic数量
        if (input_topics_.size() > 8) {
            RCLCPP_WARN(this->get_logger(), "Too many input topics (>8), using only the first 8");
            input_topics_.resize(8);
        } else if (input_topics_.size() < 1) {
            RCLCPP_ERROR(this->get_logger(), "No input topics specified");
            return;
        }

        // 确保输出topic数量与输入一致
        if (output_topics_.size() != input_topics_.size()) {
            RCLCPP_WARN(this->get_logger(),
                "Number of output topics (%zu) doesn't match input topics (%zu), adjusting...",
                output_topics_.size(), input_topics_.size());

            // 如果输出topic太少，添加默认topic
            while (output_topics_.size() < input_topics_.size()) {
                std::string new_topic = "/vehicle/det" + std::to_string(output_topics_.size() + 1);
                output_topics_.push_back(new_topic);
            }

            // 如果输出topic太多，截断
            if (output_topics_.size() > input_topics_.size()) {
                output_topics_.resize(input_topics_.size());
            }
        }

        // 确保group_names数量与输入一致
        if (group_names_.size() != input_topics_.size()) {
            RCLCPP_WARN(this->get_logger(),
                "Number of group names (%zu) doesn't match input topics (%zu), adjusting...",
                group_names_.size(), input_topics_.size());

            // 如果group_names太少，添加默认名称
            while (group_names_.size() < input_topics_.size()) {
                std::string new_group = "vehicle_detection" + std::to_string(group_names_.size() + 1);
                group_names_.push_back(new_group);
            }

            // 如果group_names太多，截断
            if (group_names_.size() > input_topics_.size()) {
                group_names_.resize(input_topics_.size());
            }
        }

        // 创建topic和group_name的映射关系
        for (size_t i = 0; i < input_topics_.size(); ++i) {
            topic_to_group_map_[input_topics_[i]] = group_names_[i];
            RCLCPP_INFO(this->get_logger(), "Mapping topic '%s' to group name '%s'",
                        input_topics_[i].c_str(), group_names_[i].c_str());
        }

        // 初始化TRT Worker
        init_trt_worker();

        // 创建图像订阅者
        for (size_t i = 0; i < input_topics_.size(); ++i) {
            const auto& topic = input_topics_[i];
            auto callback = [this, i, topic](const ImageMsg::SharedPtr msg) {
                this->image_callback(msg, i, topic);
            };

            auto sub = this->create_subscription<ImageMsg>(
                topic, 10, callback);

            image_subscribers_.push_back(sub);
            image_queues_[i] = std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>();

            RCLCPP_INFO(this->get_logger(), "Subscribed to image topic: %s", topic.c_str());
        }

        // 创建检测结果发布者
        for (size_t i = 0; i < output_topics_.size(); ++i) {
            const auto& topic = output_topics_[i];
            auto pub = this->create_publisher<UInt8MultiArrayMsg>(topic, 10);
            detection_publishers_.push_back(pub);
            RCLCPP_INFO(this->get_logger(), "Created publisher for topic: %s", topic.c_str());
        }

        // 实验室环境
        for (size_t i = 0; i < output_topics_.size(); ++i) {
            const auto& topic = output_topics_[i];
            std::string debug_topic = topic + "/time_debug";
            auto debug_pub = this->create_publisher<std_msgs::msg::String>(debug_topic, 10);
            debug_time_publishers_.push_back(debug_pub);
            RCLCPP_INFO(this->get_logger(), "Created debug publisher for topic: %s", debug_topic.c_str());

            // 创建图像结果发布器
            std::string image_debug_topic = input_topics_[i] + "/detection_result";
            auto image_debug_pub = this->create_publisher<sensor_msgs::msg::Image>(image_debug_topic, 10);
            debug_image_publishers_.push_back(image_debug_pub);
            RCLCPP_INFO(this->get_logger(), "Created debug image publisher for topic: %s", image_debug_topic.c_str());
        }

        // 创建推理定时器
        inference_timer_ = this->create_wall_timer(
            std::chrono::milliseconds(static_cast<int>(1000.0 / inference_rate_)),
            std::bind(&VehicleDetNode::inference_callback, this));

        RCLCPP_INFO(this->get_logger(), "Vehicle detection node initialized with %zu cameras", input_topics_.size());
    }

    ~VehicleDetNode() {
        // 清理资源
        if (inference_timer_) {
            inference_timer_->cancel();
        }
    }

private:
    void init_trt_worker() {
        /* --------------------------------Step1. 配置检测模型-------------------------------- */
        // 模型配置
        std::string onnxPath = model_path_;
        auto level = logger::Level::WARN; //FATAL;
        auto params = model::Params();
        // 通用参数
        params.batch_size = static_cast<int>(input_topics_.size());  // 批处理大小
        params.img = {640, 640, 3};
        params.task = model::task_type::VEHICLE_DETECTION;
        params.dev = model::device::CPU;
        params.prec = model::precision::INT8;
        params.calib_data_file = "";
        params.calib_table_file = "";
        // 检测参数
        params.conf_thresh = confidence_threshold_;
        params.nms_thresh = 0.45;
        params.det_model = model::detection_model::YOLOV5;
        // DLA参数
        params.use_dla = false;
        params.dla_core = 0;
        // 创建TRT Worker
        worker_ = thread::create_worker(onnxPath, level, params);
        if (!worker_) {
            RCLCPP_ERROR(this->get_logger(), "Failed to create TRT worker");
        } else {
            RCLCPP_INFO(this->get_logger(), "TRT worker created successfully");
        }
        /* --------------------------------------------------------------------------------- */
        /* --------------------------------Step2. 配置分类模型-------------------------------- */
        // 模型配置
        std::string onnxPath_cls = model_path_cls_;
        auto params_cls = model::Params();
        // 通用参数
        params_cls.batch_size = 8;  // 批处理大小
        params_cls.img = {192, 256, 3};
        params_cls.task = model::task_type::VEHICLE_CLASSIFICATION;
        params_cls.dev = model::device::CPU;
        params_cls.prec = model::precision::INT8;
        params_cls.calib_data_file = "";
        params_cls.calib_table_file = "";
        // 分类参数
        params_cls.num_cls = 21;
        std::vector<std::vector<int>>().swap(params_cls.class_domains);
        params_cls.class_domains.push_back({0,1,2,3,4,5,6,7,8,9});
        params_cls.class_domains.push_back({10,11,12,13,14,15,16,17,18,19,20});
        // DLA参数
        params_cls.use_dla = false;
        params_cls.dla_core = 0;
        // 创建分类器
        worker_vehicle_cls_ = thread::create_worker(onnxPath_cls, level, params_cls);
        if (!worker_vehicle_cls_) {
            RCLCPP_ERROR(this->get_logger(), "Failed to create TRT worker for vehicle classification");
        } else {
            RCLCPP_INFO(this->get_logger(), "TRT worker created successfully for vehicle classification");
        }
        /* --------------------------------------------------------------------------------- */
    }

    void image_callback(const ImageMsg::SharedPtr msg, size_t camera_idx, const std::string& topic) {
        try {
            if (msg->encoding != "bgr8" && msg->encoding != "rgb8") {
                RCLCPP_ERROR(this->get_logger(), "Unsupported encoding: %s",
                                msg->encoding.c_str());
                return;
            }
            cv::Mat image(msg->height, msg->width, CV_8UC3,
                const_cast<uint8_t*>(msg->data.data()), msg->step);
            if (image.empty()) {
                RCLCPP_ERROR(this->get_logger(),
                                "Received empty image for camera: %d",
                                camera_idx);
                return;
            }
            if (msg->encoding == "rgb8") {
                cv::cvtColor(image, image, cv::COLOR_RGB2BGR);
            }

            // 加锁保护队列
            {
                std::lock_guard<std::mutex> lock(queue_mutex_);
                auto& queue = image_queues_[camera_idx];
                // 添加到对应相机的队列
                queue.push_back(std::make_pair(msg, image.clone()));
                // 限制队列大小
                while (queue.size() > static_cast<size_t>(max_queue_size_)) {
                    queue.pop_front();
                }
            }

            RCLCPP_DEBUG(this->get_logger(), "Received image from %s, queue size: %zu",
                        topic.c_str(), image_queues_[camera_idx].size());

            // 生产环境日志
            LOGW("Received image from %s, timestamp of msg: %ld (ms), timestamp of system: %ld (ms)", 
                 topic.c_str(), rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000,
                 std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count());
            LOGW("Delay between camera msg header and system time of getting image: %ld (ms)",
                 std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count() - rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000);
            LOGW("");
        }
        catch (cv_bridge::Exception& e) {
            RCLCPP_ERROR(this->get_logger(), "CV bridge exception: %s", e.what());
        }
    }

    void inference_callback() {
        /* Step 0: Init Check */
        // 检查TRT worker是否已初始化
        if (!worker_) {
            RCLCPP_WARN_THROTTLE(this->get_logger(), *this->get_clock(), 5000,
                                "TRT worker not initialized");
            return;
        }
        // 检查前后两次进入inference_callback的时间戳
        static rclcpp::Time last_callback_time = this->now();
        rclcpp::Time current_time = this->now();
        // 计算时间差（单位：毫秒）
        double time_diff = (current_time - last_callback_time).seconds() * 1000;
        // 如果时间差大于100ms，打印警告
        if (time_diff > 102.0) {
            RCLCPP_DEBUG(this->get_logger(), 
                        "Long delay between start of inference callbacks: %.2f ms", 
                        time_diff);
        }
        // 更新上次调用时间
        last_callback_time = current_time;

        /* Step 1: Data Load */
        std::chrono::steady_clock::time_point begin_data_load = std::chrono::steady_clock::now();
        // 准备批量推理的图像和ID
        std::vector<ImageMsg::SharedPtr> batch_msgs;
        std::vector<cv::Mat> batch_images;
        std::vector<size_t> camera_indices;
        // 加锁访问队列
        {
            std::lock_guard<std::mutex> lock(queue_mutex_);
            // 检查每个相机队列，取队首图像
            for (size_t i = 0; i < input_topics_.size(); ++i) {
                auto& queue = image_queues_[i];
                if (!queue.empty()) {
                    auto& front_pair = queue.front();
                    batch_msgs.push_back(front_pair.first);
                    batch_images.push_back(front_pair.second);
                    camera_indices.push_back(i);
                    queue.pop_front();
                }
            }
        }
        // 如果没有图像可处理，直接返回
        if (batch_images.empty()) {
            return;
        }
        // RCLCPP_INFO(this->get_logger(), "Processing batch of %zu images", batch_images.size());
        std::chrono::steady_clock::time_point end_data_load = std::chrono::steady_clock::now();

        /* Step 2: Inference */
        // 执行批量推理
        worker_->batch_inference(batch_images);
        std::chrono::steady_clock::time_point end_inference = std::chrono::steady_clock::now();

        /* Step 3: Result Process */
        // 处理推理结果
        process_detection_results(batch_msgs, camera_indices);
        std::chrono::steady_clock::time_point end_result_process = std::chrono::steady_clock::now();

        // Calculate timing for each step
        auto data_load_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_data_load - begin_data_load).count();
        auto inference_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_inference - end_data_load).count();
        auto result_process_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_result_process - end_inference).count();
        auto total_time = std::chrono::duration_cast<std::chrono::microseconds>(
            end_result_process - begin_data_load).count();
        LOGV("\t  Processed batch of %zu images", batch_images.size());
        LOGV("\t  Data Load:       %d us", data_load_time);
        LOGV("\t  Inference:       %d us", inference_time);
        LOGV("\t  Result Process:  %d us", result_process_time);
        LOGV("\t  Total Time:      %d us", total_time);

        if (total_time > 102000) {
            RCLCPP_WARN(this->get_logger(), 
                        "Long delay between inference once: %.2f ms", 
                        total_time / 1000.0);
        }
    }

    void process_detection_results(const std::vector<ImageMsg::SharedPtr>& batch_msgs,
                                    const std::vector<size_t>& camera_indices) {
        // 获取检测结果
        std::vector<std::vector<model::detector::bbox>> batch_objs;
        if (worker_->m_vehicle_detector) {
            batch_objs = worker_->m_vehicle_detector->get_batch_obj_bboxes();
        }
        // 获取裁剪后的图像
        std::vector<std::vector<cv::Mat>> batch_cropped_images;
        if (worker_->m_vehicle_detector) {
            batch_cropped_images = worker_->m_vehicle_detector->get_batch_cropped_images();
        }

        // 对每个图像处理检测结果
        for (size_t i = 0; i < batch_msgs.size(); ++i) {
            const ImageMsg::SharedPtr& msg = batch_msgs[i];
            size_t camera_idx = camera_indices[i];

            // 创建DetectedObjects消息
            DetectedObjects detected_objects;
            // 设置时间戳和组名
            int64_t timestamp_ns = rclcpp::Time(msg->header.stamp).nanoseconds();
            // // 路侧环境
            // detected_objects.set_time_meas(timestamp_ns / 1000); // 转换为微秒
            // detected_objects.set_time_pub(std::chrono::duration_cast<std::chrono::microseconds>(
            //     std::chrono::system_clock::now().time_since_epoch()).count());

            // 实验室环境
            auto now = std::chrono::duration_cast<std::chrono::microseconds>(
                std::chrono::system_clock::now().time_since_epoch()).count();
            detected_objects.set_time_meas(now);
            detected_objects.set_time_pub(now);
            nlohmann::json debug_time_json;
            debug_time_json["camera"] = input_topics_[camera_idx];
            debug_time_json["time_meas_us"] = timestamp_ns / 1000;
            debug_time_json["time_pub_us"] = now;
            std_msgs::msg::String debug_time_msg;
            debug_time_msg.data = debug_time_json.dump();

            // 实验室环境
            cv::Mat detection_image = worker_->m_vehicle_detector->get_image_detected(i);  // i: batch_idx, camera_indices[i]: camera_idx
            if (!detection_image.empty()) {
                cv_bridge::CvImage cv_image;
                cv_image.header = msg->header;
                cv_image.encoding = "bgr8";
                cv_image.image = detection_image;
                debug_image_publishers_[camera_idx]->publish(*cv_image.toImageMsg());
            }

            // 使用对应相机的group_name
            const std::string& topic = input_topics_[camera_idx];
            const std::string& group_name = group_names_[camera_idx];
            detected_objects.set_group_name(group_name);

            // 添加检测到的对象
            for (const auto& det : batch_objs[i]) {
                DetectedObject* obj = detected_objects.add_objects();
                // 设置基本属性
                obj->set_type(convert_label_to_proto_type(det.label));
                obj->set_confidence(det.confidence);
                
                // 复用3D字段存储2D信息
                Vector3f* pos3d = obj->mutable_position();
                pos3d->set_x(0.5 * (det.x0 + det.x1));
                pos3d->set_y(0.5 * (det.y0 + det.y1));
                Vector3f* shape3d = obj->mutable_shape();
                shape3d->set_x(det.x1 - det.x0);  // 宽度
                shape3d->set_y(det.y1 - det.y0);  // 高度

                // 生产环境日志
                LOGW("Object info (before vehicle_cls) from %s, type: %d, confidence: %.3f, x-left: %.3f, y-top: %.3f, width: %.3f, height: %.3f", 
                     topic.c_str(), obj->type(), obj->confidence(), pos3d->x(), pos3d->y(), shape3d->x(), shape3d->y());
            }

            // 加一层针对truck的车型分类（用户可以自定义对truck、car等类别进行车型分类）
            if (!batch_cropped_images[i].empty()) {
                process_vehicle_classification(detected_objects, batch_cropped_images[i], batch_objs[i]);
            }

            // 生产环境日志
            for (int i = 0; i < detected_objects.objects_size(); ++i) {
                const DetectedObject& obj = detected_objects.objects(i);
                LOGW("Object info (after vehicle_cls) from %s, type: %d, confidence: %.3f, x-left: %.3f, y-top: %.3f, width: %.3f, height: %.3f", 
                     topic.c_str(), obj.type(), obj.confidence(), obj.position().x(), obj.position().y(), obj.shape().x(), obj.shape().y());
            }

            // 序列化并发布结果
            std::string serialized_data;
            if (detected_objects.SerializeToString(&serialized_data)) {
                UInt8MultiArrayMsg output_msg;
                output_msg.layout.dim.push_back(std_msgs::msg::MultiArrayDimension());
                output_msg.layout.dim[0].size = serialized_data.size();
                output_msg.layout.dim[0].stride = 1;
                output_msg.layout.dim[0].label = "protobuf_bytes";
                output_msg.data.assign(serialized_data.begin(), serialized_data.end());

                // 发布到对应相机的输出topic
                detection_publishers_[camera_idx]->publish(output_msg);

                // 实验室环境
                debug_time_publishers_[camera_idx]->publish(debug_time_msg);

                // 生产环境日志
                LOGW("Publish object from %s, timestamp of meas(modified): %ld (ms), timestamp of pub: %ld (ms)", 
                     topic.c_str(), rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000, detected_objects.time_pub() / 1000);
                LOGW("Delay between camera msg header and system time of publishing objects: %ld (ms)",
                     detected_objects.time_pub() / 1000 - rclcpp::Time(msg->header.stamp).nanoseconds() / 1000000);
                LOGW("");

                RCLCPP_DEBUG(this->get_logger(), "Published %zu detections for camera %zu",
                            detected_objects.objects_size(), camera_idx);
            } else {
                RCLCPP_ERROR(this->get_logger(), "Failed to serialize detection results");
            }
        }

        // for (size_t i = 0; i < input_topics_.size(); ++i) {
        //     // 检查i是否在camera_indices中
        //     if (std::find(camera_indices.begin(), camera_indices.end(), i) == camera_indices.end()) {
        //         // 创建空的DetectedObjects消息
        //         DetectedObjects detected_objects;
                
        //         // 设置当前时间戳
        //         int64_t current_time_us = std::chrono::duration_cast<std::chrono::microseconds>(
        //             std::chrono::system_clock::now().time_since_epoch()).count();
        //         detected_objects.set_time_meas(current_time_us);
        //         detected_objects.set_time_pub(current_time_us);
                
        //         // 设置对应相机的group_name
        //         detected_objects.set_group_name(group_names_[i]);
        
        //         // 序列化并发布空结果
        //         std::string serialized_data;
        //         if (detected_objects.SerializeToString(&serialized_data)) {
        //             UInt8MultiArrayMsg output_msg;
        //             output_msg.layout.dim.push_back(std_msgs::msg::MultiArrayDimension());
        //             output_msg.layout.dim[0].size = serialized_data.size();
        //             output_msg.layout.dim[0].stride = 1;
        //             output_msg.layout.dim[0].label = "protobuf_bytes";
        //             output_msg.data.assign(serialized_data.begin(), serialized_data.end());
        
        //             // 发布到对应相机的输出topic
        //             detection_publishers_[i]->publish(output_msg);
        
        //             RCLCPP_DEBUG(this->get_logger(), "Published empty detection for camera %zu", i);
        //         } else {
        //             RCLCPP_ERROR(this->get_logger(), "Failed to serialize empty detection results");
        //         }
        //     }
        // }
    }

    int32_t convert_label_to_proto_type(int label) {
        // 默认返回UNKNOWN(0)
        if (label < 0 || label > 12) {
            return 0;  // UNKNOWN
        }
    
        // 映射关系
        static const std::unordered_map<int, int32_t> label_map = {
            {0, 1},    // car -> CAR
            {1, 4},    // truck -> TRUCK
            {2, 4},    // heavy_truck -> TRUCK
            {3, 5},    // van -> VAN
            {4, 6},    // bus -> BUS
            {5, 3},    // bicycle -> CYCLIST
            {6, 3},    // cyclist -> CYCLIST
            {7, 10},   // tricycle -> TROLLEY
            {8, 10},   // trolley -> TROLLEY
            {9, 2},    // pedestrain -> PEDESTRIAN
            {10, 9},   // cone -> CONE
            {11, 0},   // animal -> UNKNOWN
            {12, 0},   // other -> UNKNOWN
        };
    
        auto it = label_map.find(label);
        return (it != label_map.end()) ? it->second : 0;
    }

    void process_vehicle_classification(DetectedObjects& detected_objects, 
        const std::vector<cv::Mat>& cropped_images,
        const std::vector<model::detector::bbox>& detections) {
        // 只对特定类型的车辆进行分类（如truck、car等）
        std::vector<cv::Mat> vehicle_images;
        std::vector<int> vehicle_indices;
        for (size_t obj_idx = 0; obj_idx < detections.size(); obj_idx++) {
            const auto& det = detections[obj_idx];
            int obj_type = convert_label_to_proto_type(det.label);
            // 只对truck类型进行分类
            if (obj_type == 4 || obj_type == 5 || obj_type == 6) { // TRUCK VAN BUS
                if (obj_idx < cropped_images.size()) {
                    vehicle_images.push_back(cropped_images[obj_idx]);
                    vehicle_indices.push_back(obj_idx);
                }
            }
        }
        // 如果有车辆图像，执行分类
        if (!vehicle_images.empty()) {
            // 执行批量推理
            worker_vehicle_cls_->batch_inference(vehicle_images);
            // 获取分类结果
            auto classification_results = worker_vehicle_cls_->m_vehicle_classifier->get_batch_classification_results();
            // 将分类结果应用到检测对象
            for (size_t cls_idx = 0; cls_idx < classification_results.size() && cls_idx < vehicle_indices.size(); cls_idx++) {
                int obj_idx = vehicle_indices[cls_idx];
                int vehicle_type = classification_results[cls_idx].first;
                float confidence = classification_results[cls_idx].second;
                if (obj_idx < detected_objects.objects_size()) {
                    DetectedObject* obj = detected_objects.mutable_objects(obj_idx);
                    // 获取原始类型
                    int original_type = obj->type();
                    // 将车型分类结果转换为proto类型
                    int new_type = convert_vehicle_cls_type_to_proto_type(vehicle_type, original_type);
                    // 更新对象类型
                    obj->set_type(new_type);

                    // 生产环境日志
                    LOGW("Vehicle classification for object %d: original_type=%d, new_type=%d, detailed_type=%d, confidence=%.3f", 
                         obj_idx, original_type, new_type, vehicle_type, confidence);
                }
            }
        }
    }

    int32_t convert_vehicle_cls_type_to_proto_type(int vehicle_cls_type, int original_type) {
        // 参考vehicle_color_labels.hpp中的标签
        // 0: bus, 1: car, 2: engineering truck, 3: truck, 4: police car, 
        // 5: ambulance, 6: mixer, 7: null, 8: slagcar, 9: fire engine
        switch (vehicle_cls_type) {
            case 0: // bus
                return 6; // BUS
            case 1: // car
                return 1; // CAR
            case 2: // engineering truck
                return 4; // TRUCK
            case 3: // truck
                return 4; // TRUCK
            case 5: // ambulance
                return 5; // VAN
            case 6: // mixer
                return 4; // TRUCK
            case 8: // slagcar
                return 4; // TRUCK
            case 9: // fire engine
                return 4; // TRUCK
            default:
                return original_type; // 保持原类型
        }
    }

private:
    // 参数
    std::vector<std::string> input_topics_;
    std::vector<std::string> output_topics_;
    std::vector<std::string> group_names_;
    std::string model_path_;
    std::string model_path_cls_;
    int max_queue_size_;
    float inference_rate_;
    float confidence_threshold_;
    int max_batch_size_;
    std::map<std::string, std::string> topic_to_group_map_; // 映射topic到group_name

    // ROS2 接口
    std::vector<rclcpp::Subscription<ImageMsg>::SharedPtr> image_subscribers_;
    std::vector<rclcpp::Publisher<UInt8MultiArrayMsg>::SharedPtr> detection_publishers_;
    rclcpp::TimerBase::SharedPtr inference_timer_;

    // 图像队列
    std::map<size_t, std::deque<std::pair<ImageMsg::SharedPtr, cv::Mat>>> image_queues_;
    std::mutex queue_mutex_;

    // TRT Worker
    std::shared_ptr<thread::Worker> worker_;
    std::shared_ptr<thread::Worker> worker_vehicle_cls_;

    // 实验室环境
    std::vector<rclcpp::Publisher<std_msgs::msg::String>::SharedPtr> debug_time_publishers_;
    std::vector<rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr> debug_image_publishers_;
};

// } // namespace Det2dWorker

// RCLCPP_COMPONENTS_REGISTER_NODE(Det2dWorker::VehicleDetNode)

int main(int argc, char * argv[]) {
    rclcpp::init(argc, argv);
    
    // 创建节点时提供 NodeOptions
    rclcpp::NodeOptions options;
    auto node = std::make_shared<VehicleDetNode>(options);
    
    if (rclcpp::ok()) {
        rclcpp::spin(node);
    }
    rclcpp::shutdown();
    return 0;
}