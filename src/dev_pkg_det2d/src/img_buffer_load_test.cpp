#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "utils.hpp"
#include "image_buffer.hpp"
#include "image_buffer_manager.hpp"

#include <iostream>
#include <dirent.h> // 用于目录遍历
#include <sys/stat.h> // 用于文件状态
#include <cstring> // 用于std::strerror
#include <chrono>
#include <thread>
#include <rclcpp/rclcpp.hpp>

using namespace std;

// 辅助函数：从目录加载图像到缓冲区
bool load_images_to_buffer(const std::string& dir_path, std::shared_ptr<model::ImageBuffer> buffer) {
    DIR* dir = opendir(dir_path.c_str());
    if (dir == nullptr) {
        std::cerr << "无法打开目录: " << dir_path << " - " << std::strerror(errno) << std::endl;
        return false;
    }

    std::vector<std::string> file_paths;
    struct dirent* entry;

    // 首先收集所有图像文件路径
    while ((entry = readdir(dir)) != nullptr) {
        std::string filename = entry->d_name;
        
        // 跳过 . 和 .. 目录
        if (filename == "." || filename == "..") {
            continue;
        }
        
        // 检查是否为图像文件（简单检查扩展名）
        if (filename.find(".jpg") != std::string::npos || 
            filename.find(".jpeg") != std::string::npos || 
            filename.find(".png") != std::string::npos) {
            
            std::string full_path = dir_path + "/" + filename;
            file_paths.push_back(full_path);
        }
    }
    
    closedir(dir);
    
    // 对文件路径进行排序
    std::sort(file_paths.begin(), file_paths.end());
    
    std::vector<cv::Mat> images;
    std::vector<model::ImageMetadata> metadata_list;
    int64_t id = 0;

    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    
    // 按排序后的顺序加载图像
    for (const auto& full_path : file_paths) {
        cv::Mat img = cv::imread(full_path);
        
        if (img.data == nullptr) {
            std::cerr << "无法加载图像: " << full_path << std::endl;
            continue;
        }
        
        // 提取文件名
        std::string filename = full_path.substr(full_path.find_last_of("/") + 1);
        
        // 创建元数据
        model::ImageMetadata metadata;
        metadata.unique_id = id;
        metadata.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();
        metadata.frame_id = filename;
        
        // 添加到向量
        std::cout << "加载图像 " << full_path << std::endl;
        images.push_back(img);
        metadata_list.push_back(metadata);
        
        id++;
    }

    std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();
    std::cout << "获取图像时间: " << std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count() << " ms" << std::endl;
    
    // 将所有图像添加到缓冲区
    if (!images.empty()) {
        buffer->add_images(images, metadata_list);
        std::cout << "已加载 " << images.size() << " 张图像到缓冲区" << std::endl;
        return true;
    }
    
    std::cerr << "目录中没有找到图像" << std::endl;
    return false;
}

class ImageBufferNode : public rclcpp::Node {
public:
    ImageBufferNode() : Node("image_buffer_node") {
        // 声明参数
        this->declare_parameter<std::string>("image_dir", "src/dev_pkg_det2d/data/source/fisheye_det");
        this->declare_parameter<int>("buffer_size", 8);
        this->declare_parameter<std::string>("buffer_name", "default");
        
        // 获取参数
        std::string image_dir;
        int buffer_size;
        std::string buffer_name;
        this->get_parameter("image_dir", image_dir);
        this->get_parameter("buffer_size", buffer_size);
        this->get_parameter("buffer_name", buffer_name);
        
        // 获取图像缓冲区管理器实例
        auto& manager = ImageBufferManager::getInstance();
        
        // 初始化共享内存和图像缓冲区
        if (!manager.initSharedMemory(buffer_name, buffer_size)) {
            RCLCPP_ERROR(this->get_logger(), "初始化共享内存失败: %s", buffer_name.c_str());
            return;
        }
        
        // 获取缓冲区
        auto image_buffer = manager.getBuffer(buffer_name);
        if (!image_buffer) {
            RCLCPP_ERROR(this->get_logger(), "获取图像缓冲区失败: %s", buffer_name.c_str());
            return;
        }
        
        // 保存缓冲区名称
        m_buffer_name = buffer_name;
        
        std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
        // 加载图像到缓冲区
        manager.lockBuffer(buffer_name); // 锁定共享资源
        bool load_success = load_images_to_buffer(image_dir, image_buffer);
        // 将图像数据写入共享内存
        manager.writeBufferToSharedMemory(buffer_name);
        manager.unlockBuffer(buffer_name); // 解锁共享资源
        
        if (!load_success) {
            RCLCPP_ERROR(this->get_logger(), "加载图像失败");
            return;
        }
        
        RCLCPP_INFO(this->get_logger(), "图像缓冲区已初始化，包含 %d 张图像", image_buffer->size());
        std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();
        RCLCPP_INFO(this->get_logger(), "获取图像、加载图像至共享内存缓冲区总时间: %d ms", 
                    std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
        
        // 创建定时器，定期输出缓冲区状态
        timer_ = this->create_wall_timer(
            std::chrono::seconds(5),
            std::bind(&ImageBufferNode::timer_callback, this));
    }

    ~ImageBufferNode() {
        auto& manager = ImageBufferManager::getInstance();
        manager.cleanupSharedMemory(m_buffer_name, true); // 标记为创建者
    }

private:
    void timer_callback() {
        auto& manager = ImageBufferManager::getInstance();
        auto image_buffer = manager.getBuffer(m_buffer_name);
        if (image_buffer) {
            RCLCPP_INFO(this->get_logger(), "图像缓冲区 %s 当前包含 %d 张图像", 
                        m_buffer_name.c_str(), image_buffer->size());
        }
    }
    
    rclcpp::TimerBase::SharedPtr timer_;
    std::string m_buffer_name;
};

int main(int argc, char * argv[]) {
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ImageBufferNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
} 