// Filename: src/video_publisher_component.cpp

#include <cv_bridge/cv_bridge.h>
#include <opencv2/opencv.hpp>
#include <rclcpp/rclcpp.hpp>
#include <rclcpp_components/register_node_macro.hpp>
#include <sensor_msgs/msg/image.hpp>
#include <memory>
#include <string>

namespace Dev2dTools {

class VideoPublisherComponent : public rclcpp::Node {
public:
  explicit VideoPublisherComponent(const rclcpp::NodeOptions & options)
  : Node("video_publisher_component", options)
  {
    // Declare parameters
    this->declare_parameter<std::string>("video_path", "/workspace/ros2_ws/src/dev_ws_det2d/src/dev_pkg_det2d/data/media/bj_short.mp4");
    this->declare_parameter<std::string>("topic_name", "video_frames");
    this->declare_parameter<double>("publish_rate", 30.0); // Default to 30 fps
    this->declare_parameter<bool>("loop_video", true);     // Loop video by default
    
    // Get parameters
    std::string video_path = this->get_parameter("video_path").as_string();
    std::string topic_name = this->get_parameter("topic_name").as_string();
    double publish_rate = this->get_parameter("publish_rate").as_double();
    loop_video_ = this->get_parameter("loop_video").as_bool();
    
    RCLCPP_INFO(this->get_logger(), "Opening video: %s", video_path.c_str());
    RCLCPP_INFO(this->get_logger(), "Publishing to topic: %s", topic_name.c_str());
    RCLCPP_INFO(this->get_logger(), "Publish rate: %.2f fps", publish_rate);
    
    // Open the video file
    cap_ = std::make_unique<cv::VideoCapture>(video_path);
    
    if (!cap_->isOpened()) {
      RCLCPP_ERROR(this->get_logger(), "Failed to open video file: %s", video_path.c_str());
      return;
    }
    
    // Get video properties
    video_fps_ = cap_->get(cv::CAP_PROP_FPS);
    frame_count_ = static_cast<int>(cap_->get(cv::CAP_PROP_FRAME_COUNT));
    
    RCLCPP_INFO(this->get_logger(), "Video FPS: %.2f, Total frames: %d", 
                video_fps_, frame_count_);
    
    // Create publisher
    publisher_ = this->create_publisher<sensor_msgs::msg::Image>(topic_name, 10);
    
    // Create timer for publishing frames
    // Use the specified publish rate instead of the video's native FPS
    auto timer_period = std::chrono::duration<double>(1.0 / publish_rate);
    timer_ = this->create_wall_timer(
      timer_period, std::bind(&VideoPublisherComponent::timer_callback, this));
  }

private:
  void timer_callback() {
    cv::Mat frame;
    bool success = cap_->read(frame);
    
    // If we reached the end of the video and looping is enabled
    if (!success && loop_video_) {
      RCLCPP_INFO(this->get_logger(), "Reached end of video, looping back to start");
      cap_->set(cv::CAP_PROP_POS_FRAMES, 0);
      success = cap_->read(frame);
    }
    
    if (!success) {
      RCLCPP_WARN(this->get_logger(), "Failed to read frame");
      return;
    }
    
    // Convert OpenCV image to ROS message
    std_msgs::msg::Header header;
    header.stamp = this->now();
    header.frame_id = "camera_frame";

    sensor_msgs::msg::Image::SharedPtr msg = std::make_shared<sensor_msgs::msg::Image>();
    msg->header = header;
    msg->height = frame.rows;
    msg->width = frame.cols;
    msg->encoding = "bgr8";
    msg->is_bigendian = false;
    msg->step = frame.cols * frame.elemSize();
    size_t size = msg->step * frame.rows;
    msg->data.resize(size);
    memcpy(msg->data.data(), frame.data, size);
    
    // Publish the message
    publisher_->publish(*msg);
  }

  std::unique_ptr<cv::VideoCapture> cap_;
  rclcpp::Publisher<sensor_msgs::msg::Image>::SharedPtr publisher_;
  rclcpp::TimerBase::SharedPtr timer_;
  double video_fps_;
  int frame_count_;
  bool loop_video_;
};

} // namespace Dev2dTools

// Register the component with class_loader
RCLCPP_COMPONENTS_REGISTER_NODE(Dev2dTools::VideoPublisherComponent)