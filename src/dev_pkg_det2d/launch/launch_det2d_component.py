import os

from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import ComposableNodeContainer
from launch_ros.descriptions import ComposableNode

def generate_launch_description():
    # Get the package share directory
    share_directory = get_package_share_directory('dev_pkg_det2d')
    # Construct the path to the parameters file
    params_file = os.path.join(share_directory, 'config', 'params.yaml')

    # Log the params file path (optional, for debugging)
    print(f"Params file: {params_file}")

    # Define the composable node
    det2d_node = ComposableNode(
        package='dev_pkg_det2d',
        plugin='YoloDetector::Det2dNode',
        name='det2d_node',
        parameters=[params_file]
    )

    # Define the container to hold the composable node
    container = ComposableNodeContainer(
        name='det2d_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[det2d_node],
        output='screen',
    )

    # Return the launch description
    return LaunchDescription([container])