#include "trt_model.hpp"
#include "trt_logger.hpp"
#include "trt_worker.hpp"
#include "utils.hpp"
#include "image_buffer.hpp"
#include "image_buffer_manager.hpp"

#include <iostream>
#include <chrono>
#include <future>
#include <vector>

using namespace std;

int main(int argc, char const *argv[])
{
    // 获取图像缓冲区管理器实例
    auto& manager = ImageBufferManager::getInstance();
    
    // 默认缓冲区名称
    std::string buffer_name = "default";
    
    // 如果提供了命令行参数，使用指定的缓冲区名称
    if (argc > 1) {
        buffer_name = argv[1];
    }
    
    // 连接到共享内存
    if (!manager.connectToSharedMemory(buffer_name)) {
        std::cerr << "连接到共享内存失败，请确保图像缓冲区节点已启动" << std::endl;
        std::cerr << "请先运行: ros2 run dev_pkg_det2d img_buffer_load_test" << std::endl;
        return 1;
    }
    
    // 获取指定名称的图像缓冲区
    auto image_buffer = manager.getBuffer(buffer_name);
    if (!image_buffer) {
        std::cerr << "获取图像缓冲区失败: " << buffer_name << std::endl;
        return 1;
    }
    
    // 检查缓冲区是否有图像
    manager.lockBuffer(buffer_name);
    size_t buffer_size = image_buffer->size();
    manager.unlockBuffer(buffer_name);
    
    if (buffer_size == 0) {
        std::cerr << "图像缓冲区为空，请确保图像已加载" << std::endl;
        std::cerr << "请先运行: ros2 run dev_pkg_det2d img_buffer_load_test" << std::endl;
        manager.cleanupSharedMemory(buffer_name);
        return 1;
    }
    
    // 模型配置
    string onnxPath1 = "src/dev_pkg_det2d/models/onnx/vehicle_det_detr_batch4.onnx";
    auto level1 = logger::Level::VERB;
    auto params1 = model::Params();
    // 通用参数
    params1.batch_size = 4;  // 批处理大小
    params1.img = {640, 640, 3};
    params1.task = model::task_type::VEHICLE_DETECTION_DETR;
    params1.dev = model::device::CPU;
    params1.prec = model::precision::FP16;  // TODO: INT8量化掉点不严重，但是存在类型错误
    params1.calib_data_file = "src/dev_pkg_det2d/calibration/calibration_list_vehicle_det.txt";
    params1.calib_table_file = "src/dev_pkg_det2d/calibration/calibration_vehicle_det_detr_batch4_gpu_table_minmax_resize.txt";
    // 检测参数
    params1.conf_thresh = 0.60;
    params1.nms_thresh = 0.45;
    params1.det_model = model::detection_model::DETR;
    // DLA参数
    params1.use_dla = false;
    params1.dla_core = 0;
    // 创建worker实例并设置图像缓冲区
    auto worker1 = thread::create_worker_with_buffer(onnxPath1, level1, params1, image_buffer);
    LOG("图像缓冲区 %s 包含 %d 张图像", buffer_name.c_str(), buffer_size);
    // 预热模型（第一次运行通常较慢）
    LOG("Warm up model...");
    worker1->inference_all_from_buffer();

    LOG("Test async inference...");
    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    auto future = worker1->async_inference_all_from_buffer();
    future.wait();
    std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();
    LOG("\tAsync inference time: %d ms", 
        std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());

    LOG("Test sync inference...");
    begin = std::chrono::steady_clock::now();
    worker1->inference_all_from_buffer();
    end = std::chrono::steady_clock::now();
    LOG("\tSync inference time: %d ms", 
        std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
    
    LOG("Test completed");
    
    // 在退出前清理共享内存资源
    manager.cleanupSharedMemory(buffer_name, false);
    return 0;
}