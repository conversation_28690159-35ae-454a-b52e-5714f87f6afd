// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/perc/group.proto

#include "esurfing/perc/group.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG

namespace _pb = ::PROTOBUF_NAMESPACE_ID;
namespace _pbi = _pb::internal;

namespace perception {
namespace config {
PROTOBUF_CONSTEXPR LidarIntrisic::LidarIntrisic(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.enabled_)*/false
  , /*decltype(_impl_.min_intensity_)*/0
  , /*decltype(_impl_.max_intensity_)*/0
  , /*decltype(_impl_.rot_)*/0
  , /*decltype(_impl_.vert_)*/0
  , /*decltype(_impl_.dist_)*/0
  , /*decltype(_impl_.dist_x_)*/0
  , /*decltype(_impl_.dist_y_)*/0
  , /*decltype(_impl_.vert_offset_)*/0
  , /*decltype(_impl_.horiz_offset_)*/0
  , /*decltype(_impl_.focal_distance_)*/0
  , /*decltype(_impl_.focal_scope_)*/0
  , /*decltype(_impl_.ring_)*/0
  , /*decltype(_impl_.cos_rot_)*/0
  , /*decltype(_impl_.sin_rot_)*/0
  , /*decltype(_impl_.cos_vert_)*/0
  , /*decltype(_impl_.sin_vert_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LidarIntrisicDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LidarIntrisicDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LidarIntrisicDefaultTypeInternal() {}
  union {
    LidarIntrisic _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LidarIntrisicDefaultTypeInternal _LidarIntrisic_default_instance_;
PROTOBUF_CONSTEXPR Lidar::Lidar(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.intrinsics_)*/{}
  , /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.topic_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tf_map_lidar_)*/nullptr
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.min_distance_)*/0
  , /*decltype(_impl_.max_distance_)*/0
  , /*decltype(_impl_.usage_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct LidarDefaultTypeInternal {
  PROTOBUF_CONSTEXPR LidarDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~LidarDefaultTypeInternal() {}
  union {
    Lidar _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 LidarDefaultTypeInternal _Lidar_default_instance_;
PROTOBUF_CONSTEXPR Radar::Radar(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.intrinsics_)*/{}
  , /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.topic_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tf_map_radar_)*/nullptr
  , /*decltype(_impl_.type_)*/0
  , /*decltype(_impl_.min_distance_)*/0
  , /*decltype(_impl_.max_distance_)*/0
  , /*decltype(_impl_.usage_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct RadarDefaultTypeInternal {
  PROTOBUF_CONSTEXPR RadarDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~RadarDefaultTypeInternal() {}
  union {
    Radar _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 RadarDefaultTypeInternal _Radar_default_instance_;
PROTOBUF_CONSTEXPR Camera::Camera(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.radial_distortion_)*/{}
  , /*decltype(_impl_.name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.topic_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tf_map_camera_)*/nullptr
  , /*decltype(_impl_.image_width_)*/0
  , /*decltype(_impl_.image_height_)*/0
  , /*decltype(_impl_.fx_)*/0
  , /*decltype(_impl_.fy_)*/0
  , /*decltype(_impl_.cx_)*/0
  , /*decltype(_impl_.cy_)*/0
  , /*decltype(_impl_.time_synced_)*/false
  , /*decltype(_impl_.usage_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct CameraDefaultTypeInternal {
  PROTOBUF_CONSTEXPR CameraDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~CameraDefaultTypeInternal() {}
  union {
    Camera _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 CameraDefaultTypeInternal _Camera_default_instance_;
PROTOBUF_CONSTEXPR ConfigGroup::ConfigGroup(
    ::_pbi::ConstantInitialized): _impl_{
    /*decltype(_impl_.radars_)*/{}
  , /*decltype(_impl_.cameras_)*/{}
  , /*decltype(_impl_.group_name_)*/{&::_pbi::fixed_address_empty_string, ::_pbi::ConstantInitialized{}}
  , /*decltype(_impl_.tf_map_group_)*/nullptr
  , /*decltype(_impl_.group_type_)*/0
  , /*decltype(_impl_._cached_size_)*/{}} {}
struct ConfigGroupDefaultTypeInternal {
  PROTOBUF_CONSTEXPR ConfigGroupDefaultTypeInternal()
      : _instance(::_pbi::ConstantInitialized{}) {}
  ~ConfigGroupDefaultTypeInternal() {}
  union {
    ConfigGroup _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PROTOBUF_ATTRIBUTE_INIT_PRIORITY1 ConfigGroupDefaultTypeInternal _ConfigGroup_default_instance_;
}  // namespace config
}  // namespace perception
static ::_pb::Metadata file_level_metadata_esurfing_2fperc_2fgroup_2eproto[5];
static const ::_pb::EnumDescriptor* file_level_enum_descriptors_esurfing_2fperc_2fgroup_2eproto[2];
static constexpr ::_pb::ServiceDescriptor const** file_level_service_descriptors_esurfing_2fperc_2fgroup_2eproto = nullptr;

const uint32_t TableStruct_esurfing_2fperc_2fgroup_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.enabled_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.min_intensity_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.max_intensity_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.rot_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.vert_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.dist_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.dist_x_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.dist_y_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.vert_offset_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.horiz_offset_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.focal_distance_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.focal_scope_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.ring_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.cos_rot_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.sin_rot_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.cos_vert_),
  PROTOBUF_FIELD_OFFSET(::perception::config::LidarIntrisic, _impl_.sin_vert_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.intrinsics_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.min_distance_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.max_distance_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.tf_map_lidar_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.topic_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Lidar, _impl_.usage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.type_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.intrinsics_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.min_distance_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.max_distance_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.tf_map_radar_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.topic_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Radar, _impl_.usage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.name_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.image_width_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.image_height_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.tf_map_camera_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.fx_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.fy_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.cx_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.cy_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.radial_distortion_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.topic_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.time_synced_),
  PROTOBUF_FIELD_OFFSET(::perception::config::Camera, _impl_.usage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _impl_.group_name_),
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _impl_.group_type_),
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _impl_.tf_map_group_),
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _impl_.radars_),
  PROTOBUF_FIELD_OFFSET(::perception::config::ConfigGroup, _impl_.cameras_),
};
static const ::_pbi::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::perception::config::LidarIntrisic)},
  { 23, -1, -1, sizeof(::perception::config::Lidar)},
  { 37, -1, -1, sizeof(::perception::config::Radar)},
  { 51, -1, -1, sizeof(::perception::config::Camera)},
  { 69, -1, -1, sizeof(::perception::config::ConfigGroup)},
};

static const ::_pb::Message* const file_default_instances[] = {
  &::perception::config::_LidarIntrisic_default_instance_._instance,
  &::perception::config::_Lidar_default_instance_._instance,
  &::perception::config::_Radar_default_instance_._instance,
  &::perception::config::_Camera_default_instance_._instance,
  &::perception::config::_ConfigGroup_default_instance_._instance,
};

const char descriptor_table_protodef_esurfing_2fperc_2fgroup_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031esurfing/perc/group.proto\022\021perception."
  "config\032\034esurfing/math/geometry.proto\"\303\002\n"
  "\rLidarIntrisic\022\017\n\007enabled\030\001 \001(\010\022\025\n\rmin_i"
  "ntensity\030\002 \001(\005\022\025\n\rmax_intensity\030\003 \001(\005\022\013\n"
  "\003rot\030\004 \001(\002\022\014\n\004vert\030\005 \001(\002\022\014\n\004dist\030\006 \001(\002\022\016"
  "\n\006dist_x\030\007 \001(\002\022\016\n\006dist_y\030\010 \001(\002\022\023\n\013vert_o"
  "ffset\030\t \001(\002\022\024\n\014horiz_offset\030\n \001(\002\022\026\n\016foc"
  "al_distance\030\013 \001(\002\022\023\n\013focal_scope\030\014 \001(\002\022\014"
  "\n\004ring\030\r \001(\005\022\017\n\007cos_rot\030\016 \001(\002\022\017\n\007sin_rot"
  "\030\017 \001(\002\022\020\n\010cos_vert\030\020 \001(\002\022\020\n\010sin_vert\030\021 \001"
  "(\002\"\270\002\n\005Lidar\022\014\n\004name\030\001 \001(\014\022+\n\004type\030\002 \001(\016"
  "2\035.perception.config.Lidar.Type\0224\n\nintri"
  "nsics\030\003 \003(\0132 .perception.config.LidarInt"
  "risic\022\024\n\014min_distance\030\004 \001(\002\022\024\n\014max_dista"
  "nce\030\005 \001(\002\0229\n\014tf_map_lidar\030\006 \001(\0132#.percep"
  "tion.config.Transformation3d\022\r\n\005topic\030\007 "
  "\001(\014\022\r\n\005usage\030\010 \001(\005\"9\n\004Type\022\013\n\007UNKNOWN\020\000\022"
  "\n\n\006VLP_16\020\001\022\n\n\006VLP_32\020\002\022\014\n\010HESAI_64\020\003\"\234\002"
  "\n\005Radar\022\014\n\004name\030\001 \001(\014\022+\n\004type\030\002 \001(\0162\035.pe"
  "rception.config.Radar.Type\0224\n\nintrinsics"
  "\030\003 \003(\0132 .perception.config.LidarIntrisic"
  "\022\024\n\014min_distance\030\004 \001(\002\022\024\n\014max_distance\030\005"
  " \001(\002\0229\n\014tf_map_radar\030\006 \001(\0132#.perception."
  "config.Transformation3d\022\r\n\005topic\030\007 \001(\014\022\r"
  "\n\005usage\030\010 \001(\005\"\035\n\004Type\022\013\n\007UNKNOWN\020\000\022\010\n\004AR"
  "BE\020\001\"\373\001\n\006Camera\022\014\n\004name\030\001 \001(\014\022\023\n\013image_w"
  "idth\030\002 \001(\005\022\024\n\014image_height\030\003 \001(\005\022:\n\rtf_m"
  "ap_camera\030\004 \001(\0132#.perception.config.Tran"
  "sformation3d\022\n\n\002fx\030\005 \001(\001\022\n\n\002fy\030\006 \001(\001\022\n\n\002"
  "cx\030\007 \001(\001\022\n\n\002cy\030\010 \001(\001\022\031\n\021radial_distortio"
  "n\030\t \003(\001\022\r\n\005topic\030\n \001(\014\022\023\n\013time_synced\030\013 "
  "\001(\010\022\r\n\005usage\030\014 \001(\005\"\306\001\n\013ConfigGroup\022\022\n\ngr"
  "oup_name\030\001 \001(\014\022\022\n\ngroup_type\030\002 \001(\005\0229\n\014tf"
  "_map_group\030\003 \001(\0132#.perception.config.Tra"
  "nsformation3d\022(\n\006radars\030\004 \003(\0132\030.percepti"
  "on.config.Radar\022*\n\007cameras\030\005 \003(\0132\031.perce"
  "ption.config.CameraB\017B\013GroupConfigP\000b\006pr"
  "oto3"
  ;
static const ::_pbi::DescriptorTable* const descriptor_table_esurfing_2fperc_2fgroup_2eproto_deps[1] = {
  &::descriptor_table_esurfing_2fmath_2fgeometry_2eproto,
};
static ::_pbi::once_flag descriptor_table_esurfing_2fperc_2fgroup_2eproto_once;
const ::_pbi::DescriptorTable descriptor_table_esurfing_2fperc_2fgroup_2eproto = {
    false, false, 1484, descriptor_table_protodef_esurfing_2fperc_2fgroup_2eproto,
    "esurfing/perc/group.proto",
    &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once, descriptor_table_esurfing_2fperc_2fgroup_2eproto_deps, 1, 5,
    schemas, file_default_instances, TableStruct_esurfing_2fperc_2fgroup_2eproto::offsets,
    file_level_metadata_esurfing_2fperc_2fgroup_2eproto, file_level_enum_descriptors_esurfing_2fperc_2fgroup_2eproto,
    file_level_service_descriptors_esurfing_2fperc_2fgroup_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::_pbi::DescriptorTable* descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter() {
  return &descriptor_table_esurfing_2fperc_2fgroup_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY2 static ::_pbi::AddDescriptorsRunner dynamic_init_dummy_esurfing_2fperc_2fgroup_2eproto(&descriptor_table_esurfing_2fperc_2fgroup_2eproto);
namespace perception {
namespace config {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Lidar_Type_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_esurfing_2fperc_2fgroup_2eproto);
  return file_level_enum_descriptors_esurfing_2fperc_2fgroup_2eproto[0];
}
bool Lidar_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Lidar_Type Lidar::UNKNOWN;
constexpr Lidar_Type Lidar::VLP_16;
constexpr Lidar_Type Lidar::VLP_32;
constexpr Lidar_Type Lidar::HESAI_64;
constexpr Lidar_Type Lidar::Type_MIN;
constexpr Lidar_Type Lidar::Type_MAX;
constexpr int Lidar::Type_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Radar_Type_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_esurfing_2fperc_2fgroup_2eproto);
  return file_level_enum_descriptors_esurfing_2fperc_2fgroup_2eproto[1];
}
bool Radar_Type_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr Radar_Type Radar::UNKNOWN;
constexpr Radar_Type Radar::ARBE;
constexpr Radar_Type Radar::Type_MIN;
constexpr Radar_Type Radar::Type_MAX;
constexpr int Radar::Type_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class LidarIntrisic::_Internal {
 public:
};

LidarIntrisic::LidarIntrisic(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.LidarIntrisic)
}
LidarIntrisic::LidarIntrisic(const LidarIntrisic& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  LidarIntrisic* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.enabled_){}
    , decltype(_impl_.min_intensity_){}
    , decltype(_impl_.max_intensity_){}
    , decltype(_impl_.rot_){}
    , decltype(_impl_.vert_){}
    , decltype(_impl_.dist_){}
    , decltype(_impl_.dist_x_){}
    , decltype(_impl_.dist_y_){}
    , decltype(_impl_.vert_offset_){}
    , decltype(_impl_.horiz_offset_){}
    , decltype(_impl_.focal_distance_){}
    , decltype(_impl_.focal_scope_){}
    , decltype(_impl_.ring_){}
    , decltype(_impl_.cos_rot_){}
    , decltype(_impl_.sin_rot_){}
    , decltype(_impl_.cos_vert_){}
    , decltype(_impl_.sin_vert_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&_impl_.enabled_, &from._impl_.enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.sin_vert_) -
    reinterpret_cast<char*>(&_impl_.enabled_)) + sizeof(_impl_.sin_vert_));
  // @@protoc_insertion_point(copy_constructor:perception.config.LidarIntrisic)
}

inline void LidarIntrisic::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.enabled_){false}
    , decltype(_impl_.min_intensity_){0}
    , decltype(_impl_.max_intensity_){0}
    , decltype(_impl_.rot_){0}
    , decltype(_impl_.vert_){0}
    , decltype(_impl_.dist_){0}
    , decltype(_impl_.dist_x_){0}
    , decltype(_impl_.dist_y_){0}
    , decltype(_impl_.vert_offset_){0}
    , decltype(_impl_.horiz_offset_){0}
    , decltype(_impl_.focal_distance_){0}
    , decltype(_impl_.focal_scope_){0}
    , decltype(_impl_.ring_){0}
    , decltype(_impl_.cos_rot_){0}
    , decltype(_impl_.sin_rot_){0}
    , decltype(_impl_.cos_vert_){0}
    , decltype(_impl_.sin_vert_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
}

LidarIntrisic::~LidarIntrisic() {
  // @@protoc_insertion_point(destructor:perception.config.LidarIntrisic)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void LidarIntrisic::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LidarIntrisic::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void LidarIntrisic::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.LidarIntrisic)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&_impl_.enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.sin_vert_) -
      reinterpret_cast<char*>(&_impl_.enabled_)) + sizeof(_impl_.sin_vert_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LidarIntrisic::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _impl_.enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 min_intensity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.min_intensity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 max_intensity = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.max_intensity_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float rot = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.rot_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float vert = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.vert_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float dist = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          _impl_.dist_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float dist_x = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          _impl_.dist_x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float dist_y = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          _impl_.dist_y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float vert_offset = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          _impl_.vert_offset_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float horiz_offset = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          _impl_.horiz_offset_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float focal_distance = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 93)) {
          _impl_.focal_distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float focal_scope = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 101)) {
          _impl_.focal_scope_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 ring = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          _impl_.ring_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float cos_rot = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 117)) {
          _impl_.cos_rot_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float sin_rot = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 125)) {
          _impl_.sin_rot_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float cos_vert = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 133)) {
          _impl_.cos_vert_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float sin_vert = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 141)) {
          _impl_.sin_vert_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LidarIntrisic::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.LidarIntrisic)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  // int32 min_intensity = 2;
  if (this->_internal_min_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_min_intensity(), target);
  }

  // int32 max_intensity = 3;
  if (this->_internal_max_intensity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_max_intensity(), target);
  }

  // float rot = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rot = this->_internal_rot();
  uint32_t raw_rot;
  memcpy(&raw_rot, &tmp_rot, sizeof(tmp_rot));
  if (raw_rot != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_rot(), target);
  }

  // float vert = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert = this->_internal_vert();
  uint32_t raw_vert;
  memcpy(&raw_vert, &tmp_vert, sizeof(tmp_vert));
  if (raw_vert != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_vert(), target);
  }

  // float dist = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist = this->_internal_dist();
  uint32_t raw_dist;
  memcpy(&raw_dist, &tmp_dist, sizeof(tmp_dist));
  if (raw_dist != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(6, this->_internal_dist(), target);
  }

  // float dist_x = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_x = this->_internal_dist_x();
  uint32_t raw_dist_x;
  memcpy(&raw_dist_x, &tmp_dist_x, sizeof(tmp_dist_x));
  if (raw_dist_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(7, this->_internal_dist_x(), target);
  }

  // float dist_y = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_y = this->_internal_dist_y();
  uint32_t raw_dist_y;
  memcpy(&raw_dist_y, &tmp_dist_y, sizeof(tmp_dist_y));
  if (raw_dist_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(8, this->_internal_dist_y(), target);
  }

  // float vert_offset = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert_offset = this->_internal_vert_offset();
  uint32_t raw_vert_offset;
  memcpy(&raw_vert_offset, &tmp_vert_offset, sizeof(tmp_vert_offset));
  if (raw_vert_offset != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(9, this->_internal_vert_offset(), target);
  }

  // float horiz_offset = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_horiz_offset = this->_internal_horiz_offset();
  uint32_t raw_horiz_offset;
  memcpy(&raw_horiz_offset, &tmp_horiz_offset, sizeof(tmp_horiz_offset));
  if (raw_horiz_offset != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(10, this->_internal_horiz_offset(), target);
  }

  // float focal_distance = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_distance = this->_internal_focal_distance();
  uint32_t raw_focal_distance;
  memcpy(&raw_focal_distance, &tmp_focal_distance, sizeof(tmp_focal_distance));
  if (raw_focal_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(11, this->_internal_focal_distance(), target);
  }

  // float focal_scope = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_scope = this->_internal_focal_scope();
  uint32_t raw_focal_scope;
  memcpy(&raw_focal_scope, &tmp_focal_scope, sizeof(tmp_focal_scope));
  if (raw_focal_scope != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(12, this->_internal_focal_scope(), target);
  }

  // int32 ring = 13;
  if (this->_internal_ring() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(13, this->_internal_ring(), target);
  }

  // float cos_rot = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_rot = this->_internal_cos_rot();
  uint32_t raw_cos_rot;
  memcpy(&raw_cos_rot, &tmp_cos_rot, sizeof(tmp_cos_rot));
  if (raw_cos_rot != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(14, this->_internal_cos_rot(), target);
  }

  // float sin_rot = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_rot = this->_internal_sin_rot();
  uint32_t raw_sin_rot;
  memcpy(&raw_sin_rot, &tmp_sin_rot, sizeof(tmp_sin_rot));
  if (raw_sin_rot != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(15, this->_internal_sin_rot(), target);
  }

  // float cos_vert = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_vert = this->_internal_cos_vert();
  uint32_t raw_cos_vert;
  memcpy(&raw_cos_vert, &tmp_cos_vert, sizeof(tmp_cos_vert));
  if (raw_cos_vert != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(16, this->_internal_cos_vert(), target);
  }

  // float sin_vert = 17;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_vert = this->_internal_sin_vert();
  uint32_t raw_sin_vert;
  memcpy(&raw_sin_vert, &tmp_sin_vert, sizeof(tmp_sin_vert));
  if (raw_sin_vert != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(17, this->_internal_sin_vert(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.LidarIntrisic)
  return target;
}

size_t LidarIntrisic::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.LidarIntrisic)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  // int32 min_intensity = 2;
  if (this->_internal_min_intensity() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_min_intensity());
  }

  // int32 max_intensity = 3;
  if (this->_internal_max_intensity() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_max_intensity());
  }

  // float rot = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rot = this->_internal_rot();
  uint32_t raw_rot;
  memcpy(&raw_rot, &tmp_rot, sizeof(tmp_rot));
  if (raw_rot != 0) {
    total_size += 1 + 4;
  }

  // float vert = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert = this->_internal_vert();
  uint32_t raw_vert;
  memcpy(&raw_vert, &tmp_vert, sizeof(tmp_vert));
  if (raw_vert != 0) {
    total_size += 1 + 4;
  }

  // float dist = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist = this->_internal_dist();
  uint32_t raw_dist;
  memcpy(&raw_dist, &tmp_dist, sizeof(tmp_dist));
  if (raw_dist != 0) {
    total_size += 1 + 4;
  }

  // float dist_x = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_x = this->_internal_dist_x();
  uint32_t raw_dist_x;
  memcpy(&raw_dist_x, &tmp_dist_x, sizeof(tmp_dist_x));
  if (raw_dist_x != 0) {
    total_size += 1 + 4;
  }

  // float dist_y = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_y = this->_internal_dist_y();
  uint32_t raw_dist_y;
  memcpy(&raw_dist_y, &tmp_dist_y, sizeof(tmp_dist_y));
  if (raw_dist_y != 0) {
    total_size += 1 + 4;
  }

  // float vert_offset = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert_offset = this->_internal_vert_offset();
  uint32_t raw_vert_offset;
  memcpy(&raw_vert_offset, &tmp_vert_offset, sizeof(tmp_vert_offset));
  if (raw_vert_offset != 0) {
    total_size += 1 + 4;
  }

  // float horiz_offset = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_horiz_offset = this->_internal_horiz_offset();
  uint32_t raw_horiz_offset;
  memcpy(&raw_horiz_offset, &tmp_horiz_offset, sizeof(tmp_horiz_offset));
  if (raw_horiz_offset != 0) {
    total_size += 1 + 4;
  }

  // float focal_distance = 11;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_distance = this->_internal_focal_distance();
  uint32_t raw_focal_distance;
  memcpy(&raw_focal_distance, &tmp_focal_distance, sizeof(tmp_focal_distance));
  if (raw_focal_distance != 0) {
    total_size += 1 + 4;
  }

  // float focal_scope = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_scope = this->_internal_focal_scope();
  uint32_t raw_focal_scope;
  memcpy(&raw_focal_scope, &tmp_focal_scope, sizeof(tmp_focal_scope));
  if (raw_focal_scope != 0) {
    total_size += 1 + 4;
  }

  // int32 ring = 13;
  if (this->_internal_ring() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_ring());
  }

  // float cos_rot = 14;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_rot = this->_internal_cos_rot();
  uint32_t raw_cos_rot;
  memcpy(&raw_cos_rot, &tmp_cos_rot, sizeof(tmp_cos_rot));
  if (raw_cos_rot != 0) {
    total_size += 1 + 4;
  }

  // float sin_rot = 15;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_rot = this->_internal_sin_rot();
  uint32_t raw_sin_rot;
  memcpy(&raw_sin_rot, &tmp_sin_rot, sizeof(tmp_sin_rot));
  if (raw_sin_rot != 0) {
    total_size += 1 + 4;
  }

  // float cos_vert = 16;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_vert = this->_internal_cos_vert();
  uint32_t raw_cos_vert;
  memcpy(&raw_cos_vert, &tmp_cos_vert, sizeof(tmp_cos_vert));
  if (raw_cos_vert != 0) {
    total_size += 2 + 4;
  }

  // float sin_vert = 17;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_vert = this->_internal_sin_vert();
  uint32_t raw_sin_vert;
  memcpy(&raw_sin_vert, &tmp_sin_vert, sizeof(tmp_sin_vert));
  if (raw_sin_vert != 0) {
    total_size += 2 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LidarIntrisic::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    LidarIntrisic::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LidarIntrisic::GetClassData() const { return &_class_data_; }


void LidarIntrisic::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<LidarIntrisic*>(&to_msg);
  auto& from = static_cast<const LidarIntrisic&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.LidarIntrisic)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _this->_internal_set_enabled(from._internal_enabled());
  }
  if (from._internal_min_intensity() != 0) {
    _this->_internal_set_min_intensity(from._internal_min_intensity());
  }
  if (from._internal_max_intensity() != 0) {
    _this->_internal_set_max_intensity(from._internal_max_intensity());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_rot = from._internal_rot();
  uint32_t raw_rot;
  memcpy(&raw_rot, &tmp_rot, sizeof(tmp_rot));
  if (raw_rot != 0) {
    _this->_internal_set_rot(from._internal_rot());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert = from._internal_vert();
  uint32_t raw_vert;
  memcpy(&raw_vert, &tmp_vert, sizeof(tmp_vert));
  if (raw_vert != 0) {
    _this->_internal_set_vert(from._internal_vert());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist = from._internal_dist();
  uint32_t raw_dist;
  memcpy(&raw_dist, &tmp_dist, sizeof(tmp_dist));
  if (raw_dist != 0) {
    _this->_internal_set_dist(from._internal_dist());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_x = from._internal_dist_x();
  uint32_t raw_dist_x;
  memcpy(&raw_dist_x, &tmp_dist_x, sizeof(tmp_dist_x));
  if (raw_dist_x != 0) {
    _this->_internal_set_dist_x(from._internal_dist_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_dist_y = from._internal_dist_y();
  uint32_t raw_dist_y;
  memcpy(&raw_dist_y, &tmp_dist_y, sizeof(tmp_dist_y));
  if (raw_dist_y != 0) {
    _this->_internal_set_dist_y(from._internal_dist_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_vert_offset = from._internal_vert_offset();
  uint32_t raw_vert_offset;
  memcpy(&raw_vert_offset, &tmp_vert_offset, sizeof(tmp_vert_offset));
  if (raw_vert_offset != 0) {
    _this->_internal_set_vert_offset(from._internal_vert_offset());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_horiz_offset = from._internal_horiz_offset();
  uint32_t raw_horiz_offset;
  memcpy(&raw_horiz_offset, &tmp_horiz_offset, sizeof(tmp_horiz_offset));
  if (raw_horiz_offset != 0) {
    _this->_internal_set_horiz_offset(from._internal_horiz_offset());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_distance = from._internal_focal_distance();
  uint32_t raw_focal_distance;
  memcpy(&raw_focal_distance, &tmp_focal_distance, sizeof(tmp_focal_distance));
  if (raw_focal_distance != 0) {
    _this->_internal_set_focal_distance(from._internal_focal_distance());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_focal_scope = from._internal_focal_scope();
  uint32_t raw_focal_scope;
  memcpy(&raw_focal_scope, &tmp_focal_scope, sizeof(tmp_focal_scope));
  if (raw_focal_scope != 0) {
    _this->_internal_set_focal_scope(from._internal_focal_scope());
  }
  if (from._internal_ring() != 0) {
    _this->_internal_set_ring(from._internal_ring());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_rot = from._internal_cos_rot();
  uint32_t raw_cos_rot;
  memcpy(&raw_cos_rot, &tmp_cos_rot, sizeof(tmp_cos_rot));
  if (raw_cos_rot != 0) {
    _this->_internal_set_cos_rot(from._internal_cos_rot());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_rot = from._internal_sin_rot();
  uint32_t raw_sin_rot;
  memcpy(&raw_sin_rot, &tmp_sin_rot, sizeof(tmp_sin_rot));
  if (raw_sin_rot != 0) {
    _this->_internal_set_sin_rot(from._internal_sin_rot());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_cos_vert = from._internal_cos_vert();
  uint32_t raw_cos_vert;
  memcpy(&raw_cos_vert, &tmp_cos_vert, sizeof(tmp_cos_vert));
  if (raw_cos_vert != 0) {
    _this->_internal_set_cos_vert(from._internal_cos_vert());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_sin_vert = from._internal_sin_vert();
  uint32_t raw_sin_vert;
  memcpy(&raw_sin_vert, &tmp_sin_vert, sizeof(tmp_sin_vert));
  if (raw_sin_vert != 0) {
    _this->_internal_set_sin_vert(from._internal_sin_vert());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LidarIntrisic::CopyFrom(const LidarIntrisic& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.LidarIntrisic)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LidarIntrisic::IsInitialized() const {
  return true;
}

void LidarIntrisic::InternalSwap(LidarIntrisic* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LidarIntrisic, _impl_.sin_vert_)
      + sizeof(LidarIntrisic::_impl_.sin_vert_)
      - PROTOBUF_FIELD_OFFSET(LidarIntrisic, _impl_.enabled_)>(
          reinterpret_cast<char*>(&_impl_.enabled_),
          reinterpret_cast<char*>(&other->_impl_.enabled_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LidarIntrisic::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_2eproto[0]);
}

// ===================================================================

class Lidar::_Internal {
 public:
  static const ::perception::config::Transformation3d& tf_map_lidar(const Lidar* msg);
};

const ::perception::config::Transformation3d&
Lidar::_Internal::tf_map_lidar(const Lidar* msg) {
  return *msg->_impl_.tf_map_lidar_;
}
void Lidar::clear_tf_map_lidar() {
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_lidar_ != nullptr) {
    delete _impl_.tf_map_lidar_;
  }
  _impl_.tf_map_lidar_ = nullptr;
}
Lidar::Lidar(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Lidar)
}
Lidar::Lidar(const Lidar& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Lidar* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.intrinsics_){from._impl_.intrinsics_}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_lidar_){nullptr}
    , decltype(_impl_.type_){}
    , decltype(_impl_.min_distance_){}
    , decltype(_impl_.max_distance_){}
    , decltype(_impl_.usage_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_topic().empty()) {
    _this->_impl_.topic_.Set(from._internal_topic(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_tf_map_lidar()) {
    _this->_impl_.tf_map_lidar_ = new ::perception::config::Transformation3d(*from._impl_.tf_map_lidar_);
  }
  ::memcpy(&_impl_.type_, &from._impl_.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.usage_) -
    reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.usage_));
  // @@protoc_insertion_point(copy_constructor:perception.config.Lidar)
}

inline void Lidar::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.intrinsics_){arena}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_lidar_){nullptr}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.min_distance_){0}
    , decltype(_impl_.max_distance_){0}
    , decltype(_impl_.usage_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Lidar::~Lidar() {
  // @@protoc_insertion_point(destructor:perception.config.Lidar)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Lidar::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.intrinsics_.~RepeatedPtrField();
  _impl_.name_.Destroy();
  _impl_.topic_.Destroy();
  if (this != internal_default_instance()) delete _impl_.tf_map_lidar_;
}

void Lidar::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Lidar::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Lidar)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.intrinsics_.Clear();
  _impl_.name_.ClearToEmpty();
  _impl_.topic_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_lidar_ != nullptr) {
    delete _impl_.tf_map_lidar_;
  }
  _impl_.tf_map_lidar_ = nullptr;
  ::memset(&_impl_.type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.usage_) -
      reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.usage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Lidar::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Lidar.Type type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::perception::config::Lidar_Type>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.config.LidarIntrisic intrinsics = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_intrinsics(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float min_distance = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.min_distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_distance = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.max_distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Transformation3d tf_map_lidar = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_tf_map_lidar(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes topic = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_topic();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 usage = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.usage_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Lidar::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Lidar)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .perception.config.Lidar.Type type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_intrinsics_size()); i < n; i++) {
    const auto& repfield = this->_internal_intrinsics(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  // float min_distance = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = this->_internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_min_distance(), target);
  }

  // float max_distance = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = this->_internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_max_distance(), target);
  }

  // .perception.config.Transformation3d tf_map_lidar = 6;
  if (this->_internal_has_tf_map_lidar()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::tf_map_lidar(this),
        _Internal::tf_map_lidar(this).GetCachedSize(), target, stream);
  }

  // bytes topic = 7;
  if (!this->_internal_topic().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_topic(), target);
  }

  // int32 usage = 8;
  if (this->_internal_usage() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_usage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Lidar)
  return target;
}

size_t Lidar::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Lidar)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  total_size += 1UL * this->_internal_intrinsics_size();
  for (const auto& msg : this->_impl_.intrinsics_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_name());
  }

  // bytes topic = 7;
  if (!this->_internal_topic().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_topic());
  }

  // .perception.config.Transformation3d tf_map_lidar = 6;
  if (this->_internal_has_tf_map_lidar()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.tf_map_lidar_);
  }

  // .perception.config.Lidar.Type type = 2;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  // float min_distance = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = this->_internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    total_size += 1 + 4;
  }

  // float max_distance = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = this->_internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    total_size += 1 + 4;
  }

  // int32 usage = 8;
  if (this->_internal_usage() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_usage());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Lidar::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Lidar::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Lidar::GetClassData() const { return &_class_data_; }


void Lidar::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Lidar*>(&to_msg);
  auto& from = static_cast<const Lidar&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Lidar)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.intrinsics_.MergeFrom(from._impl_.intrinsics_);
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_topic().empty()) {
    _this->_internal_set_topic(from._internal_topic());
  }
  if (from._internal_has_tf_map_lidar()) {
    _this->_internal_mutable_tf_map_lidar()->::perception::config::Transformation3d::MergeFrom(
        from._internal_tf_map_lidar());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = from._internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    _this->_internal_set_min_distance(from._internal_min_distance());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = from._internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    _this->_internal_set_max_distance(from._internal_max_distance());
  }
  if (from._internal_usage() != 0) {
    _this->_internal_set_usage(from._internal_usage());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Lidar::CopyFrom(const Lidar& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Lidar)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Lidar::IsInitialized() const {
  return true;
}

void Lidar::InternalSwap(Lidar* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.intrinsics_.InternalSwap(&other->_impl_.intrinsics_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.topic_, lhs_arena,
      &other->_impl_.topic_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Lidar, _impl_.usage_)
      + sizeof(Lidar::_impl_.usage_)
      - PROTOBUF_FIELD_OFFSET(Lidar, _impl_.tf_map_lidar_)>(
          reinterpret_cast<char*>(&_impl_.tf_map_lidar_),
          reinterpret_cast<char*>(&other->_impl_.tf_map_lidar_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Lidar::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_2eproto[1]);
}

// ===================================================================

class Radar::_Internal {
 public:
  static const ::perception::config::Transformation3d& tf_map_radar(const Radar* msg);
};

const ::perception::config::Transformation3d&
Radar::_Internal::tf_map_radar(const Radar* msg) {
  return *msg->_impl_.tf_map_radar_;
}
void Radar::clear_tf_map_radar() {
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_radar_ != nullptr) {
    delete _impl_.tf_map_radar_;
  }
  _impl_.tf_map_radar_ = nullptr;
}
Radar::Radar(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Radar)
}
Radar::Radar(const Radar& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Radar* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.intrinsics_){from._impl_.intrinsics_}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_radar_){nullptr}
    , decltype(_impl_.type_){}
    , decltype(_impl_.min_distance_){}
    , decltype(_impl_.max_distance_){}
    , decltype(_impl_.usage_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_topic().empty()) {
    _this->_impl_.topic_.Set(from._internal_topic(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_tf_map_radar()) {
    _this->_impl_.tf_map_radar_ = new ::perception::config::Transformation3d(*from._impl_.tf_map_radar_);
  }
  ::memcpy(&_impl_.type_, &from._impl_.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.usage_) -
    reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.usage_));
  // @@protoc_insertion_point(copy_constructor:perception.config.Radar)
}

inline void Radar::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.intrinsics_){arena}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_radar_){nullptr}
    , decltype(_impl_.type_){0}
    , decltype(_impl_.min_distance_){0}
    , decltype(_impl_.max_distance_){0}
    , decltype(_impl_.usage_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Radar::~Radar() {
  // @@protoc_insertion_point(destructor:perception.config.Radar)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Radar::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.intrinsics_.~RepeatedPtrField();
  _impl_.name_.Destroy();
  _impl_.topic_.Destroy();
  if (this != internal_default_instance()) delete _impl_.tf_map_radar_;
}

void Radar::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Radar::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Radar)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.intrinsics_.Clear();
  _impl_.name_.ClearToEmpty();
  _impl_.topic_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_radar_ != nullptr) {
    delete _impl_.tf_map_radar_;
  }
  _impl_.tf_map_radar_ = nullptr;
  ::memset(&_impl_.type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.usage_) -
      reinterpret_cast<char*>(&_impl_.type_)) + sizeof(_impl_.usage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Radar::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Radar.Type type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::perception::config::Radar_Type>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.config.LidarIntrisic intrinsics = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_intrinsics(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float min_distance = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          _impl_.min_distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_distance = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          _impl_.max_distance_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Transformation3d tf_map_radar = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_tf_map_radar(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes topic = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_topic();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 usage = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          _impl_.usage_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Radar::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Radar)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .perception.config.Radar.Type type = 2;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteEnumToArray(
      2, this->_internal_type(), target);
  }

  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_intrinsics_size()); i < n; i++) {
    const auto& repfield = this->_internal_intrinsics(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(3, repfield, repfield.GetCachedSize(), target, stream);
  }

  // float min_distance = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = this->_internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(4, this->_internal_min_distance(), target);
  }

  // float max_distance = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = this->_internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteFloatToArray(5, this->_internal_max_distance(), target);
  }

  // .perception.config.Transformation3d tf_map_radar = 6;
  if (this->_internal_has_tf_map_radar()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, _Internal::tf_map_radar(this),
        _Internal::tf_map_radar(this).GetCachedSize(), target, stream);
  }

  // bytes topic = 7;
  if (!this->_internal_topic().empty()) {
    target = stream->WriteBytesMaybeAliased(
        7, this->_internal_topic(), target);
  }

  // int32 usage = 8;
  if (this->_internal_usage() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(8, this->_internal_usage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Radar)
  return target;
}

size_t Radar::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Radar)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.config.LidarIntrisic intrinsics = 3;
  total_size += 1UL * this->_internal_intrinsics_size();
  for (const auto& msg : this->_impl_.intrinsics_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_name());
  }

  // bytes topic = 7;
  if (!this->_internal_topic().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_topic());
  }

  // .perception.config.Transformation3d tf_map_radar = 6;
  if (this->_internal_has_tf_map_radar()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.tf_map_radar_);
  }

  // .perception.config.Radar.Type type = 2;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::_pbi::WireFormatLite::EnumSize(this->_internal_type());
  }

  // float min_distance = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = this->_internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    total_size += 1 + 4;
  }

  // float max_distance = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = this->_internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    total_size += 1 + 4;
  }

  // int32 usage = 8;
  if (this->_internal_usage() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_usage());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Radar::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Radar::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Radar::GetClassData() const { return &_class_data_; }


void Radar::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Radar*>(&to_msg);
  auto& from = static_cast<const Radar&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Radar)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.intrinsics_.MergeFrom(from._impl_.intrinsics_);
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_topic().empty()) {
    _this->_internal_set_topic(from._internal_topic());
  }
  if (from._internal_has_tf_map_radar()) {
    _this->_internal_mutable_tf_map_radar()->::perception::config::Transformation3d::MergeFrom(
        from._internal_tf_map_radar());
  }
  if (from._internal_type() != 0) {
    _this->_internal_set_type(from._internal_type());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_distance = from._internal_min_distance();
  uint32_t raw_min_distance;
  memcpy(&raw_min_distance, &tmp_min_distance, sizeof(tmp_min_distance));
  if (raw_min_distance != 0) {
    _this->_internal_set_min_distance(from._internal_min_distance());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_distance = from._internal_max_distance();
  uint32_t raw_max_distance;
  memcpy(&raw_max_distance, &tmp_max_distance, sizeof(tmp_max_distance));
  if (raw_max_distance != 0) {
    _this->_internal_set_max_distance(from._internal_max_distance());
  }
  if (from._internal_usage() != 0) {
    _this->_internal_set_usage(from._internal_usage());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Radar::CopyFrom(const Radar& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Radar)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Radar::IsInitialized() const {
  return true;
}

void Radar::InternalSwap(Radar* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.intrinsics_.InternalSwap(&other->_impl_.intrinsics_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.topic_, lhs_arena,
      &other->_impl_.topic_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Radar, _impl_.usage_)
      + sizeof(Radar::_impl_.usage_)
      - PROTOBUF_FIELD_OFFSET(Radar, _impl_.tf_map_radar_)>(
          reinterpret_cast<char*>(&_impl_.tf_map_radar_),
          reinterpret_cast<char*>(&other->_impl_.tf_map_radar_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Radar::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_2eproto[2]);
}

// ===================================================================

class Camera::_Internal {
 public:
  static const ::perception::config::Transformation3d& tf_map_camera(const Camera* msg);
};

const ::perception::config::Transformation3d&
Camera::_Internal::tf_map_camera(const Camera* msg) {
  return *msg->_impl_.tf_map_camera_;
}
void Camera::clear_tf_map_camera() {
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_camera_ != nullptr) {
    delete _impl_.tf_map_camera_;
  }
  _impl_.tf_map_camera_ = nullptr;
}
Camera::Camera(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.Camera)
}
Camera::Camera(const Camera& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  Camera* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.radial_distortion_){from._impl_.radial_distortion_}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_camera_){nullptr}
    , decltype(_impl_.image_width_){}
    , decltype(_impl_.image_height_){}
    , decltype(_impl_.fx_){}
    , decltype(_impl_.fy_){}
    , decltype(_impl_.cx_){}
    , decltype(_impl_.cy_){}
    , decltype(_impl_.time_synced_){}
    , decltype(_impl_.usage_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    _this->_impl_.name_.Set(from._internal_name(), 
      _this->GetArenaForAllocation());
  }
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_topic().empty()) {
    _this->_impl_.topic_.Set(from._internal_topic(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_tf_map_camera()) {
    _this->_impl_.tf_map_camera_ = new ::perception::config::Transformation3d(*from._impl_.tf_map_camera_);
  }
  ::memcpy(&_impl_.image_width_, &from._impl_.image_width_,
    static_cast<size_t>(reinterpret_cast<char*>(&_impl_.usage_) -
    reinterpret_cast<char*>(&_impl_.image_width_)) + sizeof(_impl_.usage_));
  // @@protoc_insertion_point(copy_constructor:perception.config.Camera)
}

inline void Camera::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.radial_distortion_){arena}
    , decltype(_impl_.name_){}
    , decltype(_impl_.topic_){}
    , decltype(_impl_.tf_map_camera_){nullptr}
    , decltype(_impl_.image_width_){0}
    , decltype(_impl_.image_height_){0}
    , decltype(_impl_.fx_){0}
    , decltype(_impl_.fy_){0}
    , decltype(_impl_.cx_){0}
    , decltype(_impl_.cy_){0}
    , decltype(_impl_.time_synced_){false}
    , decltype(_impl_.usage_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  _impl_.topic_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.topic_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

Camera::~Camera() {
  // @@protoc_insertion_point(destructor:perception.config.Camera)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void Camera::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.radial_distortion_.~RepeatedField();
  _impl_.name_.Destroy();
  _impl_.topic_.Destroy();
  if (this != internal_default_instance()) delete _impl_.tf_map_camera_;
}

void Camera::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void Camera::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.Camera)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.radial_distortion_.Clear();
  _impl_.name_.ClearToEmpty();
  _impl_.topic_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_camera_ != nullptr) {
    delete _impl_.tf_map_camera_;
  }
  _impl_.tf_map_camera_ = nullptr;
  ::memset(&_impl_.image_width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&_impl_.usage_) -
      reinterpret_cast<char*>(&_impl_.image_width_)) + sizeof(_impl_.usage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Camera::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 image_width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.image_width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 image_height = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _impl_.image_height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Transformation3d tf_map_camera = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_tf_map_camera(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double fx = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          _impl_.fx_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double fy = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 49)) {
          _impl_.fy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double cx = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          _impl_.cx_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double cy = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 65)) {
          _impl_.cy_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // repeated double radial_distortion = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_radial_distortion(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 73) {
          _internal_add_radial_distortion(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // bytes topic = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_topic();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool time_synced = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          _impl_.time_synced_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 usage = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          _impl_.usage_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Camera::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.Camera)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_name(), target);
  }

  // int32 image_width = 2;
  if (this->_internal_image_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_image_width(), target);
  }

  // int32 image_height = 3;
  if (this->_internal_image_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(3, this->_internal_image_height(), target);
  }

  // .perception.config.Transformation3d tf_map_camera = 4;
  if (this->_internal_has_tf_map_camera()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, _Internal::tf_map_camera(this),
        _Internal::tf_map_camera(this).GetCachedSize(), target, stream);
  }

  // double fx = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fx = this->_internal_fx();
  uint64_t raw_fx;
  memcpy(&raw_fx, &tmp_fx, sizeof(tmp_fx));
  if (raw_fx != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(5, this->_internal_fx(), target);
  }

  // double fy = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fy = this->_internal_fy();
  uint64_t raw_fy;
  memcpy(&raw_fy, &tmp_fy, sizeof(tmp_fy));
  if (raw_fy != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(6, this->_internal_fy(), target);
  }

  // double cx = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cx = this->_internal_cx();
  uint64_t raw_cx;
  memcpy(&raw_cx, &tmp_cx, sizeof(tmp_cx));
  if (raw_cx != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(7, this->_internal_cx(), target);
  }

  // double cy = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cy = this->_internal_cy();
  uint64_t raw_cy;
  memcpy(&raw_cy, &tmp_cy, sizeof(tmp_cy));
  if (raw_cy != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteDoubleToArray(8, this->_internal_cy(), target);
  }

  // repeated double radial_distortion = 9;
  if (this->_internal_radial_distortion_size() > 0) {
    target = stream->WriteFixedPacked(9, _internal_radial_distortion(), target);
  }

  // bytes topic = 10;
  if (!this->_internal_topic().empty()) {
    target = stream->WriteBytesMaybeAliased(
        10, this->_internal_topic(), target);
  }

  // bool time_synced = 11;
  if (this->_internal_time_synced() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteBoolToArray(11, this->_internal_time_synced(), target);
  }

  // int32 usage = 12;
  if (this->_internal_usage() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(12, this->_internal_usage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.Camera)
  return target;
}

size_t Camera::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.Camera)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated double radial_distortion = 9;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_radial_distortion_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::_pbi::WireFormatLite::Int32Size(static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // bytes name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_name());
  }

  // bytes topic = 10;
  if (!this->_internal_topic().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_topic());
  }

  // .perception.config.Transformation3d tf_map_camera = 4;
  if (this->_internal_has_tf_map_camera()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.tf_map_camera_);
  }

  // int32 image_width = 2;
  if (this->_internal_image_width() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_image_width());
  }

  // int32 image_height = 3;
  if (this->_internal_image_height() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_image_height());
  }

  // double fx = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fx = this->_internal_fx();
  uint64_t raw_fx;
  memcpy(&raw_fx, &tmp_fx, sizeof(tmp_fx));
  if (raw_fx != 0) {
    total_size += 1 + 8;
  }

  // double fy = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fy = this->_internal_fy();
  uint64_t raw_fy;
  memcpy(&raw_fy, &tmp_fy, sizeof(tmp_fy));
  if (raw_fy != 0) {
    total_size += 1 + 8;
  }

  // double cx = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cx = this->_internal_cx();
  uint64_t raw_cx;
  memcpy(&raw_cx, &tmp_cx, sizeof(tmp_cx));
  if (raw_cx != 0) {
    total_size += 1 + 8;
  }

  // double cy = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cy = this->_internal_cy();
  uint64_t raw_cy;
  memcpy(&raw_cy, &tmp_cy, sizeof(tmp_cy));
  if (raw_cy != 0) {
    total_size += 1 + 8;
  }

  // bool time_synced = 11;
  if (this->_internal_time_synced() != 0) {
    total_size += 1 + 1;
  }

  // int32 usage = 12;
  if (this->_internal_usage() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_usage());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Camera::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    Camera::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Camera::GetClassData() const { return &_class_data_; }


void Camera::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<Camera*>(&to_msg);
  auto& from = static_cast<const Camera&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.Camera)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.radial_distortion_.MergeFrom(from._impl_.radial_distortion_);
  if (!from._internal_name().empty()) {
    _this->_internal_set_name(from._internal_name());
  }
  if (!from._internal_topic().empty()) {
    _this->_internal_set_topic(from._internal_topic());
  }
  if (from._internal_has_tf_map_camera()) {
    _this->_internal_mutable_tf_map_camera()->::perception::config::Transformation3d::MergeFrom(
        from._internal_tf_map_camera());
  }
  if (from._internal_image_width() != 0) {
    _this->_internal_set_image_width(from._internal_image_width());
  }
  if (from._internal_image_height() != 0) {
    _this->_internal_set_image_height(from._internal_image_height());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fx = from._internal_fx();
  uint64_t raw_fx;
  memcpy(&raw_fx, &tmp_fx, sizeof(tmp_fx));
  if (raw_fx != 0) {
    _this->_internal_set_fx(from._internal_fx());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_fy = from._internal_fy();
  uint64_t raw_fy;
  memcpy(&raw_fy, &tmp_fy, sizeof(tmp_fy));
  if (raw_fy != 0) {
    _this->_internal_set_fy(from._internal_fy());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cx = from._internal_cx();
  uint64_t raw_cx;
  memcpy(&raw_cx, &tmp_cx, sizeof(tmp_cx));
  if (raw_cx != 0) {
    _this->_internal_set_cx(from._internal_cx());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cy = from._internal_cy();
  uint64_t raw_cy;
  memcpy(&raw_cy, &tmp_cy, sizeof(tmp_cy));
  if (raw_cy != 0) {
    _this->_internal_set_cy(from._internal_cy());
  }
  if (from._internal_time_synced() != 0) {
    _this->_internal_set_time_synced(from._internal_time_synced());
  }
  if (from._internal_usage() != 0) {
    _this->_internal_set_usage(from._internal_usage());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Camera::CopyFrom(const Camera& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.Camera)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Camera::IsInitialized() const {
  return true;
}

void Camera::InternalSwap(Camera* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.radial_distortion_.InternalSwap(&other->_impl_.radial_distortion_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.name_, lhs_arena,
      &other->_impl_.name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.topic_, lhs_arena,
      &other->_impl_.topic_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Camera, _impl_.usage_)
      + sizeof(Camera::_impl_.usage_)
      - PROTOBUF_FIELD_OFFSET(Camera, _impl_.tf_map_camera_)>(
          reinterpret_cast<char*>(&_impl_.tf_map_camera_),
          reinterpret_cast<char*>(&other->_impl_.tf_map_camera_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Camera::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_2eproto[3]);
}

// ===================================================================

class ConfigGroup::_Internal {
 public:
  static const ::perception::config::Transformation3d& tf_map_group(const ConfigGroup* msg);
};

const ::perception::config::Transformation3d&
ConfigGroup::_Internal::tf_map_group(const ConfigGroup* msg) {
  return *msg->_impl_.tf_map_group_;
}
void ConfigGroup::clear_tf_map_group() {
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_group_ != nullptr) {
    delete _impl_.tf_map_group_;
  }
  _impl_.tf_map_group_ = nullptr;
}
ConfigGroup::ConfigGroup(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor(arena, is_message_owned);
  // @@protoc_insertion_point(arena_constructor:perception.config.ConfigGroup)
}
ConfigGroup::ConfigGroup(const ConfigGroup& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  ConfigGroup* const _this = this; (void)_this;
  new (&_impl_) Impl_{
      decltype(_impl_.radars_){from._impl_.radars_}
    , decltype(_impl_.cameras_){from._impl_.cameras_}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.tf_map_group_){nullptr}
    , decltype(_impl_.group_type_){}
    , /*decltype(_impl_._cached_size_)*/{}};

  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_name().empty()) {
    _this->_impl_.group_name_.Set(from._internal_group_name(), 
      _this->GetArenaForAllocation());
  }
  if (from._internal_has_tf_map_group()) {
    _this->_impl_.tf_map_group_ = new ::perception::config::Transformation3d(*from._impl_.tf_map_group_);
  }
  _this->_impl_.group_type_ = from._impl_.group_type_;
  // @@protoc_insertion_point(copy_constructor:perception.config.ConfigGroup)
}

inline void ConfigGroup::SharedCtor(
    ::_pb::Arena* arena, bool is_message_owned) {
  (void)arena;
  (void)is_message_owned;
  new (&_impl_) Impl_{
      decltype(_impl_.radars_){arena}
    , decltype(_impl_.cameras_){arena}
    , decltype(_impl_.group_name_){}
    , decltype(_impl_.tf_map_group_){nullptr}
    , decltype(_impl_.group_type_){0}
    , /*decltype(_impl_._cached_size_)*/{}
  };
  _impl_.group_name_.InitDefault();
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    _impl_.group_name_.Set("", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ConfigGroup::~ConfigGroup() {
  // @@protoc_insertion_point(destructor:perception.config.ConfigGroup)
  if (auto *arena = _internal_metadata_.DeleteReturnArena<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>()) {
  (void)arena;
    return;
  }
  SharedDtor();
}

inline void ConfigGroup::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  _impl_.radars_.~RepeatedPtrField();
  _impl_.cameras_.~RepeatedPtrField();
  _impl_.group_name_.Destroy();
  if (this != internal_default_instance()) delete _impl_.tf_map_group_;
}

void ConfigGroup::SetCachedSize(int size) const {
  _impl_._cached_size_.Set(size);
}

void ConfigGroup::Clear() {
// @@protoc_insertion_point(message_clear_start:perception.config.ConfigGroup)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  _impl_.radars_.Clear();
  _impl_.cameras_.Clear();
  _impl_.group_name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && _impl_.tf_map_group_ != nullptr) {
    delete _impl_.tf_map_group_;
  }
  _impl_.tf_map_group_ = nullptr;
  _impl_.group_type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigGroup::_InternalParse(const char* ptr, ::_pbi::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::_pbi::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes group_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_group_name();
          ptr = ::_pbi::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 group_type = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _impl_.group_type_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .perception.config.Transformation3d tf_map_group = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_tf_map_group(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.config.Radar radars = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_radars(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .perception.config.Camera cameras = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_cameras(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<42>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigGroup::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:perception.config.ConfigGroup)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes group_name = 1;
  if (!this->_internal_group_name().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_group_name(), target);
  }

  // int32 group_type = 2;
  if (this->_internal_group_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::_pbi::WireFormatLite::WriteInt32ToArray(2, this->_internal_group_type(), target);
  }

  // .perception.config.Transformation3d tf_map_group = 3;
  if (this->_internal_has_tf_map_group()) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, _Internal::tf_map_group(this),
        _Internal::tf_map_group(this).GetCachedSize(), target, stream);
  }

  // repeated .perception.config.Radar radars = 4;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_radars_size()); i < n; i++) {
    const auto& repfield = this->_internal_radars(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(4, repfield, repfield.GetCachedSize(), target, stream);
  }

  // repeated .perception.config.Camera cameras = 5;
  for (unsigned i = 0,
      n = static_cast<unsigned>(this->_internal_cameras_size()); i < n; i++) {
    const auto& repfield = this->_internal_cameras(i);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
        InternalWriteMessage(5, repfield, repfield.GetCachedSize(), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::_pbi::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:perception.config.ConfigGroup)
  return target;
}

size_t ConfigGroup::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:perception.config.ConfigGroup)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .perception.config.Radar radars = 4;
  total_size += 1UL * this->_internal_radars_size();
  for (const auto& msg : this->_impl_.radars_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .perception.config.Camera cameras = 5;
  total_size += 1UL * this->_internal_cameras_size();
  for (const auto& msg : this->_impl_.cameras_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // bytes group_name = 1;
  if (!this->_internal_group_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_group_name());
  }

  // .perception.config.Transformation3d tf_map_group = 3;
  if (this->_internal_has_tf_map_group()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *_impl_.tf_map_group_);
  }

  // int32 group_type = 2;
  if (this->_internal_group_type() != 0) {
    total_size += ::_pbi::WireFormatLite::Int32SizePlusOne(this->_internal_group_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_impl_._cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigGroup::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSourceCheck,
    ConfigGroup::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigGroup::GetClassData() const { return &_class_data_; }


void ConfigGroup::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg) {
  auto* const _this = static_cast<ConfigGroup*>(&to_msg);
  auto& from = static_cast<const ConfigGroup&>(from_msg);
  // @@protoc_insertion_point(class_specific_merge_from_start:perception.config.ConfigGroup)
  GOOGLE_DCHECK_NE(&from, _this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  _this->_impl_.radars_.MergeFrom(from._impl_.radars_);
  _this->_impl_.cameras_.MergeFrom(from._impl_.cameras_);
  if (!from._internal_group_name().empty()) {
    _this->_internal_set_group_name(from._internal_group_name());
  }
  if (from._internal_has_tf_map_group()) {
    _this->_internal_mutable_tf_map_group()->::perception::config::Transformation3d::MergeFrom(
        from._internal_tf_map_group());
  }
  if (from._internal_group_type() != 0) {
    _this->_internal_set_group_type(from._internal_group_type());
  }
  _this->_internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigGroup::CopyFrom(const ConfigGroup& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:perception.config.ConfigGroup)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigGroup::IsInitialized() const {
  return true;
}

void ConfigGroup::InternalSwap(ConfigGroup* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  _impl_.radars_.InternalSwap(&other->_impl_.radars_);
  _impl_.cameras_.InternalSwap(&other->_impl_.cameras_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &_impl_.group_name_, lhs_arena,
      &other->_impl_.group_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConfigGroup, _impl_.group_type_)
      + sizeof(ConfigGroup::_impl_.group_type_)
      - PROTOBUF_FIELD_OFFSET(ConfigGroup, _impl_.tf_map_group_)>(
          reinterpret_cast<char*>(&_impl_.tf_map_group_),
          reinterpret_cast<char*>(&other->_impl_.tf_map_group_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigGroup::GetMetadata() const {
  return ::_pbi::AssignDescriptors(
      &descriptor_table_esurfing_2fperc_2fgroup_2eproto_getter, &descriptor_table_esurfing_2fperc_2fgroup_2eproto_once,
      file_level_metadata_esurfing_2fperc_2fgroup_2eproto[4]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace config
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::perception::config::LidarIntrisic*
Arena::CreateMaybeMessage< ::perception::config::LidarIntrisic >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::LidarIntrisic >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::Lidar*
Arena::CreateMaybeMessage< ::perception::config::Lidar >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Lidar >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::Radar*
Arena::CreateMaybeMessage< ::perception::config::Radar >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Radar >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::Camera*
Arena::CreateMaybeMessage< ::perception::config::Camera >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::Camera >(arena);
}
template<> PROTOBUF_NOINLINE ::perception::config::ConfigGroup*
Arena::CreateMaybeMessage< ::perception::config::ConfigGroup >(Arena* arena) {
  return Arena::CreateMessageInternal< ::perception::config::ConfigGroup >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>
