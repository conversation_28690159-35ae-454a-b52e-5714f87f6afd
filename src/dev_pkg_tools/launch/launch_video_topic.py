import os
# from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='dev_pkg_tools',
            executable='video_publisher',
            name='video_topic_node',
            respawn=True
            # output='screen',
            # parameters=[os.path.join(
            #     get_package_share_directory('dev_pkg_det2d'),
            #     'config',
            #     'params.yaml'
            # )]
        ),
        # Node(
        #     package='dev_pkg_tools',
        #     executable='video_publisher_2',
        #     name='video_topic_node_2',
        #     respawn=True
        #     # output='screen',
        #     # parameters=[os.path.join(
        #     #     get_package_share_directory('dev_pkg_det2d'),
        #     #     'config',
        #     #     'params.yaml'
        #     # )]
        # )
    ])
