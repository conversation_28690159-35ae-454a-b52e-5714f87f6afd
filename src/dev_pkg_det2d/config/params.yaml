det2d_node:
  ros__parameters:
    # Log class
    log_level: 4            # 0: FATAL, 1: ERROR, 2: WARN, 3: INFO, 4: VERB, 5: DEBUG

    # Input topic params
    is_use_drv_cfg: false
    ## Debug
    drv_cfg_file: src/dev_pkg_det2d/config/config_drv.yaml
    ## Release
    # drv_cfg_file: /app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/config/config_drv.yaml

    # Topic channels
    input_topic_names:
      - video_frames
    output_obj_topic_name: obj_detections
    output_img_topic_name: img_detections

    # Callback params
    rate_infer_all_camera: 10.0  # Hz

    # Model params
    ## Debug
    model_file: src/dev_pkg_det2d/models/onnx/yolov8n.onnx
    ## Release
    # model_file: /app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/models/onnx/road-crack.onnx
    model_type: 3           # 0: NULLDET, 1: YOLOV5, 2: YOLOV7, 3: YOLOV8
    model_precision: 1      # 0: FP32, 1: FP16, 2: INT8
    ## Debug INT8
    # calib_data_file: src/dev_pkg_det2d/calibration/calibration_list_coco.txt
    # calib_table_file: src/dev_pkg_det2d/calibration/calibration_table_yolov8.txt
    ## Release INT8
    # calib_data_file: /app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/calibration/calibration_list_coco.txt
    # calib_table_file: /app/dev_ws_det2d/install/dev_pkg_det2d/share/dev_pkg_det2d/calibration/calibration_table_yolov8.txt
    ## FP32, FP16
    calib_data_file: ''
    calib_table_file: ''

    # Det. params
    img_height: 640
    img_width: 640
    img_channel: 3
    pro_device: 0           # 0: CPU, 1: GPU
    conf_thresh: 0.25
    nms_thresh: 0.45
    