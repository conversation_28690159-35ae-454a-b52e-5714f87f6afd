// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: esurfing/math/geometry.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_esurfing_2fmath_2fgeometry_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_esurfing_2fmath_2fgeometry_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3021000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3021010 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_esurfing_2fmath_2fgeometry_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_esurfing_2fmath_2fgeometry_2eproto {
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_esurfing_2fmath_2fgeometry_2eproto;
namespace perception {
namespace config {
class Quaterniond;
struct QuaterniondDefaultTypeInternal;
extern QuaterniondDefaultTypeInternal _Quaterniond_default_instance_;
class Transformation3d;
struct Transformation3dDefaultTypeInternal;
extern Transformation3dDefaultTypeInternal _Transformation3d_default_instance_;
class Vector3d;
struct Vector3dDefaultTypeInternal;
extern Vector3dDefaultTypeInternal _Vector3d_default_instance_;
}  // namespace config
}  // namespace perception
PROTOBUF_NAMESPACE_OPEN
template<> ::perception::config::Quaterniond* Arena::CreateMaybeMessage<::perception::config::Quaterniond>(Arena*);
template<> ::perception::config::Transformation3d* Arena::CreateMaybeMessage<::perception::config::Transformation3d>(Arena*);
template<> ::perception::config::Vector3d* Arena::CreateMaybeMessage<::perception::config::Vector3d>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace perception {
namespace config {

// ===================================================================

class Transformation3d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Transformation3d) */ {
 public:
  inline Transformation3d() : Transformation3d(nullptr) {}
  ~Transformation3d() override;
  explicit PROTOBUF_CONSTEXPR Transformation3d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Transformation3d(const Transformation3d& from);
  Transformation3d(Transformation3d&& from) noexcept
    : Transformation3d() {
    *this = ::std::move(from);
  }

  inline Transformation3d& operator=(const Transformation3d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Transformation3d& operator=(Transformation3d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Transformation3d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Transformation3d* internal_default_instance() {
    return reinterpret_cast<const Transformation3d*>(
               &_Transformation3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Transformation3d& a, Transformation3d& b) {
    a.Swap(&b);
  }
  inline void Swap(Transformation3d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Transformation3d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Transformation3d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Transformation3d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Transformation3d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Transformation3d& from) {
    Transformation3d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Transformation3d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Transformation3d";
  }
  protected:
  explicit Transformation3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTranslationFieldNumber = 1,
    kRotationFieldNumber = 2,
  };
  // .perception.config.Vector3d translation = 1;
  bool has_translation() const;
  private:
  bool _internal_has_translation() const;
  public:
  void clear_translation();
  const ::perception::config::Vector3d& translation() const;
  PROTOBUF_NODISCARD ::perception::config::Vector3d* release_translation();
  ::perception::config::Vector3d* mutable_translation();
  void set_allocated_translation(::perception::config::Vector3d* translation);
  private:
  const ::perception::config::Vector3d& _internal_translation() const;
  ::perception::config::Vector3d* _internal_mutable_translation();
  public:
  void unsafe_arena_set_allocated_translation(
      ::perception::config::Vector3d* translation);
  ::perception::config::Vector3d* unsafe_arena_release_translation();

  // .perception.config.Quaterniond rotation = 2;
  bool has_rotation() const;
  private:
  bool _internal_has_rotation() const;
  public:
  void clear_rotation();
  const ::perception::config::Quaterniond& rotation() const;
  PROTOBUF_NODISCARD ::perception::config::Quaterniond* release_rotation();
  ::perception::config::Quaterniond* mutable_rotation();
  void set_allocated_rotation(::perception::config::Quaterniond* rotation);
  private:
  const ::perception::config::Quaterniond& _internal_rotation() const;
  ::perception::config::Quaterniond* _internal_mutable_rotation();
  public:
  void unsafe_arena_set_allocated_rotation(
      ::perception::config::Quaterniond* rotation);
  ::perception::config::Quaterniond* unsafe_arena_release_rotation();

  // @@protoc_insertion_point(class_scope:perception.config.Transformation3d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    ::perception::config::Vector3d* translation_;
    ::perception::config::Quaterniond* rotation_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fmath_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Vector3d final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Vector3d) */ {
 public:
  inline Vector3d() : Vector3d(nullptr) {}
  ~Vector3d() override;
  explicit PROTOBUF_CONSTEXPR Vector3d(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Vector3d(const Vector3d& from);
  Vector3d(Vector3d&& from) noexcept
    : Vector3d() {
    *this = ::std::move(from);
  }

  inline Vector3d& operator=(const Vector3d& from) {
    CopyFrom(from);
    return *this;
  }
  inline Vector3d& operator=(Vector3d&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Vector3d& default_instance() {
    return *internal_default_instance();
  }
  static inline const Vector3d* internal_default_instance() {
    return reinterpret_cast<const Vector3d*>(
               &_Vector3d_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Vector3d& a, Vector3d& b) {
    a.Swap(&b);
  }
  inline void Swap(Vector3d* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Vector3d* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Vector3d* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Vector3d>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Vector3d& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Vector3d& from) {
    Vector3d::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Vector3d* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Vector3d";
  }
  protected:
  explicit Vector3d(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.Vector3d)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double x_;
    double y_;
    double z_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fmath_2fgeometry_2eproto;
};
// -------------------------------------------------------------------

class Quaterniond final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:perception.config.Quaterniond) */ {
 public:
  inline Quaterniond() : Quaterniond(nullptr) {}
  ~Quaterniond() override;
  explicit PROTOBUF_CONSTEXPR Quaterniond(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Quaterniond(const Quaterniond& from);
  Quaterniond(Quaterniond&& from) noexcept
    : Quaterniond() {
    *this = ::std::move(from);
  }

  inline Quaterniond& operator=(const Quaterniond& from) {
    CopyFrom(from);
    return *this;
  }
  inline Quaterniond& operator=(Quaterniond&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Quaterniond& default_instance() {
    return *internal_default_instance();
  }
  static inline const Quaterniond* internal_default_instance() {
    return reinterpret_cast<const Quaterniond*>(
               &_Quaterniond_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(Quaterniond& a, Quaterniond& b) {
    a.Swap(&b);
  }
  inline void Swap(Quaterniond* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Quaterniond* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Quaterniond* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Quaterniond>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Quaterniond& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom( const Quaterniond& from) {
    Quaterniond::MergeImpl(*this, from);
  }
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message& to_msg, const ::PROTOBUF_NAMESPACE_ID::Message& from_msg);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _impl_._cached_size_.Get(); }

  private:
  void SharedCtor(::PROTOBUF_NAMESPACE_ID::Arena* arena, bool is_message_owned);
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Quaterniond* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "perception.config.Quaterniond";
  }
  protected:
  explicit Quaterniond(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
    kZFieldNumber = 3,
    kWFieldNumber = 4,
  };
  // double x = 1;
  void clear_x();
  double x() const;
  void set_x(double value);
  private:
  double _internal_x() const;
  void _internal_set_x(double value);
  public:

  // double y = 2;
  void clear_y();
  double y() const;
  void set_y(double value);
  private:
  double _internal_y() const;
  void _internal_set_y(double value);
  public:

  // double z = 3;
  void clear_z();
  double z() const;
  void set_z(double value);
  private:
  double _internal_z() const;
  void _internal_set_z(double value);
  public:

  // double w = 4;
  void clear_w();
  double w() const;
  void set_w(double value);
  private:
  double _internal_w() const;
  void _internal_set_w(double value);
  public:

  // @@protoc_insertion_point(class_scope:perception.config.Quaterniond)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  struct Impl_ {
    double x_;
    double y_;
    double z_;
    double w_;
    mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  };
  union { Impl_ _impl_; };
  friend struct ::TableStruct_esurfing_2fmath_2fgeometry_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Transformation3d

// .perception.config.Vector3d translation = 1;
inline bool Transformation3d::_internal_has_translation() const {
  return this != internal_default_instance() && _impl_.translation_ != nullptr;
}
inline bool Transformation3d::has_translation() const {
  return _internal_has_translation();
}
inline void Transformation3d::clear_translation() {
  if (GetArenaForAllocation() == nullptr && _impl_.translation_ != nullptr) {
    delete _impl_.translation_;
  }
  _impl_.translation_ = nullptr;
}
inline const ::perception::config::Vector3d& Transformation3d::_internal_translation() const {
  const ::perception::config::Vector3d* p = _impl_.translation_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Vector3d&>(
      ::perception::config::_Vector3d_default_instance_);
}
inline const ::perception::config::Vector3d& Transformation3d::translation() const {
  // @@protoc_insertion_point(field_get:perception.config.Transformation3d.translation)
  return _internal_translation();
}
inline void Transformation3d::unsafe_arena_set_allocated_translation(
    ::perception::config::Vector3d* translation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.translation_);
  }
  _impl_.translation_ = translation;
  if (translation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.Transformation3d.translation)
}
inline ::perception::config::Vector3d* Transformation3d::release_translation() {
  
  ::perception::config::Vector3d* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Vector3d* Transformation3d::unsafe_arena_release_translation() {
  // @@protoc_insertion_point(field_release:perception.config.Transformation3d.translation)
  
  ::perception::config::Vector3d* temp = _impl_.translation_;
  _impl_.translation_ = nullptr;
  return temp;
}
inline ::perception::config::Vector3d* Transformation3d::_internal_mutable_translation() {
  
  if (_impl_.translation_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Vector3d>(GetArenaForAllocation());
    _impl_.translation_ = p;
  }
  return _impl_.translation_;
}
inline ::perception::config::Vector3d* Transformation3d::mutable_translation() {
  ::perception::config::Vector3d* _msg = _internal_mutable_translation();
  // @@protoc_insertion_point(field_mutable:perception.config.Transformation3d.translation)
  return _msg;
}
inline void Transformation3d::set_allocated_translation(::perception::config::Vector3d* translation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.translation_;
  }
  if (translation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(translation);
    if (message_arena != submessage_arena) {
      translation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, translation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.translation_ = translation;
  // @@protoc_insertion_point(field_set_allocated:perception.config.Transformation3d.translation)
}

// .perception.config.Quaterniond rotation = 2;
inline bool Transformation3d::_internal_has_rotation() const {
  return this != internal_default_instance() && _impl_.rotation_ != nullptr;
}
inline bool Transformation3d::has_rotation() const {
  return _internal_has_rotation();
}
inline void Transformation3d::clear_rotation() {
  if (GetArenaForAllocation() == nullptr && _impl_.rotation_ != nullptr) {
    delete _impl_.rotation_;
  }
  _impl_.rotation_ = nullptr;
}
inline const ::perception::config::Quaterniond& Transformation3d::_internal_rotation() const {
  const ::perception::config::Quaterniond* p = _impl_.rotation_;
  return p != nullptr ? *p : reinterpret_cast<const ::perception::config::Quaterniond&>(
      ::perception::config::_Quaterniond_default_instance_);
}
inline const ::perception::config::Quaterniond& Transformation3d::rotation() const {
  // @@protoc_insertion_point(field_get:perception.config.Transformation3d.rotation)
  return _internal_rotation();
}
inline void Transformation3d::unsafe_arena_set_allocated_rotation(
    ::perception::config::Quaterniond* rotation) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(_impl_.rotation_);
  }
  _impl_.rotation_ = rotation;
  if (rotation) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:perception.config.Transformation3d.rotation)
}
inline ::perception::config::Quaterniond* Transformation3d::release_rotation() {
  
  ::perception::config::Quaterniond* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::perception::config::Quaterniond* Transformation3d::unsafe_arena_release_rotation() {
  // @@protoc_insertion_point(field_release:perception.config.Transformation3d.rotation)
  
  ::perception::config::Quaterniond* temp = _impl_.rotation_;
  _impl_.rotation_ = nullptr;
  return temp;
}
inline ::perception::config::Quaterniond* Transformation3d::_internal_mutable_rotation() {
  
  if (_impl_.rotation_ == nullptr) {
    auto* p = CreateMaybeMessage<::perception::config::Quaterniond>(GetArenaForAllocation());
    _impl_.rotation_ = p;
  }
  return _impl_.rotation_;
}
inline ::perception::config::Quaterniond* Transformation3d::mutable_rotation() {
  ::perception::config::Quaterniond* _msg = _internal_mutable_rotation();
  // @@protoc_insertion_point(field_mutable:perception.config.Transformation3d.rotation)
  return _msg;
}
inline void Transformation3d::set_allocated_rotation(::perception::config::Quaterniond* rotation) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete _impl_.rotation_;
  }
  if (rotation) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalGetOwningArena(rotation);
    if (message_arena != submessage_arena) {
      rotation = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rotation, submessage_arena);
    }
    
  } else {
    
  }
  _impl_.rotation_ = rotation;
  // @@protoc_insertion_point(field_set_allocated:perception.config.Transformation3d.rotation)
}

// -------------------------------------------------------------------

// Vector3d

// double x = 1;
inline void Vector3d::clear_x() {
  _impl_.x_ = 0;
}
inline double Vector3d::_internal_x() const {
  return _impl_.x_;
}
inline double Vector3d::x() const {
  // @@protoc_insertion_point(field_get:perception.config.Vector3d.x)
  return _internal_x();
}
inline void Vector3d::_internal_set_x(double value) {
  
  _impl_.x_ = value;
}
inline void Vector3d::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:perception.config.Vector3d.x)
}

// double y = 2;
inline void Vector3d::clear_y() {
  _impl_.y_ = 0;
}
inline double Vector3d::_internal_y() const {
  return _impl_.y_;
}
inline double Vector3d::y() const {
  // @@protoc_insertion_point(field_get:perception.config.Vector3d.y)
  return _internal_y();
}
inline void Vector3d::_internal_set_y(double value) {
  
  _impl_.y_ = value;
}
inline void Vector3d::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:perception.config.Vector3d.y)
}

// double z = 3;
inline void Vector3d::clear_z() {
  _impl_.z_ = 0;
}
inline double Vector3d::_internal_z() const {
  return _impl_.z_;
}
inline double Vector3d::z() const {
  // @@protoc_insertion_point(field_get:perception.config.Vector3d.z)
  return _internal_z();
}
inline void Vector3d::_internal_set_z(double value) {
  
  _impl_.z_ = value;
}
inline void Vector3d::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:perception.config.Vector3d.z)
}

// -------------------------------------------------------------------

// Quaterniond

// double x = 1;
inline void Quaterniond::clear_x() {
  _impl_.x_ = 0;
}
inline double Quaterniond::_internal_x() const {
  return _impl_.x_;
}
inline double Quaterniond::x() const {
  // @@protoc_insertion_point(field_get:perception.config.Quaterniond.x)
  return _internal_x();
}
inline void Quaterniond::_internal_set_x(double value) {
  
  _impl_.x_ = value;
}
inline void Quaterniond::set_x(double value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:perception.config.Quaterniond.x)
}

// double y = 2;
inline void Quaterniond::clear_y() {
  _impl_.y_ = 0;
}
inline double Quaterniond::_internal_y() const {
  return _impl_.y_;
}
inline double Quaterniond::y() const {
  // @@protoc_insertion_point(field_get:perception.config.Quaterniond.y)
  return _internal_y();
}
inline void Quaterniond::_internal_set_y(double value) {
  
  _impl_.y_ = value;
}
inline void Quaterniond::set_y(double value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:perception.config.Quaterniond.y)
}

// double z = 3;
inline void Quaterniond::clear_z() {
  _impl_.z_ = 0;
}
inline double Quaterniond::_internal_z() const {
  return _impl_.z_;
}
inline double Quaterniond::z() const {
  // @@protoc_insertion_point(field_get:perception.config.Quaterniond.z)
  return _internal_z();
}
inline void Quaterniond::_internal_set_z(double value) {
  
  _impl_.z_ = value;
}
inline void Quaterniond::set_z(double value) {
  _internal_set_z(value);
  // @@protoc_insertion_point(field_set:perception.config.Quaterniond.z)
}

// double w = 4;
inline void Quaterniond::clear_w() {
  _impl_.w_ = 0;
}
inline double Quaterniond::_internal_w() const {
  return _impl_.w_;
}
inline double Quaterniond::w() const {
  // @@protoc_insertion_point(field_get:perception.config.Quaterniond.w)
  return _internal_w();
}
inline void Quaterniond::_internal_set_w(double value) {
  
  _impl_.w_ = value;
}
inline void Quaterniond::set_w(double value) {
  _internal_set_w(value);
  // @@protoc_insertion_point(field_set:perception.config.Quaterniond.w)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace config
}  // namespace perception

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_esurfing_2fmath_2fgeometry_2eproto
