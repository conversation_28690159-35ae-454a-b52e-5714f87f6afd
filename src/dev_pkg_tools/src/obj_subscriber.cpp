#include <memory>
#include <string>
#include <vector>
#include <rclcpp/rclcpp.hpp>
#include <std_msgs/msg/u_int8_multi_array.hpp>
#include "esurfing/perc/group_detected.pb.h" // 包含 Protobuf 定义

// Protobuf 命名空间别名
using perception::detect::DetectedObjects;
using perception::detect::DetectedObject;

class ObjectSubscriber : public rclcpp::Node
{
public:
    ObjectSubscriber()
    : Node("object_subscriber_node")
    {
        // 声明参数
        this->declare_parameter<std::string>("input_topic", "/objects_topic"); // 订阅的 topic

        // 获取参数
        std::string input_topic = this->get_parameter("input_topic").as_string();

        // 创建订阅者
        subscription_ = this->create_subscription<std_msgs::msg::UInt8MultiArray>(
            input_topic,
            10, // QoS 历史深度
            std::bind(&ObjectSubscriber::topic_callback, this, std::placeholders::_1));

        RCLCPP_INFO(this->get_logger(), "Object subscriber node initialized, listening on '%s'", input_topic.c_str());
    }

private:
    void topic_callback(const std_msgs::msg::UInt8MultiArray::SharedPtr msg) const
    {
        // 1. Create empty Protobuf message object
        DetectedObjects detected_objects_msg;

        // 2. Deserialize
        if (detected_objects_msg.ParseFromArray(msg->data.data(), msg->data.size())) {
            RCLCPP_INFO(this->get_logger(), "Successfully parsed DetectedObjects message:");
            RCLCPP_INFO(this->get_logger(), "  time_meas (ms): %ld", detected_objects_msg.time_meas());
            RCLCPP_INFO(this->get_logger(), "  time_pub (ms): %ld", detected_objects_msg.time_pub());
            RCLCPP_INFO(this->get_logger(), "  group_name: %s", detected_objects_msg.group_name().c_str());
            // group_info is bytes, might need special handling
            // RCLCPP_INFO(this->get_logger(), "  group_info: %s", detected_objects_msg.group_info().c_str()); // Note: bytes might not be valid UTF-8
            RCLCPP_INFO(this->get_logger(), "  objects_size: %d", detected_objects_msg.objects_size());

            // 3. Iterate and print information for each object
            for (int i = 0; i < detected_objects_msg.objects_size(); ++i) {
                const DetectedObject& obj = detected_objects_msg.objects(i);
                RCLCPP_INFO(this->get_logger(), "--- Object %d ---", i + 1);

                // overlap_name (bytes)
                // RCLCPP_INFO(this->get_logger(), "    overlap_name: %s", obj.overlap_name().c_str()); // Note: bytes might not be valid UTF-8

                RCLCPP_INFO(this->get_logger(), "    uuid: %lu", obj.uuid());
                RCLCPP_INFO(this->get_logger(), "    type: %d", obj.type());
                RCLCPP_INFO(this->get_logger(), "    confidence: %.3f", obj.confidence());

                if (obj.has_position()) {
                    RCLCPP_INFO(this->get_logger(), "    position (x,y,z): (%.3f, %.3f, %.3f)",
                                obj.position().x(), obj.position().y(), obj.position().z());
                }
                if (obj.has_shape()) {
                    RCLCPP_INFO(this->get_logger(), "    shape (l,w,h): (%.3f, %.3f, %.3f)",
                                obj.shape().x(), obj.shape().y(), obj.shape().z());
                }

                if (obj.has_hull() && obj.hull().points_size() > 0) {
                    std::string hull_str = "    hull points: ";
                    for(const auto& pt : obj.hull().points()) {
                        hull_str += "(" + std::to_string(pt.x()) + "," + std::to_string(pt.y()) + ") ";
                    }
                     RCLCPP_INFO(this->get_logger(), "%s", hull_str.c_str());
                }

                RCLCPP_INFO(this->get_logger(), "    orientation (rad): %.3f", obj.orientation());

                if (obj.has_velocity()) {
                      RCLCPP_INFO(this->get_logger(), "    velocity (h,s,a): (%.3f, %.3f, %.3f)",
                                obj.velocity().heading(), obj.velocity().speed(), obj.velocity().acceleration());
                 }

                RCLCPP_INFO(this->get_logger(), "    is_static: %s", obj.is_static() ? "true" : "false");

                if (obj.has_color()) {
                     RCLCPP_INFO(this->get_logger(), "    color (r,g,b,a): (%u, %u, %u, %.3f)",
                                obj.color().r(), obj.color().g(), obj.color().b(), obj.color().a());
                 }

                if (obj.feature_size() > 0) {
                    std::string feature_str = "    feature: [";
                    for(const auto& f : obj.feature()) {
                        feature_str += std::to_string(f) + ", ";
                    }
                    if (feature_str.length() > 15) feature_str.replace(feature_str.end()-2, feature_str.end(), "]");
                    else feature_str += "]";
                    RCLCPP_INFO(this->get_logger(), "%s", feature_str.c_str());
                }

                if (obj.trajectories_size() > 0) {
                    RCLCPP_INFO(this->get_logger(), "    trajectories:");
                    for (int traj_idx = 0; traj_idx < obj.trajectories_size(); ++traj_idx) {
                        const auto& traj = obj.trajectories(traj_idx);
                        RCLCPP_INFO(this->get_logger(), "      trajectory %d (probability: %.3f):", traj_idx + 1, traj.probability());
                        for (int wp_idx = 0; wp_idx < traj.waypoints_size(); ++wp_idx) {
                            const auto& wp = traj.waypoints(wp_idx);
                             RCLCPP_INFO(this->get_logger(), "        waypoint %d: time=%ld, pos=(%.3f,%.3f,%.3f), vel=(h=%.3f,s=%.3f,a=%.3f)",
                                         wp_idx + 1, wp.time_meas(),
                                         wp.position().x(), wp.position().y(), wp.position().z(),
                                         wp.velocity().heading(), wp.velocity().speed(), wp.velocity().acceleration());
                        }
                    }
                }

                if (obj.str_array_size() > 0) {
                    std::string str_arr_str = "    str_array: [";
                     for(const auto& s_bytes : obj.str_array()) {
                        // Assuming bytes are UTF-8 strings
                        str_arr_str += "\"" + s_bytes + "\", ";
                    }
                    if (str_arr_str.length() > 18) str_arr_str.replace(str_arr_str.end()-2, str_arr_str.end(), "]");
                    else str_arr_str += "]";
                    RCLCPP_INFO(this->get_logger(), "%s", str_arr_str.c_str());
                }

                if (obj.int_array_size() > 0) {
                     std::string int_arr_str = "    int_array: [";
                     for(const auto& i_val : obj.int_array()) {
                        int_arr_str += std::to_string(i_val) + ", ";
                    }
                    if (int_arr_str.length() > 18) int_arr_str.replace(int_arr_str.end()-2, int_arr_str.end(), "]");
                    else int_arr_str += "]";
                    RCLCPP_INFO(this->get_logger(), "%s", int_arr_str.c_str());
                }

                RCLCPP_INFO(this->get_logger(), "    parking_time: %ld", obj.parking_time());

                if (obj.has_plate()) {
                    RCLCPP_INFO(this->get_logger(), "    plate:");
                    RCLCPP_INFO(this->get_logger(), "      color_code: %d (confidence: %.3f)", obj.plate().color(), obj.plate().color_confidence());
                    RCLCPP_INFO(this->get_logger(), "      number: %s (confidence: %.3f)", obj.plate().number().c_str(), obj.plate().number_confidence());
                }

                RCLCPP_INFO(this->get_logger(), "    obj_color_code: %d", obj.obj_color());
                RCLCPP_INFO(this->get_logger(), "    group_id: %s", obj.group_id().c_str());
                RCLCPP_INFO(this->get_logger(), "    obj_color_conf: %.3f", obj.obj_color_conf());

                if (obj.has_track_conf()) {
                    RCLCPP_INFO(this->get_logger(), "    track_conf:");
                    RCLCPP_INFO(this->get_logger(), "      color_count: %u", obj.track_conf().color());
                    RCLCPP_INFO(this->get_logger(), "      type_count: %u", obj.track_conf().type());
                    RCLCPP_INFO(this->get_logger(), "      plate_number_count: %u", obj.track_conf().plate_number());
                    RCLCPP_INFO(this->get_logger(), "      plate_color_count: %u", obj.track_conf().plate_color());
                }

                RCLCPP_INFO(this->get_logger(), "    camera_name: %s", obj.camera_name().c_str());

                if (obj.has_position_image()) {
                    RCLCPP_INFO(this->get_logger(), "    position_image (x,y,z): (%.1f, %.1f, %.1f)",
                                obj.position_image().x(), obj.position_image().y(), obj.position_image().z());
                }
                 if (obj.has_shape_image()) {
                    RCLCPP_INFO(this->get_logger(), "    shape_image (w,h): (%.1f, %.1f)",
                                obj.shape_image().x(), obj.shape_image().y());
                }

            }
        } else {
            RCLCPP_WARN(this->get_logger(), "Failed to deserialize incoming byte data");
        }
    }

    rclcpp::Subscription<std_msgs::msg::UInt8MultiArray>::SharedPtr subscription_;
};

int main(int argc, char * argv[])
{
    // GOOGLE_PROTOBUF_VERIFY_VERSION; // Optional

    rclcpp::init(argc, argv);
    auto node = std::make_shared<ObjectSubscriber>();
    if (rclcpp::ok()) {
        rclcpp::spin(node);
    }
    rclcpp::shutdown();

    // google::protobuf::ShutdownProtobufLibrary(); // Optional
    return 0;
} 