import os
# from ament_index_python.packages import get_package_share_directory
from launch import LaunchDescription
from launch_ros.actions import Node

def generate_launch_description():
    return LaunchDescription([
        Node(
            package='dev_pkg_det2d',
            executable='yolo_ros2_interface_test',
            name='det2d_node',
            respawn=True
            # output='screen',
            # parameters=[os.path.join(
            #     get_package_share_directory('dev_pkg_det2d'),
            #     'config',
            #     'params.yaml'
            # )]
        )
    ])
