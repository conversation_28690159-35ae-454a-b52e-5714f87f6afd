# For more detail, check NGC
FROM nvcr.io/nvidia/tensorrt:23.10-py3  

ENV TZ=Asia/Shanghai
# ARG user=trt-starter

# Set timezone in case of interation during installation
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# # install packages for opencv
# RUN apt-get update
# RUN apt install -y --no-install-recommends \
#   build-essential cmake git pkg-config libgtk-3-dev \
#   libavcodec-dev libavformat-dev libswscale-dev libv4l-dev libxvidcore-dev libx264-dev \
#   libjpeg-dev libpng-dev libtiff-dev gfortran openexr libatlas-base-dev \
#   python3-dev python3-numpy libtbb2 libtbb-dev libdc1394-22-dev \
#   build-essential cmake git pkg-config libgtk-3-dev libavcodec-dev \
#   libavformat-dev libswscale-dev libv4l-dev libxvidcore-dev libx264-dev \
#   libjpeg-dev libpng-dev libtiff-dev gfortran openexr libatlas-base-dev python3-dev \
#   python3-numpy libtbb2 libtbb-dev libdc1394-22-dev 
# RUN apt-get -y clean && rm -rf /var/lib/apt/lists/*

# install packages for opencv 
# The libdc1394-22-dev package was removed in Debian and Ubuntu after 21.10
RUN apt-get update
RUN apt install -y --no-install-recommends \
  build-essential cmake git pkg-config libgtk-3-dev \
  libavcodec-dev libavformat-dev libswscale-dev libv4l-dev libxvidcore-dev libx264-dev \
  libjpeg-dev libpng-dev libtiff-dev gfortran openexr libatlas-base-dev \
  python3-dev python3-numpy libtbb2 libtbb-dev \
  build-essential cmake git pkg-config libgtk-3-dev libavcodec-dev \
  libavformat-dev libswscale-dev libv4l-dev libxvidcore-dev libx264-dev \
  libjpeg-dev libpng-dev libtiff-dev gfortran openexr libatlas-base-dev python3-dev \
  python3-numpy libtbb2 libtbb-dev
RUN apt-get -y clean && rm -rf /var/lib/apt/lists/*

#Build opencv4.2.0
RUN mkdir /root/opencv_build
WORKDIR /root/opencv_build/
RUN git clone https://github.com/opencv/opencv.git && git clone https://github.com/opencv/opencv_contrib.git && \
  cd /root/opencv_build/opencv && git checkout 4.2.0 && \
  cd /root/opencv_build/opencv_contrib && git checkout 4.2.0 && \
  cd /root/opencv_build/opencv && mkdir build

WORKDIR /root/opencv_build/opencv/build
RUN cmake -D OPENCV_GENERATE_PKGCONFIG=ON \
  -D OPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules \
  -D WITH_GSTREAMER=ON \
  -D WITH_LIBV4L=ON \
  -D BUILD_opencv_python2=ON \
  -D BUILD_opencv_python3=ON \
  -D BUILD_TESTS=OFF \
  -D BUILD_PERF_TESTS=OFF \
  -D BUILD_EXAMPLES=OFF \
  -D CMAKE_BUILD_TYPE=RELEASE \
  -D PYTHON3_PACKAGES_PATH=/usr/lib/python3/dist-packages \
  -D CMAKE_INSTALL_PREFIX=/usr/local ..
RUN make -j16 && make install
WORKDIR /root
RUN rm -rf opencv_build


# install packages that are used for trt inferences
RUN apt-get update && apt install -y --no-install-recommends \
  sudo libgflags-dev libboost-dev libxml2-dev \
  libyaml-cpp-dev sqlite3 libsqlite3-dev libboost-all-dev \
  fish lsb-release peco feh fim openssh-server tmux curl
RUN apt-get -y clean && rm -rf /var/lib/apt/lists/*

# install netron to show model structure
# RUN pip install netron

# set up fish shell and exa
RUN curl -Lo exa.zip "https://github.com/ogham/exa/releases/latest/download/exa-linux-x86_64-v0.10.1.zip" && \
  sudo unzip -q exa.zip bin/exa -d /usr/local && rm exa.zip
  
# install others
RUN sudo apt install -y nano
RUN sudo curl -sSL https://raw.githubusercontent.com/ros/rosdistro/master/ros.key -o /usr/share/keyrings/ros-archive-keyring.gpg
RUN sudo echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/ros-archive-keyring.gpg] http://packages.ros.org/ros2/ubuntu $(source /etc/os-release && echo $UBUNTU_CODENAME) main" | sudo tee /etc/apt/sources.list.d/ros2.list > /dev/null
RUN sudo apt update && sudo apt install -y ros-humble-desktop
RUN source /opt/ros/humble/setup.bash && echo " source /opt/ros/humble/setup.bash" >> ~/.bashrc
RUN sudo apt install -y python3-colcon-common-extensions
RUN sudo apt install -y ros-humble-backward-ros
RUN sudo pip3 install rosdepc && sudo rosdepc init && rosdepc update
RUN sudo apt-get install -y nlohmann-json3-dev
RUN sudo apt install -y gdb


# # set userinfo
# RUN useradd -rm -c ${user} -u 1000 -d /home/<USER>/bin/bash -G sudo ${user}
# SHELL ["/bin/bash", "-o", "pipefail", "-c"]
# RUN echo "$user   ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

RUN apt-get update

# # copy dotfiles
# COPY my_dot_files/tmux/* /home/<USER>/
# COPY my_dot_files/fish/ /home/<USER>/.config/fish

# # set working directory of trt webinar
# RUN mkdir -p /home/<USER>/workspace/
# RUN chown -R ${user}:users /home/<USER>

# ssh setting
RUN systemctl enable ssh

# # set default working directory and user
# WORKDIR /home/<USER>
# USER ${user}


