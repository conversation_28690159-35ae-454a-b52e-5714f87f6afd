#include <iostream>

#include <string>
#include <vector>
#include "assert.h"
#include <time.h>
#include "opencv2/core/core.hpp"
#include "opencv2/core/types.hpp"
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"

using namespace std;

class VehicleLabelsDetr {
public:
    VehicleLabelsDetr() {
        // Initialize with 14 labels
        for(int i = 0; i < 14; i++) {
            string x = "NA";
            mLabels.push_back(x);
        }

        // Class labels (0-13)
        mLabels[0] = "car";
        mLabels[1] = "truck";
        mLabels[2] = "heavy_truck";
        mLabels[3] = "van";
        mLabels[4] = "bus";
        mLabels[5] = "bicycle";
        mLabels[6] = "cyclist";
        mLabels[7] = "tricycle";
        mLabels[8] = "trolley";
        mLabels[9] = "pedestrain";
        mLabels[10] = "cone";
        mLabels[11] = "animal";
        mLabels[12] = "other";
        mLabels[13] = "plate";
    }

    string coco_get_label(int i) {
        assert(i >= 0 && i < 14);
        return mLabels[i];
    }

    cv::Scalar coco_get_color(int i) {
        float r;
        srand(i);
        r = (float)rand() / RAND_MAX;
        int red    = int(r * 255);

        srand(i + 1);
        r = (float)rand() / RAND_MAX;
        int green    = int(r * 255);

        srand(i + 2);
        r = (float)rand() / RAND_MAX;
        int blue    = int(r * 255);

        return cv::Scalar(blue, green, red);
    }

    cv::Scalar get_inverse_color(cv::Scalar color) {
        int blue = 255 - color[0];
        int green = 255 - color[1];
        int red = 255 - color[2];
        return cv::Scalar(blue, green, red);
    }


private:
  vector<string> mLabels;

};
