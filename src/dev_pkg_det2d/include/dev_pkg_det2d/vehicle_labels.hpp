#include <iostream>

#include <string>
#include <vector>
#include "assert.h"
#include <time.h>
#include "opencv2/core/core.hpp"
#include "opencv2/core/types.hpp"
#include "opencv2/imgproc/imgproc.hpp"
#include "opencv2/highgui/highgui.hpp"

using namespace std;

class VehicleLabels {
public:
    VehicleLabels() {
        // Initialize with 29 labels (13 cls + 4 orien + 12 color)
        for(int i = 0; i < 29; i++) {
            string x = "NA";
            mLabels.push_back(x);
        }

        // Class labels (0-12)
        mLabels[0] = "car";
        mLabels[1] = "truck";
        mLabels[2] = "heavy_truck";
        mLabels[3] = "van";
        mLabels[4] = "bus";
        mLabels[5] = "bicycle";
        mLabels[6] = "cyclist";
        mLabels[7] = "tricycle";
        mLabels[8] = "trolley";
        mLabels[9] = "pedestrain";
        mLabels[10] = "cone";
        mLabels[11] = "animal";
        mLabels[12] = "other";

        // Orientation labels (13-16)
        mLabels[13] = "south";
        mLabels[14] = "north";
        mLabels[15] = "west";
        mLabels[16] = "east";

        // Color labels (17-28)
        mLabels[17] = "black";
        mLabels[18] = "white";
        mLabels[19] = "gray";
        mLabels[20] = "red";
        mLabels[21] = "yellow";
        mLabels[22] = "green";
        mLabels[23] = "blue";
        mLabels[24] = "purple";
        mLabels[25] = "brown";
        mLabels[26] = "orange";
        mLabels[27] = "pink";
        mLabels[28] = "other";
    }

    string coco_get_label(int i) {
        assert(i >= 0 && i < 29);
        return mLabels[i];
    }

    cv::Scalar coco_get_color(int i) {
        float r;
        srand(i);
        r = (float)rand() / RAND_MAX;
        int red    = int(r * 255);

        srand(i + 1);
        r = (float)rand() / RAND_MAX;
        int green    = int(r * 255);

        srand(i + 2);
        r = (float)rand() / RAND_MAX;
        int blue    = int(r * 255);

        return cv::Scalar(blue, green, red);
    }

    cv::Scalar get_inverse_color(cv::Scalar color) {
        int blue = 255 - color[0];
        int green = 255 - color[1];
        int red = 255 - color[2];
        return cv::Scalar(blue, green, red);
    }


private:
  vector<string> mLabels;

};
