#include "opencv2/core/types.hpp"
#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp" 
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <algorithm>
#include <string>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc//imgproc.hpp"
#include "opencv2/opencv.hpp"
#include "trt_plate_detector.hpp"
#include "trt_preprocess.hpp"
#include "coco_labels.hpp"
#include "image_cropping.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace detector {

float PlateDetector::iou_calc(bbox bbox1, bbox bbox2){
    auto inter_x0 = std::max(bbox1.x0, bbox2.x0);
    auto inter_y0 = std::max(bbox1.y0, bbox2.y0);
    auto inter_x1 = std::min(bbox1.x1, bbox2.x1);
    auto inter_y1 = std::min(bbox1.y1, bbox2.y1);

    float inter_w = inter_x1 - inter_x0;
    float inter_h = inter_y1 - inter_y0;
    
    float inter_area = inter_w * inter_h;
    float union_area = 
        (bbox1.x1 - bbox1.x0) * (bbox1.y1 - bbox1.y0) + 
        (bbox2.x1 - bbox2.x0) * (bbox2.y1 - bbox2.y0) - 
        inter_area;
    
    return inter_area / union_area;
}


void PlateDetector::setup(void const* data, size_t size) {
   /*
     * detector setup需要做的事情
     *   创建engine, context
     *   设置bindings。这里需要注意，不同版本的yolo的输出binding可能还不一样
     *   分配memory空间。这里需要注意，不同版本的yolo的输出所需要的空间也还不一样
     */

    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    m_outputDims  = m_context->getBindingDimensions(1);

    CUDA_CHECK(cudaStreamCreate(&m_stream));
    
    m_inputSize     = m_params->batch_size * m_params->img.h * m_params->img.w * m_params->img.c * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;
    m_outputSize    = m_params->batch_size * m_outputDims.d[1] * m_outputDims.d[2] * sizeof(float);

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_outputMemory[0], m_outputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_outputMemory[1], m_outputSize));

    // 创建m_bindings，之后再寻址就直接从这里找
    m_bindings[0] = m_inputMemory[1];
    m_bindings[1] = m_outputMemory[1];
}

void PlateDetector::reset_task(){}

bool PlateDetector::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    /*Preprocess -- yolo的预处理并没有mean和std，所以可以直接skip掉mean和std的计算 */

    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i); 
            return false;
        }
    }

    // 初始化letterbox相关变量
    preprocess::params_init(m_activedBatchSize);

    /*Preprocess -- 测速*/
    // m_timer->start_cpu();

    // 对每个批次进行处理
    for (int b = 0; b < m_params->batch_size; b++) {
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        // 每个通道在当前批次内的起始位置
        const int offset_ch0 = offset_batch;                  // R通道起始位置
        const int offset_ch1 = offset_batch + m_imgArea;      // G通道起始位置
        const int offset_ch2 = offset_batch + 2 * m_imgArea;  // B通道起始位置

        if (b < m_activedBatchSize) {
            /*Preprocess -- resize(手动实现一个CPU版本的letterbox)*/
            int   input_w  = m_inputImages[b].cols;  // dammit, w and h are reversed...
            int   input_h  = m_inputImages[b].rows;
            int   target_w = m_params->img.w;
            int   target_h = m_params->img.h;
            float scale    = min(float(target_w)/input_w, float(target_h)/input_h);
            int   new_w    = int(input_w * scale);
            int   new_h    = int(input_h * scale);

            preprocess::warpaffine_init(input_h, input_w, target_h, target_w, b);

            cv::Mat tar(target_w, target_h, CV_8UC3,
                      cv::Scalar(m_padding_value, m_padding_value, m_padding_value));
            cv::Mat resized_img;
            cv::resize(m_inputImages[b], resized_img, cv::Size(new_w, new_h));

            /* 寻找resize后的图片在背景中的位置*/
            int x, y;
            x = (new_w < target_w) ? (target_w - new_w) / 2 : 0;
            y = (new_h < target_h) ? (target_h - new_h) / 2 : 0;

            cv::Rect roi(x, y, new_w, new_h);

            /* 指定背景图片里居中的图片roi，把resized_img给放入到这个roi中*/
            cv::Mat roiOfTar = tar(roi);
            resized_img.copyTo(roiOfTar);

            /*Preprocess -- host端进行normalization和BGR2RGB, NHWC->NCHW*/
            for (int i = 0; i < target_h; i++) {
                for (int j = 0; j < target_w; j++) {
                    int index = i * target_w * 3 + j * 3;
                    m_inputMemory[0][offset_ch2 + i * target_w + j] = tar.data[index + 0] / 255.0f;
                    m_inputMemory[0][offset_ch1 + i * target_w + j] = tar.data[index + 1] / 255.0f;
                    m_inputMemory[0][offset_ch0 + i * target_w + j] = tar.data[index + 2] / 255.0f;
                }
            }
        } else {
            // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
            for (int i = 0; i < m_params->img.h; i++) {
                for (int j = 0; j < m_params->img.w; j++) {
                    m_inputMemory[0][offset_ch0 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch1 + i * m_params->img.w + j] = 0;
                    m_inputMemory[0][offset_ch2 + i * m_params->img.w + j] = 0;
                }
            }
        }
    }

    /*Preprocess -- 将host的数据移动到device上*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));

    // m_timer->stop_cpu<timer::Timer::ms>("preprocess(CPU)");
    return true;
}

bool PlateDetector::preprocess_gpu() {
    /*Preprocess -- yolo的预处理并没有mean和std，所以可以直接skip掉mean和std的计算 */

    // /*Preprocess -- 读取数据*/
    // m_inputImage = cv::imread(m_imagePath);
    if (m_inputImage.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }
    
    /*Preprocess -- 测速*/
    // m_timer->start_gpu();

    /*Preprocess -- 使用GPU进行warpAffine, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(m_inputImage, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w, 
                                   preprocess::tactics::GPU_WARP_AFFINE);

    // m_timer->stop_gpu("preprocess(GPU)");
    return true;
}


bool PlateDetector::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);
    // m_timer->start_cpu();

    /*Postprocess -- 将device上的数据移动到host上*/
    CUDA_CHECK(cudaMemcpyAsync(m_outputMemory[0], m_outputMemory[1], m_outputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    /*Postprocess -- yolov8的postprocess需要做的事情*/
    /*
     * 1. 把bbox从输出tensor拿出来，并进行decode，把获取的bbox放入到m_bboxes中
     * 2. 把decode得到的m_bboxes根据nms threshold进行NMS处理
     * 3. 把最终得到的bbox绘制到原图中
     */

    float conf_threshold = m_params->conf_thresh; //用来过滤decode时的bboxes  0.001
    float nms_threshold  = m_params->nms_thresh;  //用来过滤nms时的bboxes  0.7

    // 对每个批次进行处理
    for (int b = 0; b < m_activedBatchSize; b++) {
        /*Postprocess -- 1. decode*/
        /*
         * 我们需要做的就是将[batch, bboxes, ch]转换为vector<bbox>
         * 几个步骤:
         * 1. 从每一个bbox中对应的ch中获取cx, cy, width, height
         * 2. 对每一个bbox中对应的ch中，找到最大的class label, 可以使用std::max_element
         * 3. 将cx, cy, width, height转换为x0, y0, x1, y1
         * 4. 因为图像是经过resize了的，所以需要根据resize的scale和shift进行坐标的转换(这里面可以根据preprocess中的到的affine matrix来进行逆变换)
         * 5. 将转换好的x0, y0, x1, y1，以及confidence和classness给存入到box中，并push到m_bboxes中，准备接下来的NMS处理
         */
        int    boxes_count = m_outputDims.d[1];
        int    class_count = m_outputDims.d[2] - m_xywhs;
        float* tensor;

        float  cx, cy, w, h, obj, prob, conf;
        float  x0, y0, x1, y1;
        int    label;
        
        std::vector<bbox> batch_bboxes;

        for (int i = 0; i < boxes_count; i++) {
            // 计算当前批次的偏移
            tensor = m_outputMemory[0] + b * boxes_count * m_outputDims.d[2] + i * m_outputDims.d[2];

            if (b == 0 && i == 0) {
                for (int j = 0; j < class_count; j++) {
                    LOGD("class %d: confidence: %6.7f", j, *(tensor + m_xywhs + j));
                }
            }

            label = 0;
            // prob  = tensor[m_xywhs + label];
            switch(m_params->det_model) {
                case model::detection_model::YOLOV5:
                    obj = tensor[4]; // Objectness score
                    break;
                case model::detection_model::YOLOV7:
                    obj = tensor[4]; // Objectness score
                    break;
                case model::detection_model::YOLOV8:
                    obj = 1.0; // YOLOv8 does not have objectness score
                    break;
            }
            conf  = tensor[4]; // YOLOv5, YOLOv7 combines objectness and class probability

            if (conf < conf_threshold) 
                continue;

            cx = tensor[0];
            cy = tensor[1];
            w  = tensor[2];
            h  = tensor[3];
            
            x0 = cx - w / 2;
            y0 = cy - h / 2;
            x1 = x0 + w;
            y1 = y0 + h;

            // 通过warpaffine的逆变换得到yolo feature中的x0, y0, x1, y1在原图上的坐标
            preprocess::affine_transformation(preprocess::vec_affine_matrix[b].reverse, x0, y0, &x0, &y0);
            preprocess::affine_transformation(preprocess::vec_affine_matrix[b].reverse, x1, y1, &x1, &y1);
            
            bbox yolo_box(x0, y0, x1, y1, conf, label);
            batch_bboxes.emplace_back(yolo_box);
        }
        LOGD("Batch %d: the count of decoded bbox is %d", b, batch_bboxes.size());
        
        /*Postprocess -- 2. NMS*/
        /* 
         * 几个步骤:
         * 1. 做一个IoU计算的lambda函数
         * 2. 将m_bboxes中的所有数据，按照confidence从高到低进行排序
         * 3. 最终希望是对于每一个class，我们都只有一个bbox，所以对同一个class的所有bboxes进行IoU比较，
         *    选取confidence最大。并与其他的同类bboxes的IoU的重叠率最大的同时IoU > IoU threshold
         */

        std::vector<bbox> final_bboxes;
        final_bboxes.reserve(batch_bboxes.size());
        std::sort(batch_bboxes.begin(), batch_bboxes.end(), 
                [](bbox& box1, bbox& box2){return box1.confidence > box2.confidence;});

        for(int i = 0; i < batch_bboxes.size(); i++) {
            if (batch_bboxes[i].flg_remove)
                continue;
            
            final_bboxes.emplace_back(batch_bboxes[i]);
            for (int j = i + 1; j < batch_bboxes.size(); j++) {
                if (batch_bboxes[j].flg_remove)
                    continue;

                if (batch_bboxes[i].label == batch_bboxes[j].label) {
                    if (iou_calc(batch_bboxes[i], batch_bboxes[j]) > nms_threshold)
                        batch_bboxes[j].flg_remove = true;
                }
            }
        }
        LOGD("Batch %d: the count of bbox after NMS is %d", b, final_bboxes.size());
        
        /*Postprocess -- draw_bbox*/
        /*
         * 几个步骤
         * 1. 通过label获取name
         * 2. 通过label获取color
         * 3. cv::rectangle
         * 4. cv::putText
         */
        int   font_face  = 0;
        float font_scale = 0.001 * MIN(m_inputImages[b].cols, m_inputImages[b].rows);
        int   font_thick = 2;
        int   baseline;
        CocoLabels labels;
        // ImageCropper image_cropper(1);

        LOG("\tBatch %d Result:", b);
        for (int i = 0; i < final_bboxes.size(); i++) {
            auto box = final_bboxes[i];
            auto name = labels.coco_get_label(box.label);
            auto rec_color = labels.coco_get_color(box.label);
            auto txt_color = labels.get_inverse_color(rec_color);
            auto txt = cv::format({"%s: %.2f%%"}, name.c_str(), box.confidence * 100);
            auto txt_size = cv::getTextSize(txt, font_face, font_scale, font_thick, &baseline);

            int txt_height = txt_size.height + baseline + 10;
            int txt_width  = txt_size.width + 3;

            cv::Point txt_pos(round(box.x0), round(box.y0 - (txt_size.height - baseline + font_thick)));
            cv::Rect  txt_rec(round(box.x0 - font_thick), round(box.y0 - txt_height), txt_width, txt_height);
            cv::Rect  box_rec(round(box.x0), round(box.y0), round(box.x1 - box.x0), round(box.y1 - box.y0));

            // image_cropper.addRect(box_rec);

            cv::rectangle(m_inputImages[b], box_rec, rec_color, 3);
            cv::rectangle(m_inputImages[b], txt_rec, rec_color, -1);
            cv::putText(m_inputImages[b], txt, txt_pos, font_face, font_scale, txt_color, font_thick, 16);

            LOG("\t\t%s detected. Confidence: %.2f%%. Cord: (x0, y0):(%6.2f, %6.2f), (x1, y1)(%6.2f, %6.2f)", 
                name.c_str(), box.confidence * 100, box.x0, box.y0, box.x1, box.y1);
        }
        LOG("\tBatch %d Summary:", b);
        LOG("\t\tDetected Objects: %d", final_bboxes.size());

        // std::vector<cv::Mat> cropped_images = image_cropper.crop(m_inputImages[b]);
        // for (int i = 0; i < cropped_images.size(); i++) {
        //     std::string timestamp = std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
        //     std::string image_path = "./" + timestamp + ".jpg";
        //     cv::imwrite(image_path, cropped_images[i]);
        // }

        /*Postprocess -- save results to cache*/
        {
            std::lock_guard<std::mutex> lock(mtx_result);
            // 为每个批次生成唯一ID
            std::string batch_id = m_frame_id + "_" + std::to_string(b);
            obj_results.insert({batch_id, final_bboxes});
            img_results.insert({batch_id, m_inputImages[b]});
        }
    }

    // m_timer->stop_cpu<timer::Timer::ms>("postprocess(CPU)");
    // m_timer->show();

    return true;
}


bool PlateDetector::postprocess_gpu() {
    return postprocess_cpu();
}


// this func will be called by other thread
bool PlateDetector::get_obj_result(std::vector<bbox> &objects, std::string id) {
    int loop_cnt = 0;
    while (obj_results.find(id) == obj_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_obj_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    objects = obj_results[id];
    obj_results.erase(id);

    return true;
}


bool PlateDetector::check_model() {
    if (Model::m_params->det_model == model::NULLDET) {
        LOGW("No detection model specified");
        return false;
    }
    return true;
}


// this func will be called by other thread
bool PlateDetector::get_img_result(cv::Mat &img, std::string id) {
    int loop_cnt = 0;
    while (img_results.find(id) == img_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_img_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    img = img_results[id];
    img_results.erase(id);

    return true;
}


shared_ptr<PlateDetector> make_plate_detector(
    std::string onnx_path, logger::Level level, Params params)
{
    return make_shared<PlateDetector>(onnx_path, level, params);
}

}; // namespace detector
}; // namespace model
