version: "3"

services:
  det2d_service:
    image: harborserver.tyjt-ai.com/det2d/trt_starter:22.12-road-crack
    restart: on-failure
    container_name: trt_starter_road_crack
    runtime: nvidia
    ipc: host
    network_mode: host
    volumes:
      # - type: bind
      #   source: /usr/share/zoneinfo/Asia/Shanghai
      #   target: /usr/share/zoneinfo/Asia/Shanghai
      # - type: bind
      #   source: /usr/lib/x86_64-linux-gnu/libnvcuvid.so.535.183.01
      #   target: /usr/lib/x86_64-linux-gnu/libnvcuvid.so.535.183.01
      # - type: bind
      #   source: /usr/lib/x86_64-linux-gnu/libnvcuvid.so.1
      #   target: /usr/lib/x86_64-linux-gnu/libnvcuvid.so.1
      - type: bind
        source: /tmp/.X11-unix
        target: /tmp/.X11-unix
    environment:
      - DISPLAY=:1
      - NVIDIA_VISIBLE_DEVICES=0
      - NACOS_SERVER_IP=127.0.0.1
      - NACOS_NAMESPACE_ID=4d11a15d-b36c-46e6-96a4-520ac1326e14
      - GROUP_ID=G32050700001B01
      - KAFKA_BROKER_ID=127.0.0.1:9092
      - KAFKA_TOPIC_NAME=virtual_events
      - KAFKA_PARTITION_ID=0
    cpus: 8
    command: bash -c "sleep 1 && exec bash -c '/app/dev_ws_det2d/start.sh'" 
    tty: true 
    stdin_open: true
