// Copyright 2022 Allride.ai. All Rights Reserved.

syntax = "proto3";

package perception.detect;

import "esurfing/math/geo.proto";
import "esurfing/math/color.proto";

option java_outer_classname = "PercDetect";
option java_multiple_files = false;

message DetectedObjects {
  sfixed64 time_meas = 1; // in ms, data time_stamp from driver
  sfixed64 time_pub = 2; // in ms, time_stamp when publish
  bytes group_name = 3; // group id
  bytes group_info = 4; // user defined info
  repeated DetectedObject objects = 5;
}

enum ObjectType { // 仅供标准规范用
  UNKNOWN = 0;    // 未知
  CAR = 1;      // 小汽车
  PEDESTRIAN = 2;  // 行人
  CYCLIST = 3;    // 骑自行车的人
  TRUCK = 4;     // 卡车
  VAN = 5;      // 箱式货车
  BUS = 6;      // 公共汽车
  STATIC = 7;     // 静态
  STATIC_EDGE = 8;
  CONE = 9;
  TROLLEY = 10;   // 三轮车
  ROBOT = 11;
  GATE = 12;
  
  AIRPLANE = 13;   // 飞机
  DRONE = 14;     // 无人机
  HELICOPTER = 15;  // 直升机
  EVTOL = 16;     // 电动垂直起降飞机
  BIRD = 17;     // 鸟
}

message LicenePlate {
  int32 color = 1;   // green=0, yellow=1, black=2, white-3，blue=4
  float color_confidence = 2;
  string number = 3;
  float number_confidence = 4;
}

// 跟踪置信度
message TrackConfidence {
  uint32 color = 1;        // vehicle color detec count, 0~∞
  uint32 type = 2;         // object type detec count, 0~无穷大
  uint32 plate_number = 3; // plate number detec count, 0~∞
  uint32 plate_color = 4;  // plate color detec count, 0~∞
}

message DetectedObject {
  bytes overlap_name = 1; // belongs to which overlapped region
  fixed64 uuid = 2; // unique_id
  sint32 type = 3;
  float confidence = 4;
  Vector3f position = 5; // in map frame, in meter
  Vector3f shape = 6; // length, width, height, in meter
  Polygon hull = 7; // in map frame, BEV (2D) contour
  float orientation = 8; // physical object heading in map frame [-pi, pi)
  Velocity velocity = 9; // heading and speed in map frame
  bool is_static = 10; // the object is static (true) or not (false)
  Color color = 11; // vehicle color

  repeated float feature = 12; // ReID feature vector
  repeated WayPoints trajectories = 13; // history/predicted trajectories
  repeated bytes str_array = 14; // user defined info
  repeated sint32 int_array = 15; // user defined info
  int64 parking_time = 16;

  LicenePlate plate = 17;  // vehicle plate
  int32 obj_color = 18;   // vehicle color, 黑=0,白=1,灰=2,红=3,黄=4,绿=5,蓝=6,紫=7,棕=8,粉=9,橙=10,其他=11
  string group_id = 19;   // small group id
  float obj_color_conf = 20; //vehicle color confident
  TrackConfidence track_conf = 21; // track confidence

  // string camera_name = 22; // project 3D fusion box onto camera to avoid 2D detection
  // Vector3f position_image = 23; // x-left, y-top, 1; in pixel
  // Vector2f shape_image = 24; // width, height; in pixel
}

message Velocity {
  float heading = 1; // velocity heading [-pi, pi)
  float speed = 2; // velocity value in m/s
  float acceleration = 3; // in m/s2
}

message WayPoints {
  repeated WayPoint waypoints = 1;
  float probability = 2;
}

message WayPoint {
  sfixed64 time_meas = 1; // in ms
  Vector3f position = 2; // in map frame, in meter
  Velocity velocity = 3; // in map frame
}
