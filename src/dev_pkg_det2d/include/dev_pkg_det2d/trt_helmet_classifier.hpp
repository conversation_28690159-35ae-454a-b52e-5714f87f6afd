#ifndef __TRT_HELMET_CLASSIFIER_HPP__
#define __TRT_HELMET_CLASSIFIER_HPP__

#include <memory>
#include <vector>
#include <string>
#include "NvInfer.h"
#include "trt_logger.hpp"
#include "trt_model.hpp"

namespace model{

namespace classifier {
class HelmetClassifier : public Model{

public:
    // 这个构造函数实际上调用的是父类的Model的构造函数
    HelmetClassifier(std::string onnx_path, logger::Level level, Params params) :
        Model(onnx_path, level, params) {};

public:
    // 这里classifer自己实现了一套前处理/后处理，以及内存分配的初始化
    virtual void setup(void const* data, std::size_t size) override;
    virtual void reset_task() override;
    virtual bool preprocess_cpu() override;
    virtual bool preprocess_gpu() override;
    virtual bool postprocess_cpu() override;
    virtual bool postprocess_gpu() override;
    // check model name in params
    virtual bool check_model() override;
    virtual void inference() override;

    // 存储分类结果的结构体
    struct ClassificationResult {
        int cls;
        float confidence;
    };

    // 存储批次处理的分类结果
    std::vector<ClassificationResult> m_batch_results;

    // 获取分类结果
    ClassificationResult get_classification_result(int batch_idx) const {
        if (batch_idx >= m_activedBatchSize || batch_idx >= static_cast<int>(m_batch_results.size())) {
            return {-1, 0.0f};
        }

        return m_batch_results[batch_idx];
    }

    // 获取所有有效的分类结果
    const std::vector<ClassificationResult>& get_all_classification_results() const {
        return m_batch_results;
    }

private:
    // float m_confidence;
    // std::string m_label;
    int m_inputSize;
    int m_imgArea;
    // int m_outputSize;
    int m_helmetLabelSize;
    int m_helmetOutputSize;
    float* m_helmetOutputMemory[2];
    float* m_bindingsOverride[2];

    const std::map<int, std::string> HELMET_MAP = {
        {0, "withHelmet"}, {1, "helmetMissing"}
    };
};

// 外部调用的接口
std::shared_ptr<HelmetClassifier> make_helmet_classifier(
    std::string onnx_path, logger::Level level, Params params);

}; // namespace classifier
}; // namespace model

#endif //__TRT_HELMET_CLASSIFIER_HPP__
