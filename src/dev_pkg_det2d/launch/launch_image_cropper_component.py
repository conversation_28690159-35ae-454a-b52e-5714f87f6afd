from launch import LaunchDescription
from launch_ros.actions import ComposableNode<PERSON>ontainer
from launch_ros.descriptions import ComposableNode

def generate_launch_description():
    container = ComposableNodeContainer(
        name='image_processing_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dev_pkg_det2d',
                plugin='Det2dWorker::ImageCropperNode',
                name='image_cropper_node',
                parameters=[{
                    'image_topics': ['/image_topic_1'],
                    'object_topic': '/objects_topic',
                    'buffer_name': 'cropped_objects',
                    'buffer_size': 50,
                    'confidence_threshold': 0.5,
                    'target_object_types': [1],
                    'cropper_padding': 10,
                    'cleanup_max_age_sec': 60,
                    'cleanup_interval_sec': 30,
                    'time_tolerance_ms': 50,
                    'image_buffer_queue_size': 50
                }],
                extra_arguments=[{'use_intra_process_comms': True}]
            ),
            ComposableNode(
                package='dev_pkg_tools',
                plugin='Det2dWorker::ImageObjectPublisher',
                name='img_obj_publisher_node',
                parameters=[{
                    'image_path': '/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_det2d/data/source/plate_det/suzhou_3840_2160CrossImage2.jpg',
                    'image_topics': ['/image_topic_1'],
                    'frame_id': 'camera_frame',
                    'config_path': '/workspace/test/dev_dla/dev_ws_det2d/src/dev_pkg_tools/config/objects_config.yaml',
                    'object_topic': '/objects_topic',
                    'group_name': 'default_group',
                    'publish_frequency': 10.0
                }],
                extra_arguments=[{'use_intra_process_comms': True}]
            )
        ],
        output='screen',
    )

    return LaunchDescription([container])