#include "opencv2/core/types.hpp"
#include "opencv2/imgproc.hpp"
#include "trt_model.hpp"
#include "utils.hpp" 
#include "trt_logger.hpp"

#include "NvInfer.h"
#include "NvOnnxParser.h"
#include <algorithm>
#include <string>

#include "opencv2/core/core.hpp"
#include "opencv2/highgui/highgui.hpp"
#include "opencv2/imgproc//imgproc.hpp"
#include "opencv2/opencv.hpp"
#include "trt_vehicle_detector_detr.hpp"
#include "trt_preprocess.hpp"
#include "vehicle_labels_detr.hpp"
#include "image_cropping.hpp"

using namespace std;
using namespace nvinfer1;

namespace model{

namespace detector {

float VehicleDetectorDetr::iou_calc(bbox bbox1, bbox bbox2){
    auto inter_x0 = std::max(bbox1.x0, bbox2.x0);
    auto inter_y0 = std::max(bbox1.y0, bbox2.y0);
    auto inter_x1 = std::min(bbox1.x1, bbox2.x1);
    auto inter_y1 = std::min(bbox1.y1, bbox2.y1);

    float inter_w = inter_x1 - inter_x0;
    float inter_h = inter_y1 - inter_y0;
    
    float inter_area = inter_w * inter_h;
    float union_area = 
        (bbox1.x1 - bbox1.x0) * (bbox1.y1 - bbox1.y0) + 
        (bbox2.x1 - bbox2.x0) * (bbox2.y1 - bbox2.y0) - 
        inter_area;
    
    return inter_area / union_area;
}


void VehicleDetectorDetr::setup(void const* data, size_t size) {
   /*
     * detector setup需要做的事情
     *   创建engine, context
     *   设置bindings。这里需要注意，不同版本的yolo的输出binding可能还不一样
     *   分配memory空间。这里需要注意，不同版本的yolo的输出所需要的空间也还不一样
     */

    m_runtime     = shared_ptr<IRuntime>(createInferRuntime(*m_logger), destroy_trt_ptr<IRuntime>);
    m_engine      = shared_ptr<ICudaEngine>(m_runtime->deserializeCudaEngine(data, size), destroy_trt_ptr<ICudaEngine>);
    m_context     = shared_ptr<IExecutionContext>(m_engine->createExecutionContext(), destroy_trt_ptr<IExecutionContext>);
    m_inputDims   = m_context->getBindingDimensions(0);
    m_outputDims  = m_context->getBindingDimensions(1);

    CUDA_CHECK(cudaStreamCreate(&m_stream));
    
    m_inputSize     = m_params->batch_size * m_params->img.h * m_params->img.w * m_params->img.c * sizeof(float);
    m_imgArea       = m_params->img.h * m_params->img.w;
    m_outputSize    = m_params->batch_size * m_outputDims.d[1] * m_outputDims.d[2] * sizeof(float);

    // 这里对host和device上的memory一起分配空间
    CUDA_CHECK(cudaMallocHost(&m_inputMemory[0], m_inputSize));
    CUDA_CHECK(cudaMallocHost(&m_outputMemory[0], m_outputSize));
    CUDA_CHECK(cudaMalloc(&m_inputMemory[1], m_inputSize));
    CUDA_CHECK(cudaMalloc(&m_outputMemory[1], m_outputSize));

    // 创建m_bindings，之后再寻址就直接从这里找
    m_bindings[0] = m_inputMemory[1];
    m_bindings[1] = m_outputMemory[1];
}

void VehicleDetectorDetr::reset_task(){}

bool VehicleDetectorDetr::preprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_preprocess_mutex);
    /*Preprocess -- yolo的预处理并没有mean和std，所以可以直接skip掉mean和std的计算 */
    m_originalSizes.clear();
    // 检查输入图像
    for (int i = 0; i < m_activedBatchSize; i++) {
        if (m_inputImages[i].data == nullptr) {
            LOGE("ERROR: Image %d data is null! Program terminated", i); 
            return false;
        }
        // 保存原始图像尺寸
        m_originalSizes.push_back({m_inputImages[i].cols, m_inputImages[i].rows});
    }

    /* ---------------------------------------方法1. letterbox------------------------------------- */
    // // 初始化letterbox相关变量
    // preprocess::params_init(m_activedBatchSize);

    // /*Preprocess -- 测速*/
    // // m_timer->start_cpu();

    // // 对每个批次进行处理
    // for (int b = 0; b < m_params->batch_size; b++) {
    //     // 每个批次的起始偏移
    //     const int offset_batch = b * m_imgArea * m_params->img.c;
    //     // 每个通道在当前批次内的起始位置
    //     const int offset_ch0 = offset_batch;                  // R通道起始位置
    //     const int offset_ch1 = offset_batch + m_imgArea;      // G通道起始位置
    //     const int offset_ch2 = offset_batch + 2 * m_imgArea;  // B通道起始位置

    //     if (b < m_activedBatchSize) {
    //         /*Preprocess -- resize(手动实现一个CPU版本的letterbox)*/
    //         int   input_w  = m_inputImages[b].cols;  // dammit, w and h are reversed...
    //         int   input_h  = m_inputImages[b].rows;
    //         int   target_w = m_params->img.w;
    //         int   target_h = m_params->img.h;
    //         float scale    = min(float(target_w)/input_w, float(target_h)/input_h);
    //         int   new_w    = int(input_w * scale);
    //         int   new_h    = int(input_h * scale);

    //         preprocess::warpaffine_init(input_h, input_w, target_h, target_w, b);

    //         cv::Mat tar(target_w, target_h, CV_8UC3,
    //                   cv::Scalar(m_padding_value, m_padding_value, m_padding_value));
    //         cv::Mat resized_img;
    //         cv::resize(m_inputImages[b], resized_img, cv::Size(new_w, new_h));

    //         /* 寻找resize后的图片在背景中的位置*/
    //         int x, y;
    //         x = (new_w < target_w) ? (target_w - new_w) / 2 : 0;
    //         y = (new_h < target_h) ? (target_h - new_h) / 2 : 0;

    //         cv::Rect roi(x, y, new_w, new_h);

    //         /* 指定背景图片里居中的图片roi，把resized_img给放入到这个roi中*/
    //         cv::Mat roiOfTar = tar(roi);
    //         resized_img.copyTo(roiOfTar);

    //         /*Preprocess -- host端进行normalization和BGR2RGB, NHWC->NCHW*/
    //         for (int i = 0; i < target_h; i++) {
    //             for (int j = 0; j < target_w; j++) {
    //                 int index = i * target_w * 3 + j * 3;
    //                 m_inputMemory[0][offset_ch2 + i * target_w + j] = tar.data[index + 0] / 255.0f;
    //                 m_inputMemory[0][offset_ch1 + i * target_w + j] = tar.data[index + 1] / 255.0f;
    //                 m_inputMemory[0][offset_ch0 + i * target_w + j] = tar.data[index + 2] / 255.0f;
    //             }
    //         }
    //     } else {
    //         // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
    //         for (int i = 0; i < m_params->img.h; i++) {
    //             for (int j = 0; j < m_params->img.w; j++) {
    //                 m_inputMemory[0][offset_ch0 + i * m_params->img.w + j] = 0;
    //                 m_inputMemory[0][offset_ch1 + i * m_params->img.w + j] = 0;
    //                 m_inputMemory[0][offset_ch2 + i * m_params->img.w + j] = 0;
    //             }
    //         }
    //     }
    // }
    /* -------------------------------------------------------------------------------------------- */

    /* ---------------------------------------方法2. 直接resize------------------------------------- */
    /*Preprocess -- resize(默认是bilinear interpolation)*/
    std::vector<cv::Mat> resized_images(m_activedBatchSize);
    for (int i = 0; i < m_activedBatchSize; i++) {
        cv::resize(m_inputImages[i], resized_images[i], 
                   cv::Size(m_params->img.w, m_params->img.h), 0, 0, cv::INTER_LINEAR);
    }
    /*Preprocess -- host端进行normalization和BGR2RGB, NHWC->NCHW*/
    for (int b = 0; b < m_params->batch_size; b++) {
        // 每个批次的起始偏移
        const int offset_batch = b * m_imgArea * m_params->img.c;
        // 每个通道在当前批次内的起始位置
        const int offset_ch0 = offset_batch;          // R通道起始位置
        const int offset_ch1 = offset_batch + m_imgArea;   // G通道起始位置
        const int offset_ch2 = offset_batch + 2 * m_imgArea; // B通道起始位置
        for (int i = 0; i < m_params->img.h; i++) {
            for (int j = 0; j < m_params->img.w; j++) {
                // 原图NHWC布局中的像素索引（BGR顺序）
                const int src_index = (i * m_params->img.w + j) * m_params->img.c;
                // 目标NCHW布局中的像素索引（RGB顺序）
                const int dst_pos = i * m_params->img.w + j;
                if (b < m_activedBatchSize) {
                    // BGR转RGB：input[BGR] -> output[RGB]
                    m_inputMemory[0][offset_ch0 + dst_pos] = // R通道
                        resized_images[b].data[src_index + 2] / 255.0f;
                    m_inputMemory[0][offset_ch1 + dst_pos] = // G通道
                        resized_images[b].data[src_index + 1] / 255.0f;
                    m_inputMemory[0][offset_ch2 + dst_pos] = // B通道
                        resized_images[b].data[src_index + 0] / 255.0f;
                } else {
                    // 如果batch_size大于m_activedBatchSize，那么剩下的部分需要填充0
                    m_inputMemory[0][offset_ch0 + dst_pos] = 0;
                    m_inputMemory[0][offset_ch1 + dst_pos] = 0;
                    m_inputMemory[0][offset_ch2 + dst_pos] = 0;
                }
            }
        }
    }
    /* -------------------------------------------------------------------------------------------- */

    /*Preprocess -- 将host的数据移动到device上*/
    CUDA_CHECK(cudaMemcpyAsync(m_inputMemory[1], m_inputMemory[0], m_inputSize, cudaMemcpyKind::cudaMemcpyHostToDevice, m_stream));

    // m_timer->stop_cpu<timer::Timer::ms>("preprocess(CPU)");
    return true;
}

bool VehicleDetectorDetr::preprocess_gpu() {
    /*Preprocess -- yolo的预处理并没有mean和std，所以可以直接skip掉mean和std的计算 */

    // /*Preprocess -- 读取数据*/
    // m_inputImage = cv::imread(m_imagePath);
    if (m_inputImage.data == nullptr) {
        LOGE("ERROR: file not founded! Program terminated"); return false;
    }
    
    /*Preprocess -- 测速*/
    // m_timer->start_gpu();

    /*Preprocess -- 使用GPU进行warpAffine, 并将结果返回到m_inputMemory中*/
    preprocess::preprocess_resize_gpu(m_inputImage, m_inputMemory[1],
                                   m_params->img.h, m_params->img.w, 
                                   preprocess::tactics::GPU_WARP_AFFINE);

    // m_timer->stop_gpu("preprocess(GPU)");
    return true;
}


bool VehicleDetectorDetr::postprocess_cpu() {
    std::lock_guard<std::mutex> lock(m_postprocess_mutex);
    m_batch_obj_bboxes = std::vector<std::vector<bbox>>(m_activedBatchSize, std::vector<bbox>());
    m_batch_cropped_images = std::vector<std::vector<cv::Mat>>(m_activedBatchSize, std::vector<cv::Mat>());
    // m_timer->start_cpu();

    /*Postprocess -- 将device上的数据移动到host上*/
    CUDA_CHECK(cudaMemcpyAsync(m_outputMemory[0], m_outputMemory[1], m_outputSize, cudaMemcpyKind::cudaMemcpyDeviceToHost, m_stream));
    CUDA_CHECK(cudaStreamSynchronize(m_stream));

    /*Postprocess -- yolov8的postprocess需要做的事情*/
    /*
     * 1. 把bbox从输出tensor拿出来，并进行decode，把获取的bbox放入到m_bboxes中
     * 2. 把decode得到的m_bboxes根据nms threshold进行NMS处理
     * 3. 把最终得到的bbox绘制到原图中
     */

    float conf_threshold = m_params->conf_thresh; //用来过滤decode时的bboxes

    // 对每个批次进行处理
    for (int b = 0; b < m_activedBatchSize; b++) {
        /*Postprocess -- 1. decode*/
        /*
         * RTDETR模型的输出格式:
         * 1. 输出是[batch, boxes, 4+num_classes]格式，其中:
         *    - 前4个值是归一化的[x, y, w, h]坐标
         *    - 后面是每个类别的得分
         * 2. RTDETR模型已经在内部处理了NMS，所以我们只需要解码输出即可
         */
        int boxes_count = m_outputDims.d[1];
        int total_dims = m_outputDims.d[2];
        int class_count = 14;
        float* tensor;
        
        float x, y, w, h, conf;
        float x0, y0, x1, y1;
        int label;
        
        std::vector<bbox> final_bboxes;
        std::vector<bbox> plate_bboxes;

        // 检查是否需要对scores进行sigmoid归一化
        bool need_sigmoid = false;
        for (int i = 0; i < boxes_count; i++) {
            float* tensor = m_outputMemory[0] + b * boxes_count * total_dims + i * total_dims;
            for (int c = 0; c < class_count; c++) {
                float score = tensor[4 + c];
                if (score <= 0 || score >= 1) {
                    need_sigmoid = true;
                    break;
                }
            }
            if (need_sigmoid) break;
        }
        
        // 处理每个检测框
        for (int i = 0; i < boxes_count; i++) {
            float* tensor = m_outputMemory[0] + b * boxes_count * total_dims + i * total_dims;
            
            // 提取坐标 (cx, cy, w, h)
            float cx = tensor[0];
            float cy = tensor[1];
            float w = tensor[2];
            float h = tensor[3];
            
            // 转换为左上角和右下角坐标 (x1, y1, x2, y2)
            float x1 = cx - w / 2;
            float y1 = cy - h / 2;
            float x2 = cx + w / 2;
            float y2 = cy + h / 2;
            
            // 提取类别得分并找到最高得分的类别
            float max_score = -1.0f;
            int label = -1;
            
            for (int c = 0; c < class_count; c++) {
                float score = tensor[4 + c];
                
                // 如果需要，应用sigmoid归一化
                if (need_sigmoid) {
                    score = 1.0f / (1.0f + exp(-score));
                }
                
                if (score > max_score) {
                    max_score = score;
                    label = c;
                }
            }
            
            // 如果置信度低于阈值，跳过
            if (max_score < conf_threshold) 
                continue;
            
            // 将归一化坐标转换为原图尺寸
            float orig_w = m_originalSizes[b].width;
            float orig_h = m_originalSizes[b].height;
            
            // 确保坐标在有效范围内
            x1 = std::floor(std::min(std::max(0.0f, x1 * orig_w), orig_w - 1));
            y1 = std::floor(std::min(std::max(0.0f, y1 * orig_h), orig_h - 1));
            x2 = std::ceil(std::min(std::max(0.0f, x2 * orig_w), orig_w - 1));
            y2 = std::ceil(std::min(std::max(0.0f, y2 * orig_h), orig_h - 1));
            
            // 创建边界框对象并添加到结果中
            bbox detr_box(x1, y1, x2, y2, max_score, label);
            // final_bboxes.emplace_back(detr_box);
            if (label == 13) { // 车牌类别
                plate_bboxes.emplace_back(detr_box);
            } else {
                final_bboxes.emplace_back(detr_box);
            }
        }
        LOGD("Batch %d: the count of decoded bbox is %d", b, final_bboxes.size());
        
        /*Postprocess -- 2. NMS*/
        // RTDETR模型已经在内部处理了NMS，所以这里不需要额外的NMS步骤
        // 只需要按置信度排序以便后续处理
        // std::sort(final_bboxes.begin(), final_bboxes.end(), 
        //         [](bbox& box1, bbox& box2){return box1.confidence > box2.confidence;});
        
        /*Postprocess -- draw_bbox*/
        /*
         * 几个步骤
         * 1. 通过label获取name
         * 2. 通过label获取color
         * 3. cv::rectangle
         * 4. cv::putText
         */
        // int   font_face  = 0;
        // float font_scale = 0.001 * MIN(m_inputImages[b].cols, m_inputImages[b].rows);
        // int   font_thick = 2;
        // int   baseline;
        // VehicleLabelsDetr labels;
        // LOG("\tBatch %d Result:", b);
        // // crop
        // ImageCropper image_cropper(5);
        // for (int i = 0; i < final_bboxes.size(); i++) { 
        //     auto box = final_bboxes[i];
        //     cv::Rect  box_rec(round(box.x0), round(box.y0), round(box.x1 - box.x0), round(box.y1 - box.y0));
        //     image_cropper.addRect(box_rec);
        // }
        // std::vector<cv::Mat> cropped_images = image_cropper.crop(m_inputImages[b]);
        // // for (int i = 0; i < cropped_images.size(); i++) {
        // //     std::string timestamp = std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
        // //     std::string image_path = "./tmp/" + timestamp + ".jpg";
        // //     cv::imwrite(image_path, cropped_images[i]);
        // // }
        // m_batch_cropped_images[b] = cropped_images;
        // // draw
        // for (int i = 0; i < final_bboxes.size(); i++) {
        //     auto box = final_bboxes[i];
        //     auto name = labels.coco_get_label(box.label);
        //     auto rec_color = labels.coco_get_color(box.label);
        //     auto txt_color = labels.get_inverse_color(rec_color);
        //     auto txt = cv::format({"%s: %.2f%%"}, name.c_str(), box.confidence * 100);
        //     auto txt_size = cv::getTextSize(txt, font_face, font_scale, font_thick, &baseline);
        //     int txt_height = txt_size.height + baseline + 10;
        //     int txt_width  = txt_size.width + 3;
        //     cv::Point txt_pos(round(box.x0), round(box.y0 - (txt_size.height - baseline + font_thick)));
        //     cv::Rect  txt_rec(round(box.x0 - font_thick), round(box.y0 - txt_height), txt_width, txt_height);
        //     cv::Rect  box_rec(round(box.x0), round(box.y0), round(box.x1 - box.x0), round(box.y1 - box.y0));
        //     cv::rectangle(m_inputImages[b], box_rec, rec_color, 3);
        //     cv::rectangle(m_inputImages[b], txt_rec, rec_color, -1);
        //     cv::putText(m_inputImages[b], txt, txt_pos, font_face, font_scale, txt_color, font_thick, 16);
        //     LOG("\t\t%s detected. Confidence: %.2f%%. Cord: (x0, y0):(%6.2f, %6.2f), (x1, y1)(%6.2f, %6.2f)", 
        //         name.c_str(), box.confidence * 100, box.x0, box.y0, box.x1, box.y1);
        // }
        LOG("\tBatch %d Summary:", b);
        LOG("\t\tDetected Objects: %d", final_bboxes.size());
        // std::string timestamp = std::to_string(std::chrono::system_clock::now().time_since_epoch().count());
        // std::string image_path = "./tmp/" + timestamp + ".jpg";
        // cv::imwrite(image_path, m_inputImages[b]);

        /*Postprocess -- save results to cache*/
        // {
        //     std::lock_guard<std::mutex> lock(mtx_result);
        //     // 为每个批次生成唯一ID
        //     std::string batch_id = m_frame_id + "_" + std::to_string(b);
        //     obj_results.insert({batch_id, final_bboxes});
        //     img_results.insert({batch_id, m_inputImages[b]});
        // }
        // save box to result
        m_batch_obj_bboxes[b] = final_bboxes;
    }

    // m_timer->stop_cpu<timer::Timer::ms>("postprocess(CPU)");
    // m_timer->show();

    return true;
}


bool VehicleDetectorDetr::postprocess_gpu() {
    return postprocess_cpu();
}


// this func will be called by other thread
bool VehicleDetectorDetr::get_obj_result(std::vector<bbox> &objects, std::string id) {
    int loop_cnt = 0;
    while (obj_results.find(id) == obj_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_obj_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    objects = obj_results[id];
    obj_results.erase(id);

    return true;
}


bool VehicleDetectorDetr::check_model() {
    if (Model::m_params->det_model == model::NULLDET) {
        LOGW("No detection model specified");
        return false;
    }
    return true;
}


// this func will be called by other thread
bool VehicleDetectorDetr::get_img_result(cv::Mat &img, std::string id) {
    int loop_cnt = 0;
    while (img_results.find(id) == img_results.end()) {
        // sleep 5ms x 1000 = 5s
        std::this_thread::sleep_for(std::chrono::milliseconds(5));
        loop_cnt++;
        if (loop_cnt > 1000) {
            LOGW("get_img_result timeout");
            return false;
        }
    }
    std::lock_guard<std::mutex> lock(mtx_result);
    img = img_results[id];
    img_results.erase(id);

    return true;
}

std::vector<std::vector<bbox>> VehicleDetectorDetr::get_batch_obj_bboxes() {
    return m_batch_obj_bboxes;
}

std::vector<std::vector<cv::Mat>> VehicleDetectorDetr::get_batch_cropped_images() {
    return m_batch_cropped_images;
}


shared_ptr<VehicleDetectorDetr> make_vehicle_detector_detr(
    std::string onnx_path, logger::Level level, Params params)
{
    return make_shared<VehicleDetectorDetr>(onnx_path, level, params);
}

}; // namespace detector
}; // namespace model
