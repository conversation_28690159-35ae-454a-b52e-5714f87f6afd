import os
import random
import argparse

def get_random_files(dir_path, num_files):
    # Get all files in the directory
    all_files = [os.path.join(dir_path, f) for f in os.listdir(dir_path) if os.path.isfile(os.path.join(dir_path, f))]
    # Check if there are enough files
    if len(all_files) < num_files:
        raise ValueError(f"Not enough files in the directory. Found {len(all_files)}, but {num_files} are required.")
    # Randomly select the specified number of files
    random_files = random.sample(all_files, num_files)
    return random_files

def write_files_to_txt(file_paths, output_txt):
    with open(output_txt, 'w') as f:
        for file_path in file_paths:
            f.write(file_path + '\n')

def main():
    parser = argparse.ArgumentParser(description="Select random files from a directory and write their paths to a text file.")
    parser.add_argument('dir_path', type=str, help='Directory path containing files')
    parser.add_argument('output_txt', type=str, help='Output text file path')
    parser.add_argument('--num_files', type=int, default=64, help='Number of random files to select (default: 64)')

    args = parser.parse_args()

    try:
        random_files = get_random_files(args.dir_path, args.num_files)
        write_files_to_txt(random_files, args.output_txt)
        print(f"Paths of {args.num_files} random files have been written to {args.output_txt}")
    except ValueError as e:
        print(e)

if __name__ == "__main__":
    main()