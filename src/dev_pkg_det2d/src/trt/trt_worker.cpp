#include "trt_worker.hpp"
#include "trt_logger.hpp"
#include "memory"
#include <chrono>

using namespace std;

namespace thread{

Worker::Worker(string onnxPath, logger::Level level, model::Params params)
    : m_vehicle_detector_initialized(false),
      m_vehicle_detector_detr_initialized(false),
      m_fisheye_detector_detr_initialized(false),
      m_helmet_classifier_initialized(false),
      m_vehicle_classifier_initialized(false),
      m_plate_detector_initialized(false),
      m_plate_classifier_initialized(false),
      m_ocr_model_initialized(false),
      m_vehicle_detector_images_initialized(false),
      m_vehicle_detector_detr_images_initialized(false),
      m_fisheye_detector_detr_images_initialized(false),
      m_helmet_classifier_images_initialized(false),
      m_vehicle_classifier_images_initialized(false),
      m_plate_detector_images_initialized(false),
      m_plate_classifier_images_initialized(false),
      m_ocr_model_images_initialized(false) {
    m_logger = logger::create_logger(level);
    m_params = std::make_shared<model::Params>(params);

    // 创建线程池，线程数为n，分类和检测以及各自的异步任务共用一个线程池
    m_thread_pool = std::make_shared<ThreadPool>(4);

    // 这里根据task_type选择创建的trt_model的子类
    if (params.task == model::task_type::VEHICLE_DETECTION)
        m_vehicle_detector = model::detector::make_vehicle_detector(onnxPath, level, params);
    else if (params.task == model::task_type::VEHICLE_DETECTION_DETR)
        m_vehicle_detector_detr = model::detector::make_vehicle_detector_detr(onnxPath, level, params);
    else if (params.task == model::task_type::FISHEYE_DETECTION_DETR)
        m_fisheye_detector_detr = model::detector::make_fisheye_detector_detr(onnxPath, level, params);
    else if (params.task == model::task_type::HELMET_CLASSIFICATION)
        m_helmet_classifier = model::classifier::make_helmet_classifier(onnxPath, level, params);
    else if (params.task == model::task_type::VEHICLE_CLASSIFICATION)
        m_vehicle_classifier = model::classifier::make_vehicle_classifier(onnxPath, level, params);
    else if (params.task == model::task_type::PLATE_DETECTION)
        m_plate_detector = model::detector::make_plate_detector(onnxPath, level, params);
    else if (params.task == model::task_type::PLATE_CLASSIFICATION)
        m_plate_classifier = model::classifier::make_plate_classifier(onnxPath, level, params);
    else if (params.task == model::task_type::OCR_TASK)
        m_ocr_model = model::ocr::make_ocr_model(onnxPath, level, params);
}

Worker::~Worker() {
    // 等待所有任务完成
    if (m_thread_pool) {
        m_thread_pool->wait_all();
    }
}

void Worker::init_models() {
    std::lock_guard<std::mutex> lock(m_init_mutex);

    // 初始化车辆检测器
    if (m_vehicle_detector != nullptr && !m_vehicle_detector_initialized && m_vehicle_detector->check_model()) {
        m_vehicle_detector->init_model();
        m_vehicle_detector_initialized = true;
    }

    // 初始化车辆检测器detr
    if (m_vehicle_detector_detr != nullptr && !m_vehicle_detector_detr_initialized && m_vehicle_detector_detr->check_model()) {
        m_vehicle_detector_detr->init_model();
        m_vehicle_detector_detr_initialized = true;
    }

    // 初始化鱼眼检测器detr
    if (m_fisheye_detector_detr != nullptr && !m_fisheye_detector_detr_initialized && m_fisheye_detector_detr->check_model()) {
        m_fisheye_detector_detr->init_model();
        m_fisheye_detector_detr_initialized = true;
    }

    // 初始化头盔分类器
    if (m_helmet_classifier != nullptr && !m_helmet_classifier_initialized) {
        m_helmet_classifier->init_model();
        m_helmet_classifier_initialized = true;
    }

    // 初始化分类器
    if (m_vehicle_classifier != nullptr && !m_vehicle_classifier_initialized) {
        m_vehicle_classifier->init_model();
        m_vehicle_classifier_initialized = true;
    }

    // 初始化检测器
    if (m_plate_detector != nullptr && !m_plate_detector_initialized && m_plate_detector->check_model()) {
        m_plate_detector->init_model();
        m_plate_detector_initialized = true;
    }

    // 初始化车牌分类器
    if (m_plate_classifier != nullptr && !m_plate_classifier_initialized) {
        m_plate_classifier->init_model();
        m_plate_classifier_initialized = true;
    }

    // 初始化ocr模型
    if (m_ocr_model != nullptr && !m_ocr_model_initialized) {
        m_ocr_model->init_model();
        m_ocr_model_initialized = true;
    }
}

void Worker::init_images(std::string imagePath) {
    std::lock_guard<std::mutex> lock(m_images_mutex);

    // 初始化车辆检测器输入
    if (m_vehicle_detector != nullptr && !m_vehicle_detector_images_initialized) {
        m_vehicle_detector->load_images(imagePath);
        m_vehicle_detector_images_initialized = true;
    }

    // 初始化车辆检测器detr输入
    if (m_vehicle_detector_detr != nullptr && !m_vehicle_detector_detr_images_initialized) {
        m_vehicle_detector_detr->load_images(imagePath);
        m_vehicle_detector_detr_images_initialized = true;
    }

    // 初始化鱼眼检测器detr输入
    if (m_fisheye_detector_detr != nullptr && !m_fisheye_detector_detr_images_initialized) {
        m_fisheye_detector_detr->load_images(imagePath);
        m_fisheye_detector_detr_images_initialized = true;
    }

    // 初始化头盔分类器输入
    if (m_helmet_classifier != nullptr && !m_helmet_classifier_images_initialized) {
        m_helmet_classifier->load_images(imagePath);
        m_helmet_classifier_images_initialized = true;
    }

    // 初始化分类器输入
    if (m_vehicle_classifier != nullptr && !m_vehicle_classifier_images_initialized) {
        m_vehicle_classifier->load_images(imagePath);
        m_vehicle_classifier_images_initialized = true;
    }

    // 初始化检测器输入
    if (m_plate_detector != nullptr && !m_plate_detector_images_initialized) {
        m_plate_detector->load_images(imagePath);
        m_plate_detector_images_initialized = true;
    }

    // 初始化车牌分类器输入
    if (m_plate_classifier != nullptr && !m_plate_classifier_images_initialized) {
        m_plate_classifier->load_images(imagePath);
        m_plate_classifier_images_initialized = true;
    }

    // 初始化ocr模型输入
    if (m_ocr_model != nullptr && !m_ocr_model_images_initialized) {
        m_ocr_model->load_images(imagePath);
        m_ocr_model_images_initialized = true;
    }
}

void Worker::run_classifier(string imagePath) {
    if (m_vehicle_classifier == nullptr) return;

    // 获取ms级时间戳
    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    m_vehicle_classifier->inference();
    std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();

    LOGV("\ttime difference of classifier by sys(pre-infer-pro) uses %d ms",
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
}

void Worker::run_classifier(string frame_id, const cv::Mat& imageCap) {
    if (m_vehicle_classifier == nullptr) return;

    m_vehicle_classifier->load_image(frame_id, imageCap);
    m_vehicle_classifier->inference();
}

void Worker::run_detector(string imagePath) {
    if (m_plate_detector == nullptr || !m_plate_detector->check_model()) return;

    // 获取ms级时间戳
    std::chrono::steady_clock::time_point begin = std::chrono::steady_clock::now();
    m_plate_detector->inference();
    std::chrono::steady_clock::time_point end = std::chrono::steady_clock::now();

    LOGV("\ttime difference of detector by sys(pre-infer-pro) uses %d ms",
         std::chrono::duration_cast<std::chrono::milliseconds>(end - begin).count());
}

void Worker::run_detector(string frame_id, const cv::Mat& imageCap) {
    if (m_plate_detector == nullptr || !m_plate_detector->check_model()) return;

    m_plate_detector->load_image(frame_id, imageCap);
    m_plate_detector->inference();
}

// 同步推理接口
void Worker::inference(string imagePath) {
    // 确保模型已初始化
    init_models();
    // 确保输入已初始化
    init_images(imagePath);

    // // 异步执行分类器和检测器
    // std::vector<std::future<void>> futures;
    // if (m_vehicle_classifier != nullptr) {
    //     futures.push_back(m_thread_pool->enqueue(
    //         [this, imagePath]() { this->run_classifier(imagePath); }
    //     ));
    // }
    // if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
    //     futures.push_back(m_thread_pool->enqueue(
    //         [this, imagePath]() { this->run_detector(imagePath); }
    //     ));
    // }
    // for (auto& future : futures) {
    //     future.wait();
    // }

    // 同步执行分类器和检测器
    if (m_vehicle_classifier != nullptr) {
        run_classifier(imagePath);
    }
    if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
        run_detector(imagePath);
    }
}

void Worker::inference(string frame_id, const cv::Mat& imageCap) {
    // 确保模型已初始化
    init_models();
    // // 异步执行分类器和检测器
    // std::vector<std::future<void>> futures;
    // if (m_vehicle_classifier != nullptr) {
    //     futures.push_back(m_thread_pool->enqueue(
    //         [this, frame_id, imageCap = imageCap.clone()]() { this->run_classifier(frame_id, imageCap); }
    //     ));
    // }
    // if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
    //     futures.push_back(m_thread_pool->enqueue(
    //         [this, frame_id, imageCap = imageCap.clone()]() { this->run_detector(frame_id, imageCap); }
    //     ));
    // }
    // for (auto& future : futures) {
    //     future.wait();
    // }

    // 同步执行分类器和检测器
    if (m_vehicle_classifier != nullptr) {
        run_classifier(frame_id, imageCap);
    }
    if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
        run_detector(frame_id, imageCap);
    }
}

// 异步推理接口
std::future<void> Worker::async_inference(string imagePath) {
    return m_thread_pool->enqueue([this, imagePath]() {
        this->inference(imagePath);
    });
}

std::future<void> Worker::async_inference(string frame_id, const cv::Mat& imageCap) {
    // 创建 cv::Mat 的深拷贝
    cv::Mat imageCopy = imageCap.clone();
    return m_thread_pool->enqueue([this, frame_id, imageCopy]() {
        this->inference(frame_id, imageCopy);
    });
}

void Worker::inference_from_buffer(int64_t image_id) {
    // 确保模型已初始化
    init_models();
    // 同步执行分类器和检测器
    if (m_vehicle_classifier != nullptr) {
        if (m_vehicle_classifier->load_image_from_buffer(image_id)) {
            m_vehicle_classifier->inference();
        }
    }
    if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
        if (m_plate_detector->load_image_from_buffer(image_id)) {
            m_plate_detector->inference();
        }
    }
}

std::future<void> Worker::async_inference_from_buffer(int64_t image_id) {
    return m_thread_pool->enqueue([this, image_id]() {
        this->inference_from_buffer(image_id);
    });
}

void Worker::inference_latest_from_buffer() {
    // 确保模型已初始化
    init_models();
    // 同步执行分类器和检测器
    if (m_vehicle_classifier != nullptr) {
        if (m_vehicle_classifier->load_latest_image_from_buffer()) {
            m_vehicle_classifier->inference();
        }
    }
    if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
        if (m_plate_detector->load_latest_image_from_buffer()) {
            m_plate_detector->inference();
        }
    }
}

std::future<void> Worker::async_inference_latest_from_buffer() {
    return m_thread_pool->enqueue([this]() {
        this->inference_latest_from_buffer();
    });
}

void Worker::inference_all_from_buffer() {
    // 确保模型已初始化
    init_models();
    // 获取所有图像
    std::vector<model::ImageMetadata> metadata_list;
    std::vector<cv::Mat> images;
    {
        // 使用互斥锁保护图像缓冲区的读取
        std::lock_guard<std::mutex> lock(m_images_mutex);
        if (m_imageBuffer) {
            m_imageBuffer->get_all_images(images, metadata_list);
        }
    }
    if (images.empty()) {
        return;
    }

    // 分批处理所有图像
    const int batch_size = m_params->batch_size;
    const int total_images = images.size();
    const int num_batches = (total_images + batch_size - 1) / batch_size; // 向上取整
    for (int batch = 0; batch < num_batches; ++batch) {
        // 计算当前批次的起始和结束索引
        int start_idx = batch * batch_size;
        int end_idx = std::min(start_idx + batch_size, total_images);
        int current_batch_size = end_idx - start_idx;
        // // 提取当前批次的图像和元数据
        // std::vector<cv::Mat> batch_images(images.begin() + start_idx, images.begin() + end_idx);
        // std::vector<model::ImageMetadata> batch_metadata(metadata_list.begin() + start_idx, metadata_list.begin() + end_idx);
        // 提取当前批次的图像和元数据 深拷贝
        std::vector<cv::Mat> batch_images;
        std::vector<model::ImageMetadata> batch_metadata;
        for (int i = start_idx; i < end_idx; ++i) {
            batch_images.push_back(images[i].clone());
            batch_metadata.push_back(metadata_list[i]);
        }
        // 处理车辆检测器
        if (m_vehicle_detector != nullptr && m_vehicle_detector->check_model()) {
            {
                std::lock_guard<std::mutex> lock(m_vehicle_detector->m_preprocess_mutex);
                // 设置当前批次的图像
                m_vehicle_detector->m_inputImages = batch_images;
                m_vehicle_detector->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_vehicle_detector->inference();
        }
        // 处理车辆检测器detr
        if (m_vehicle_detector_detr != nullptr && m_vehicle_detector_detr->check_model()) {
            {
                std::lock_guard<std::mutex> lock(m_vehicle_detector_detr->m_preprocess_mutex);
                // 设置当前批次的图像
                m_vehicle_detector_detr->m_inputImages = batch_images;
                m_vehicle_detector_detr->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_vehicle_detector_detr->inference();
        }
        // 处理鱼眼检测器detr
        if (m_fisheye_detector_detr != nullptr && m_fisheye_detector_detr->check_model()) {
            {
                std::lock_guard<std::mutex> lock(m_fisheye_detector_detr->m_preprocess_mutex);
                // 设置当前批次的图像
                m_fisheye_detector_detr->m_inputImages = batch_images;
                m_fisheye_detector_detr->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_fisheye_detector_detr->inference();
        }
        // 处理头盔分类器
        if (m_helmet_classifier != nullptr) {
            {
                std::lock_guard<std::mutex> lock(m_helmet_classifier->m_preprocess_mutex);
                m_helmet_classifier->m_inputImages = batch_images;
                m_helmet_classifier->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_helmet_classifier->inference();
        }
        // 处理分类器
        if (m_vehicle_classifier != nullptr) {
            {
                std::lock_guard<std::mutex> lock(m_vehicle_classifier->m_preprocess_mutex);
                // 设置当前批次的图像
                m_vehicle_classifier->m_inputImages = batch_images;
                m_vehicle_classifier->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_vehicle_classifier->inference();
        }
        // 处理检测器
        if (m_plate_detector != nullptr && m_plate_detector->check_model()) {
            {
                std::lock_guard<std::mutex> lock(m_plate_detector->m_preprocess_mutex);
                // 设置当前批次的图像
                m_plate_detector->m_inputImages = batch_images;
                m_plate_detector->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_plate_detector->inference();
        }
        // 处理车牌分类器
        if (m_plate_classifier != nullptr) {
            {
                std::lock_guard<std::mutex> lock(m_plate_classifier->m_preprocess_mutex);
                m_plate_classifier->m_inputImages = batch_images;
                m_plate_classifier->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_plate_classifier->inference();
        }
        // 处理ocr模型
        if (m_ocr_model != nullptr) {
            {
                std::lock_guard<std::mutex> lock(m_ocr_model->m_preprocess_mutex);
                m_ocr_model->m_inputImages = batch_images;
                m_ocr_model->m_activedBatchSize = current_batch_size;
            }
            // 执行推理
            m_ocr_model->inference();
        }
    }
}

std::future<void> Worker::async_inference_all_from_buffer() {
    return m_thread_pool->enqueue([this]() {
        this->inference_all_from_buffer();
    });
}

// 批量推理接口
void Worker::batch_inference(const std::vector<cv::Mat>& images) {
    // 确保模型已初始化
    init_models();

    // 检查输入参数
    if (images.empty()) {
        LOGW("batch_inference: size of images is zero");
        return;
    }

    // 获取批次大小
    const int batch_size = images.size();

    // 处理车辆检测器
    if (m_vehicle_detector != nullptr && m_vehicle_detector->check_model()) {
        {
            std::lock_guard<std::mutex> lock(m_vehicle_detector->m_preprocess_mutex);
            // 设置当前批次的图像
            m_vehicle_detector->m_inputImages.clear();
            for (const auto& img : images) {
                m_vehicle_detector->m_inputImages.push_back(img.clone());
            }
            m_vehicle_detector->m_activedBatchSize = batch_size;
        }
        // 执行推理
        m_vehicle_detector->inference();
    }

    // 处理车辆检测器detr
    if (m_vehicle_detector_detr != nullptr && m_vehicle_detector_detr->check_model()) {
        {
            std::lock_guard<std::mutex> lock(m_vehicle_detector_detr->m_preprocess_mutex);
            // 设置当前批次的图像
            m_vehicle_detector_detr->m_inputImages.clear();
            for (const auto& img : images) {
                m_vehicle_detector_detr->m_inputImages.push_back(img.clone());
            }
            m_vehicle_detector_detr->m_activedBatchSize = batch_size;
        }
        // 执行推理
        m_vehicle_detector_detr->inference();
    }

    // 处理鱼眼检测器detr
    if (m_fisheye_detector_detr != nullptr && m_fisheye_detector_detr->check_model()) {
        {
            std::lock_guard<std::mutex> lock(m_fisheye_detector_detr->m_preprocess_mutex);
            // 设置当前批次的图像
            m_fisheye_detector_detr->m_inputImages.clear();
            for (const auto& img : images) {
                m_fisheye_detector_detr->m_inputImages.push_back(img.clone());
            }
            m_fisheye_detector_detr->m_activedBatchSize = batch_size;
        }
        // 执行推理
        m_fisheye_detector_detr->inference();
    }

    // 处理头盔分类器
    if (m_helmet_classifier != nullptr) {
        {
            std::lock_guard<std::mutex> lock(m_helmet_classifier->m_preprocess_mutex);
            // 设置当前批次的图像
            m_helmet_classifier->m_inputImages.clear();
            for (const auto& img : images) {
                m_helmet_classifier->m_inputImages.push_back(img.clone());
            }
            // 设置实际批次大小
            m_helmet_classifier->m_activedBatchSize = batch_size;
        }
        // 执行推理
        m_helmet_classifier->inference();
    }

    // 处理车型分类器
    if (m_vehicle_classifier != nullptr) {
        {
            std::lock_guard<std::mutex> lock(m_vehicle_classifier->m_preprocess_mutex);
            // 设置当前批次的图像
            m_vehicle_classifier->m_inputImages.clear();
            for (const auto& img : images) {
                m_vehicle_classifier->m_inputImages.push_back(img.clone());
            }
            // 设置实际批次大小
            m_vehicle_classifier->m_activedBatchSize = batch_size;
        }
        // 执行推理
        m_vehicle_classifier->inference();
    }
}

std::future<void> Worker::async_batch_inference(const std::vector<cv::Mat>& images) {
    // 创建深拷贝以避免数据竞争
    std::vector<cv::Mat> images_copy;
    for (const auto& img : images) {
        images_copy.push_back(img.clone());
    }

    return m_thread_pool->enqueue([this, images_copy]() {
        this->batch_inference(images_copy);
    });
}

// 等待所有异步任务完成
void Worker::wait_all() {
    if (m_thread_pool) {
        m_thread_pool->wait_all();
    }
}

std::shared_ptr<Worker> create_worker(
    std::string onnxPath, logger::Level level, model::Params params)
{
    // 使用智能指针来创建一个实例
    return make_shared<Worker>(onnxPath, level, params);
}

std::shared_ptr<Worker> create_worker_with_buffer(
    std::string onnxPath, logger::Level level, model::Params params,
    std::shared_ptr<model::ImageBuffer> buffer) {
    auto worker = std::make_shared<Worker>(onnxPath, level, params);
    if (buffer) {
        worker->set_image_buffer(buffer);
    }
    return worker;
}

}; // namespace thread