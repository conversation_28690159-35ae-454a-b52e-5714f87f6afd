#ifndef __IMAGE_BUFFER_HPP__
#define __IMAGE_BUFFER_HPP__

#include <vector>
#include <map>
#include <mutex>
#include <memory>
#include <condition_variable>
#include <opencv2/opencv.hpp>

namespace model {

// 图像元数据结构，包含时间戳和唯一ID
struct ImageMetadata {
    int64_t timestamp;  // 时间戳，从外部获取
    int64_t unique_id;  // 唯一ID，从外部获取
    std::string frame_id; // 帧标识符，可以是相机名称等
    
    ImageMetadata() : timestamp(0), unique_id(0), frame_id("") {}
    ImageMetadata(int64_t ts, int64_t id, const std::string& fid) 
        : timestamp(ts), unique_id(id), frame_id(fid) {}
};

// 图像缓冲区类，线程安全
class ImageBuffer {
public:
    ImageBuffer(size_t max_buffer_size = 10) 
        : m_max_buffer_size(max_buffer_size), m_next_id(0) {}
    
    ~ImageBuffer() {
        clear();
    }
    
    // 添加单张图像及其元数据
    bool add_image(const cv::Mat& image, const ImageMetadata& metadata) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 检查缓冲区大小
        if (m_images.size() >= m_max_buffer_size) {
            // 可以选择覆盖最旧的图像或拒绝添加
            auto oldest = m_images.begin();
            m_images.erase(oldest);
            m_metadata.erase(oldest->first);
        }
        
        // 使用元数据中的unique_id作为键
        int64_t id = metadata.unique_id;
        m_images[id] = image.clone(); // 深拷贝图像
        m_metadata[id] = metadata;
        
        // 通知等待的线程有新图像可用
        m_condition.notify_all();
        return true;
    }
    
    // 添加多张图像及其元数据
    bool add_images(const std::vector<cv::Mat>& images, 
                   const std::vector<ImageMetadata>& metadata_list) {
        if (images.size() != metadata_list.size()) {
            return false;
        }
        
        std::lock_guard<std::mutex> lock(m_mutex);
        
        // 清除现有图像，如果需要保留则可以修改此逻辑
        m_images.clear();
        m_metadata.clear();
        
        for (size_t i = 0; i < images.size(); ++i) {
            int64_t id = metadata_list[i].unique_id;
            m_images[id] = images[i].clone();
            m_metadata[id] = metadata_list[i];
        }
        
        m_condition.notify_all();
        return true;
    }
    
    // 获取单张图像及其元数据
    bool get_image(int64_t id, cv::Mat& image, ImageMetadata& metadata) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        auto it_img = m_images.find(id);
        auto it_meta = m_metadata.find(id);
        
        if (it_img != m_images.end() && it_meta != m_metadata.end()) {
            image = it_img->second.clone();
            metadata = it_meta->second;
            return true;
        }
        
        return false;
    }
    
    // 获取所有图像及其元数据
    bool get_all_images(std::vector<cv::Mat>& images, 
                       std::vector<ImageMetadata>& metadata_list) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_images.empty()) {
            return false;
        }
        
        images.clear();
        metadata_list.clear();
        
        for (const auto& pair : m_images) {
            images.push_back(pair.second.clone());
            metadata_list.push_back(m_metadata[pair.first]);
        }
        
        return true;
    }
    
    // 等待新图像（带超时）
    bool wait_for_new_images(std::chrono::milliseconds timeout) {
        std::unique_lock<std::mutex> lock(m_mutex);
        return m_condition.wait_for(lock, timeout, [this]() {
            return !m_images.empty();
        });
    }
    
    // 清除所有图像
    void clear() {
        std::lock_guard<std::mutex> lock(m_mutex);
        m_images.clear();
        m_metadata.clear();
    }
    
    // 获取缓冲区中的图像数量
    size_t size() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_images.size();
    }
    
    // 检查缓冲区是否为空
    bool empty() const {
        std::lock_guard<std::mutex> lock(m_mutex);
        return m_images.empty();
    }
    
    // 根据时间戳获取最新的图像
    bool get_latest_image(cv::Mat& image, ImageMetadata& metadata) {
        std::lock_guard<std::mutex> lock(m_mutex);
        
        if (m_images.empty()) {
            return false;
        }
        
        // 查找具有最大时间戳的图像
        auto latest = std::max_element(m_metadata.begin(), m_metadata.end(),
            [](const auto& a, const auto& b) {
                return a.second.timestamp < b.second.timestamp;
            });
        
        image = m_images[latest->first].clone();
        metadata = latest->second;
        return true;
    }
    
private:
    mutable std::mutex m_mutex;
    std::condition_variable m_condition;
    std::map<int64_t, cv::Mat> m_images;
    std::map<int64_t, ImageMetadata> m_metadata;
    size_t m_max_buffer_size;
    int64_t m_next_id;
};

// 创建共享的图像缓冲区
inline std::shared_ptr<ImageBuffer> create_image_buffer(size_t max_buffer_size = 10) {
    return std::make_shared<ImageBuffer>(max_buffer_size);
}

} // namespace model

#endif // __IMAGE_BUFFER_HPP__
