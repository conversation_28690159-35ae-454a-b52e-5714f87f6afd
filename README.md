# dev_ws_det2d


## Model类成员变量线程安全性说明

### 线程安全的成员变量
1. m_logger (std::shared_ptr<logger::Logger>)
    - 日志记录器通常设计为线程安全的
    - 多个线程可以同时使用同一个logger实例
2. m_runtime (std::shared_ptr<nvinfer1::IRuntime>)
    - TensorRT的Runtime对象是线程安全的
    - 多个线程可以同时调用其方法，如deserializeCudaEngine()
3. m_engine (std::shared_ptr<nvinfer1::ICudaEngine>)
    - 引擎对象本身是线程安全的
    - 多个线程可以同时调用createExecutionContext()等方法
4. m_network (std::shared_ptr<nvinfer1::INetworkDefinition>)
    - 网络定义对象通常在构建阶段使用，不会在推理时被多线程访问

### 线程不安全的成员变量
1. m_context (std::shared_ptr<nvinfer1::IExecutionContext>)
    - 最关键的线程不安全对象
    - 执行上下文不能被多个线程同时访问
    - 多线程同时调用enqueueV2()会导致未定义行为
2. m_stream (cudaStream_t)
    - CUDA流在多线程同时使用时需要同步
    - 多个线程同时向同一个流提交操作可能导致竞争条件
3. m_bindings (float)
    - 绑定数组在多线程环境下可能被同时修改
    - 需要互斥锁保护
4. m_inputMemory 和 m_outputMemory (float)
    - 内存指针在多线程环境下可能被同时访问
    - 需要互斥锁保护

## 多线程架构分析
```mermaid
graph TD
    A[Worker] --> B[ThreadPool]
    A --> C[Classifier]
    A --> D[Detector]
    C --> E[Model]
    D --> E
    E --> F[IExecutionContext]
    E --> G[ICudaEngine]
    E --> H[IRuntime]
    E --> I[cudaStream_t]
    E --> J[Bindings/Memory]
    K[Int8EntropyCalibrator] --> G
    
    %% 多线程关系
    B -->|多线程执行| L((Thread 1))
    B -->|多线程执行| M((Thread 2))
    B -->|多线程执行| N((Thread n))
    
    L -->|访问| F
    M -->|访问| F
    N -->|访问| F
    
    L -->|访问| I
    M -->|访问| I
    N -->|访问| I
    
    %% 线程安全性
    F -->|线程不安全| O{竞争条件}
    I -->|线程不安全| O
    J -->|线程不安全| O
    
    %% 锁保护
    P[m_enqueue_mutex] -->|保护| F
    Q[m_preprocess_mutex] -->|保护| R[预处理]
    S[m_postprocess_mutex] -->|保护| T[后处理]
```

## Model类成员变量关系说明

### IRuntime（运行时）
- **功能**：TensorRT的顶层对象，管理全局资源和CUDA上下文
- **责任**：
  - 反序列化序列化的引擎计划（engine plan）
  - 管理全局插件工厂
  - 创建引擎实例
- **CUDA关系**：内部持有cuBLAS、cuDNN等库的句柄，管理CUDA上下文

### ICudaEngine（引擎）
- **功能**：表示优化后的神经网络计算图
- **责任**：
  - 存储网络结构和优化后的权重
  - 包含输入输出张量的配置信息
  - 管理layer fusion、memory优化等计算图优化结果
- **CUDA关系**：包含CUDA核函数的调用顺序、内存分配策略

### IExecutionContext（执行上下文）
- **功能**：负责实际执行推理
- **责任**：
  - 管理执行推理所需的工作空间
  - 维护输入输出绑定
  - 执行推理计算，调用CUDA核函数
- **CUDA关系**：触发CUDA核函数执行，管理内存传输和CUDA流

### IRuntime和ICudaEngine
- 一个 IRuntime 对象可以反序列化多个 ICudaEngine 实例
- IRuntime 本质上是引擎的工厂类，与生成的引擎是**一对多**关系

### ICudaEngine和IExecutionContext
- 一个引擎（ICudaEngine）可以创建多个 IExecutionContext（支持多线程或多流推理）