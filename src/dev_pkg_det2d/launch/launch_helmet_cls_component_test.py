from launch import LaunchDescription
from launch_ros.actions import Composable<PERSON>ode<PERSON><PERSON><PERSON>
from launch_ros.descriptions import ComposableNode
from launch.actions import DeclareLaunchArgument
from launch.substitutions import LaunchConfiguration

def generate_launch_description():
    # Declare launch arguments
    model_path_arg = DeclareLaunchArgument(
        'model_path',
        default_value='src/dev_pkg_det2d/models/onnx/helmet_cls_batch4.onnx',
        description='Path to the ONNX model file'
    )
    
    image_topics_arg = DeclareLaunchArgument(
        'image_topics',
        default_value='["/camr_device_0_decode", "/camr_device_1_decode", "/camr_device_2_decode", "/camr_device_3_decode"]',
        description='List of input image topics'
    )
    
    vehicle_det_topics_arg = DeclareLaunchArgument(
        'vehicle_det_topics',
        default_value='["/obj_device_0_det2d", "/obj_device_1_det2d", "/obj_device_2_det2d", "/obj_device_3_det2d"]',
        description='List of input vehicle detection topics'
    )
    
    output_topic_arg = DeclareLaunchArgument(
        'output_topic',
        default_value='/obj_device_all_helmet_cls',
        description='Output topic for helmet classification results'
    )
    
    inference_rate_arg = DeclareLaunchArgument(
        'inference_rate',
        default_value='10.0',
        description='Inference rate in Hz'
    )
    
    confidence_threshold_arg = DeclareLaunchArgument(
        'confidence_threshold',
        default_value='0.5',
        description='Confidence threshold for cyclist detections'
    )

    sensor_names_arg = DeclareLaunchArgument(
        'sensor_names',
        default_value='["R39_Aw_CamS", "R39_Bn_CamW", "R39_Ce_CamN", "R39_Ds_CamE"]',
        description='List of sensor names for each camera'
    )

    min_roi_width_arg = DeclareLaunchArgument(
        'min_roi_width',
        default_value='100',
        description='Minimum width for cyclist ROI to be processed'
    )

    min_roi_height_arg = DeclareLaunchArgument(
        'min_roi_height',
        default_value='100',
        description='Minimum height for cyclist ROI to be processed'
    )
    
    # Create the composable node container
    container = ComposableNodeContainer(
        name='helmet_classification_container',
        namespace='',
        package='rclcpp_components',
        executable='component_container',
        composable_node_descriptions=[
            ComposableNode(
                package='dev_pkg_det2d',
                plugin='Det2dWorker::HelmetClsNode',
                name='helmet_cls_node',
                parameters=[{
                    'model_path': LaunchConfiguration('model_path'),
                    'image_topics': LaunchConfiguration('image_topics'),
                    'vehicle_det_topics': LaunchConfiguration('vehicle_det_topics'),
                    'output_topic': LaunchConfiguration('output_topic'),
                    'inference_rate': LaunchConfiguration('inference_rate'),
                    'confidence_threshold': LaunchConfiguration('confidence_threshold'),
                    'max_queue_size': 10,
                    'max_batch_size': 8,
                    'sensor_names': LaunchConfiguration('sensor_names'),
                    'min_roi_width': LaunchConfiguration('min_roi_width'),
                    'min_roi_height': LaunchConfiguration('min_roi_height')
                }],
                extra_arguments=[{'use_intra_process_comms': True}]
            )
        ],
        output='screen',
    )
    
    return LaunchDescription([
        model_path_arg,
        image_topics_arg,
        vehicle_det_topics_arg,
        output_topic_arg,
        inference_rate_arg,
        confidence_threshold_arg,
        sensor_names_arg,
        min_roi_width_arg,
        min_roi_height_arg,
        container
    ])
