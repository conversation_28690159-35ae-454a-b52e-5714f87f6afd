#include <chrono>
#include <functional>
#include <memory>
#include <string>
#include <vector>
#include <map>
#include <fstream>
#include <filesystem>
#include <iomanip>
#include <thread>
#include <unistd.h>  // For getcwd
#include <limits.h>  // For PATH_MAX
#include <algorithm> // For std::replace

// ROS
#include "rclcpp/rclcpp.hpp"
#include "sensor_msgs/msg/image.hpp"

// Image Processing
#include "cv_bridge/cv_bridge.h"
#include "opencv2/opencv.hpp"

// Image Buffer
#include "image_buffer_manager.hpp"

namespace fs = std::filesystem;

class ImgBufferCheckNode : public rclcpp::Node
{
public:
    ImgBufferCheckNode()
        : Node("img_buffer_check_node"),
          data_saved_(false)
    {
        RCLCPP_INFO(this->get_logger(), "Initializing ImgBufferCheckNode...");

        // --- Declare Parameters ---
        this->declare_parameter<std::string>("buffer_name", "cropped_objects");
        this->declare_parameter<std::string>("output_dir", "./tmp/img_buffer_check");
        this->declare_parameter<int>("max_retry_count", 10);
        this->declare_parameter<int>("retry_interval_sec", 2);
        this->declare_parameter<bool>("save_images", true);
        this->declare_parameter<bool>("save_metadata", true);
        this->declare_parameter<int>("max_saved_images", 100);

        // --- Get Parameters ---
        buffer_name_ = this->get_parameter("buffer_name").as_string();
        output_dir_ = this->get_parameter("output_dir").as_string();
        max_retry_count_ = this->get_parameter("max_retry_count").as_int();
        retry_interval_sec_ = this->get_parameter("retry_interval_sec").as_int();
        save_images_ = this->get_parameter("save_images").as_bool();
        save_metadata_ = this->get_parameter("save_metadata").as_bool();
        max_saved_images_ = this->get_parameter("max_saved_images").as_int();

        // --- Parameter Validation ---
        if (buffer_name_.empty()) {
            RCLCPP_FATAL(this->get_logger(), "Parameter 'buffer_name' cannot be empty.");
            rclcpp::shutdown();
            return;
        }

        // --- Setup ---
        manager_ = &ImageBufferManager::getInstance();

        // Create output directory if it doesn't exist
        if (save_images_ || save_metadata_) {
            try {
                // Make sure the path is absolute
                if (output_dir_.empty() || output_dir_[0] != '/') {
                    // If it's a relative path, get the current working directory
                    char cwd[PATH_MAX];
                    if (getcwd(cwd, sizeof(cwd)) != NULL) {
                        // Only prepend if it doesn't start with ./
                        if (output_dir_.substr(0, 2) != "./") {
                            output_dir_ = std::string(cwd) + "/" + output_dir_;
                        }
                    } else {
                        RCLCPP_WARN(this->get_logger(), "Could not get current working directory. Using relative path as is.");
                    }
                }

                RCLCPP_INFO(this->get_logger(), "Using output directory: %s", output_dir_.c_str());

                if (!fs::exists(output_dir_)) {
                    fs::create_directories(output_dir_);
                    RCLCPP_INFO(this->get_logger(), "Created output directory: %s", output_dir_.c_str());
                }

                // Test write permissions by creating a test file
                std::string test_file = output_dir_ + "/test_write_permission.tmp";
                std::ofstream test(test_file);
                if (!test.is_open()) {
                    RCLCPP_ERROR(this->get_logger(), "No write permission in directory: %s", output_dir_.c_str());
                    save_images_ = false;
                    save_metadata_ = false;
                } else {
                    test.close();
                    fs::remove(test_file);
                }
            } catch (const std::exception& e) {
                RCLCPP_ERROR(this->get_logger(), "Failed to create output directory: %s. Error: %s",
                            output_dir_.c_str(), e.what());
                save_images_ = false;
                save_metadata_ = false;
            }
        }

        // --- Initialize Check Timer ---
        check_timer_ = this->create_wall_timer(
            std::chrono::seconds(1),  // Start checking immediately
            std::bind(&ImgBufferCheckNode::check_buffer_callback, this));

        RCLCPP_INFO(this->get_logger(), "ImgBufferCheckNode initialization complete.");
        RCLCPP_INFO(this->get_logger(), "Buffer name: %s", buffer_name_.c_str());
        RCLCPP_INFO(this->get_logger(), "Output directory: %s", output_dir_.c_str());
        RCLCPP_INFO(this->get_logger(), "Save images: %s", save_images_ ? "true" : "false");
        RCLCPP_INFO(this->get_logger(), "Save metadata: %s", save_metadata_ ? "true" : "false");
    }

    ~ImgBufferCheckNode()
    {
        if (manager_ && !buffer_name_.empty()) {
            manager_->cleanupSharedMemory(buffer_name_, false);
        }
    }

private:
    void check_buffer_callback()
    {
        // If we've already saved data, don't do anything
        if (data_saved_) {
            RCLCPP_INFO(this->get_logger(), "Data already saved. Shutting down...");
            rclcpp::shutdown();
            return;
        }

        // Try to connect to the buffer
        static int retry_count = 0;

        // Connect to shared memory if not already connected
        if (!image_buffer_) {
            RCLCPP_INFO(this->get_logger(), "Connecting to shared memory buffer: %s (attempt %d/%d)",
                      buffer_name_.c_str(), retry_count + 1, max_retry_count_);

            if (!manager_->connectToSharedMemory(buffer_name_)) {
                RCLCPP_WARN(this->get_logger(), "Failed to connect to shared memory buffer '%s'.", buffer_name_.c_str());
                retry_count++;

                if (retry_count >= max_retry_count_) {
                    RCLCPP_ERROR(this->get_logger(), "Max retry count reached. Shutting down.");
                    rclcpp::shutdown();
                }
                return;
            }

            image_buffer_ = manager_->getBuffer(buffer_name_);
            if (!image_buffer_) {
                RCLCPP_WARN(this->get_logger(), "Failed to get image buffer '%s'.", buffer_name_.c_str());
                retry_count++;

                if (retry_count >= max_retry_count_) {
                    RCLCPP_ERROR(this->get_logger(), "Max retry count reached. Shutting down.");
                    rclcpp::shutdown();
                }
                return;
            }

            RCLCPP_INFO(this->get_logger(), "Successfully connected to image buffer '%s'", buffer_name_.c_str());
        }

        // Lock the buffer
        manager_->lockBuffer(buffer_name_);

        // Read from shared memory
        if (!manager_->readBufferFromSharedMemory(buffer_name_)) {
            RCLCPP_ERROR(this->get_logger(), "Failed to read buffer from shared memory: %s", buffer_name_.c_str());
            manager_->unlockBuffer(buffer_name_);
            retry_count++;

            if (retry_count >= max_retry_count_) {
                RCLCPP_ERROR(this->get_logger(), "Max retry count reached. Shutting down.");
                rclcpp::shutdown();
            }
            return;
        }

        // Get all images and metadata
        std::vector<cv::Mat> images;
        std::vector<model::ImageMetadata> metadata_list;
        bool get_ok = image_buffer_->get_all_images(images, metadata_list);

        // Unlock the buffer
        manager_->unlockBuffer(buffer_name_);

        if (!get_ok || images.empty()) {
            RCLCPP_WARN(this->get_logger(), "Failed to get images from buffer or buffer is empty: %s", buffer_name_.c_str());
            retry_count++;

            if (retry_count >= max_retry_count_) {
                RCLCPP_ERROR(this->get_logger(), "Max retry count reached. Shutting down.");
                rclcpp::shutdown();
            }
            return;
        }

        // Log buffer information
        RCLCPP_INFO(this->get_logger(), "Buffer '%s' contains %zu images", buffer_name_.c_str(), images.size());

        // Save metadata to text file
        if (save_metadata_ && !images.empty()) {
            save_metadata_to_file(images, metadata_list);
        }

        // Save images to disk
        if (save_images_ && !images.empty()) {
            save_images_to_disk(images, metadata_list);
        }

        // Mark data as saved
        data_saved_ = true;
        RCLCPP_INFO(this->get_logger(), "Data saved successfully. Will shut down in 2 seconds.");

        // Wait a bit before shutting down to allow logs to be printed
        std::this_thread::sleep_for(std::chrono::seconds(2));
        rclcpp::shutdown();
    }

    void save_metadata_to_file(const std::vector<cv::Mat>& images,
                              const std::vector<model::ImageMetadata>& metadata_list)
    {
        // Create a timestamp for the filename
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << output_dir_ << "/buffer_info_" << std::put_time(std::localtime(&now_time_t), "%Y%m%d_%H%M%S") << ".txt";
        std::string filename = ss.str();

        try {
            std::ofstream file(filename);
            if (!file.is_open()) {
                RCLCPP_ERROR(this->get_logger(), "Failed to open file for writing: %s", filename.c_str());
                return;
            }

            // Write header
            file << "Buffer Name: " << buffer_name_ << std::endl;
            file << "Timestamp: " << std::put_time(std::localtime(&now_time_t), "%Y-%m-%d %H:%M:%S") << std::endl;
            file << "Number of Images: " << images.size() << std::endl;
            file << "-------------------------------------------" << std::endl;

            // Write metadata for each image
            for (size_t i = 0; i < metadata_list.size(); ++i) {
                const auto& meta = metadata_list[i];
                const auto& img = images[i];

                file << "Image " << (i+1) << ":" << std::endl;
                file << "  Unique ID: " << meta.unique_id << std::endl;
                file << "  Timestamp: " << meta.timestamp << std::endl;
                file << "  Frame ID: " << meta.frame_id << std::endl;
                file << "  Dimensions: " << img.cols << "x" << img.rows << std::endl;
                file << "  Channels: " << img.channels() << std::endl;
                file << "  Type: " << img.type() << std::endl;
                file << std::endl;
            }

            file.close();
            RCLCPP_INFO(this->get_logger(), "Saved buffer metadata to: %s", filename.c_str());
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error saving metadata: %s", e.what());
        }
    }

    void save_images_to_disk(const std::vector<cv::Mat>& images,
                            const std::vector<model::ImageMetadata>& metadata_list)
    {
        // Create a subdirectory with timestamp
        auto now = std::chrono::system_clock::now();
        auto now_time_t = std::chrono::system_clock::to_time_t(now);
        std::stringstream ss;
        ss << output_dir_ << "/images_" << std::put_time(std::localtime(&now_time_t), "%Y%m%d_%H%M%S");
        std::string dir_path = ss.str();

        try {
            // Create the directory
            RCLCPP_INFO(this->get_logger(), "Creating directory: %s", dir_path.c_str());
            fs::create_directories(dir_path);

            // Test write permissions in the new directory
            std::string test_file = dir_path + "/test_write_permission.tmp";
            std::ofstream test(test_file);
            if (!test.is_open()) {
                RCLCPP_ERROR(this->get_logger(), "No write permission in directory: %s", dir_path.c_str());
                return;
            } else {
                test.close();
                fs::remove(test_file);
            }

            // Save each image
            int count = 0;
            for (size_t i = 0; i < std::min(images.size(), static_cast<size_t>(max_saved_images_)); ++i) {
                const auto& img = images[i];
                const auto& meta = metadata_list[i];

                // Create filename with metadata
                std::stringstream img_ss;
                img_ss << dir_path << "/img_" << std::setw(4) << std::setfill('0') << (i+1);

                // Add unique ID if available
                if (meta.unique_id > 0) {
                    img_ss << "_id_" << meta.unique_id;
                }

                // Add timestamp if available
                if (meta.timestamp > 0) {
                    img_ss << "_ts_" << meta.timestamp;
                }

                // Sanitize frame_id to avoid creating directories
                if (!meta.frame_id.empty()) {
                    std::string safe_frame_id = meta.frame_id;
                    // Replace any slashes, spaces, or special characters with underscores
                    std::replace(safe_frame_id.begin(), safe_frame_id.end(), '/', '_');
                    std::replace(safe_frame_id.begin(), safe_frame_id.end(), ' ', '_');
                    std::replace(safe_frame_id.begin(), safe_frame_id.end(), ':', '_');

                    img_ss << "_" << safe_frame_id;
                }

                img_ss << ".png";
                std::string img_path = img_ss.str();

                // Log the path we're trying to save to
                RCLCPP_INFO(this->get_logger(), "Attempting to save image to: %s", img_path.c_str());

                // Save the image
                if (!cv::imwrite(img_path, img)) {
                    RCLCPP_ERROR(this->get_logger(), "Failed to save image: %s", img_path.c_str());
                    // Try to diagnose the issue
                    if (!fs::exists(dir_path)) {
                        RCLCPP_ERROR(this->get_logger(), "Directory does not exist: %s", dir_path.c_str());
                    }
                } else {
                    count++;
                    RCLCPP_INFO(this->get_logger(), "Successfully saved image: %s", img_path.c_str());
                }
            }

            RCLCPP_INFO(this->get_logger(), "Saved %d images to: %s", count, dir_path.c_str());
        } catch (const std::exception& e) {
            RCLCPP_ERROR(this->get_logger(), "Error saving images: %s", e.what());
        }
    }

    // --- Member Variables ---
    // Parameters
    std::string buffer_name_;
    std::string output_dir_;
    int max_retry_count_;
    int retry_interval_sec_;
    bool save_images_;
    bool save_metadata_;
    int max_saved_images_;
    bool data_saved_;

    // Image Buffer
    ImageBufferManager* manager_;
    std::shared_ptr<model::ImageBuffer> image_buffer_;

    // Timer
    rclcpp::TimerBase::SharedPtr check_timer_;
};

int main(int argc, char * argv[])
{
    rclcpp::init(argc, argv);
    auto node = std::make_shared<ImgBufferCheckNode>();
    rclcpp::spin(node);
    rclcpp::shutdown();
    return 0;
}
